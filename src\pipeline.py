import json
from typing import List, Dict, Any
from datetime import datetime
import random
from logger import log
import config
from src.data_retriever import DataRetriever
from src.llm_extractor import LLMExtractor
import re
# random.seed(42)
class KnowledgeGraphPipeline:
    """
    Orchestrates the end-to-end process of fetching data, extracting insights,
    and saving the raw, structured output.
    """

    def __init__(self):
        """
        Initializes all the necessary components for the pipeline.
        """
        log.info("Initializing Knowledge Graph Pipeline...")
        self.data_retriever = DataRetriever(
            qdrant_url=config.QDRANT_URL,
            collection_name=config.QDRANT_COLLECTION_NAME,
            query_file_path=config.SEARCH_QUERIES_PATH
        )
        self.llm_extractor = LLMExtractor(
            api_key=config.OPENAI_API_KEY,
        )
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        output_dir = config.CLEAN_RELATIONSHIPS_PATH.parent
        original_stem = config.CLEAN_RELATIONSHIPS_PATH.stem #'clean_relationships'
        original_suffix = config.CLEAN_RELATIONSHIPS_PATH.suffix #jsonl
        new_filename = f"{original_stem}_{timestamp}{original_suffix}"
        self.output_path = output_dir / new_filename
        log.info("Pipeline initialized successfully.")

    def _save_output(self, data: Dict[str, Any]):
        """
        Appends a single JSON object to the output JSONL file.
        
        Args:
            data (Dict[str, Any]): The dictionary to save (e.g., the LLM output).
        """
        try:
            with open(self.output_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(data, ensure_ascii=False) + "\n")
        except IOError as e:
            log.error(f"Failed to write to output file {self.output_path}. Error: {e}")

    def run(self, companies: List[str]):
        """
        Executes the full pipeline for a list of companies, processing ONE chunk at a time and enriching the LLM output with source metadata.
        """
        log.info(f"--- Starting Pipeline Run for {len(companies)} companies (Single Chunk Mode) ---")

        total_entities = 0
        total_relationships = 0

        for company_name in companies:
            log.info(f"--- Processing company: {company_name} ---")
            
            all_chunks = self.data_retriever.fetch_relevant_chunks(
                company_name=company_name,
                search_limit=config.QDRANT_SEARCH_LIMIT
            )
            if not all_chunks:
                log.warning(f"No relevant chunks found for {company_name}. Skipping.")
                continue
            # chunks to process
            # random_chunks = random.sample(all_chunks, 3)
            safe_company_name = re.sub(r'[\\/*?:"<>|,]', "_", company_name)
            chunks_save_path = config.OUTPUT_DIR / f"{safe_company_name}_chunks.json"
            with open(chunks_save_path, 'w', encoding='utf-8') as f:
                json.dump([chunk.__dict__ for chunk in all_chunks], f, ensure_ascii=False, indent=2)
            for i, chunk in enumerate(all_chunks):
                log.info(f"Processing chunk {i+1} of {len(all_chunks)} for {company_name} (ID: {chunk.id})")

                chunks_to_process = [chunk]
                llm_output = self.llm_extractor.extract_relationships(chunks_to_process)
                if llm_output["entities"] or llm_output["relationships"]:
                    # Metadata enrichment logic
                    chunk_metadata = chunk.payload.get("metadata", {})
                    source_metadata = {
                        "report_type": chunk_metadata.get("form_type", "N/A"),
                        "period_of_report": chunk_metadata.get("period_of_report", "N/A"),
                        "section": chunk_metadata.get("section", "N/A"),
                        "source_url": chunk_metadata.get("source", "N/A"),
                    }
                    # loop through each extracted relationship and inject metadata.
                    enriched_relationships = []
                    for relationship in llm_output["relationships"]:
                        relationship["source"] = source_metadata
                        enriched_relationships.append(relationship)

                    # remove entities that does not belong in relationship
                    valid_entity_names = {rel["source_entity"] for rel in enriched_relationships} | {rel["target_entity"] for rel in enriched_relationships}
                    llm_output["entities"] = [ent for ent in llm_output["entities"] if ent["name"] in valid_entity_names]

                    output_record = {
                        "company_name": company_name,
                        "source_chunk_id": chunk.id,
                        "entities": llm_output["entities"],
                        "relationships": enriched_relationships
                    }
                    self._save_output(output_record)
                    total_entities += len(llm_output["entities"])
                    total_relationships += len(enriched_relationships)
        log.info(f"--- Pipeline Run Finished ---")
        log.info(f"Total entities extracted: {total_entities}")
        log.info(f"Total relationships extracted: {total_relationships}")
            