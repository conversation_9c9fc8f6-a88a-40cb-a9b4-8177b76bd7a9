2025-08-17 21:32:52,267 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_21-32-52.log
2025-08-17 21:33:02,616 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:177] - --- Starting UMAP -> HDBSCAN Clustering Analysis ---
2025-08-17 21:33:02,617 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:23] - Loading relationship phrases from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships.jsonl
2025-08-17 21:33:02,619 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:35] - Found 8 unique relationship phrases.
2025-08-17 21:33:02,620 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 21:33:05,235 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 21:33:05,235 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 21:33:05,235 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 21:33:20,400 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 21:33:20,401 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 21:33:20,401 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:50] - Vectorizing 8 phrases using the BGE model...
2025-08-17 21:33:24,636 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:52] - Vectorization complete. Embedding matrix shape: (8, 768)
2025-08-17 21:33:24,636 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:71] - Starting dimensionality reduction with UMAP from 768 to 10 dimensions...
