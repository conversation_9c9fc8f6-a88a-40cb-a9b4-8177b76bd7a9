import json
from typing import Dict, Any, List
from thefuzz import fuzz

from logger import log
import config

class CompanyNormalizer:
    """
    Handles the normalization of Company entities by matching them against a master
    SEC ticker list and a learned canonical map.
    """

    def __init__(self, sec_ticker_path: str, company_map_path: str):
        """
        Initializes the CompanyNormalizer.
        """
        log.info("Initializing CompanyNormalizer with new list-based map strategy...")
        self.company_map_path = company_map_path
        
        # --- In-memory data structures for high performance ---
        # self.company_map: Main lookup dict { Ticker -> Company Object }
        # self._alias_to_ticker_map: Reverse index { Alias -> Ticker }
        self.company_map: Dict[str, Dict[str, Any]] = {}
        self._alias_to_ticker_map: Dict[str, str] = {}
        
        # --- Load and process external data ---
        sec_master_list = self._load_sec_ticker_list(sec_ticker_path)
        # Create a fast lookup for the SEC list, keyed by ticker
        self._sec_ticker_lookup = {
            entry.get("ticker", "").upper(): entry for entry in sec_master_list if entry.get("ticker")
        }
        
        # Load the list-based map and build our in-memory lookups
        self._load_and_build_maps()

        self.threshold = config.FUZZY_MATCH_THRESHOLD
        log.info(f"CompanyNormalizer initialized with {len(self.company_map)} canonical companies.")

    def _load_sec_ticker_list(self, file_path: str) -> List[Dict[str, Any]]:
        """Loads the master SEC ticker list from its JSON file."""
        log.info(f"Loading SEC master ticker list from: {file_path}")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return list(data.values())
        except (FileNotFoundError, json.JSONDecodeError) as e:
            log.error(f"FATAL: Failed to load SEC ticker list from {file_path}. Error: {e}")
            raise

    def _load_and_build_maps(self):
        """
        Loads the list-based canonical map and builds the in-memory lookup dictionaries.
        """
        try:
            with open(self.company_map_path, 'r', encoding='utf-8') as f:
                log.info(f"Loading company canonical map from: {self.company_map_path}")
                content = f.read()
                if not content.strip():
                    log.warning(f"Company map file '{self.company_map_path.name}' is empty. Starting fresh.")
                    return
                
                # Load the list of company objects
                list_of_companies = json.loads(content)
                
                # Build the in-memory lookup maps from this list
                for company_obj in list_of_companies:
                    ticker = company_obj.get("ticker_symbol") # Should we do Upper here?
                    if ticker:
                        self.company_map[ticker] = company_obj
                        for alias in company_obj.get("aliases", []):
                            self._alias_to_ticker_map[alias.upper()] = ticker

        except FileNotFoundError:
            log.warning(f"Company map file not found at {self.company_map_path}. Starting fresh.")
        except json.JSONDecodeError:
            log.error(f"FATAL: Could not decode JSON from {self.company_map_path}. File may be corrupt.")
            raise

    def save_map(self):
        """
        Saves the in-memory map back to the list-based JSON file.
        """
        log.info(f"Saving updated company canonical map to: {self.company_map_path}")
        
        # Convert the in-memory dictionary back to a sorted list for saving
        list_to_save = sorted(self.company_map.values(), key=lambda x: x['ticker_symbol'])
        
        if not list_to_save:
            log.warning("In-memory company map is empty. Nothing to save.")
            return

        try:
            with open(self.company_map_path, 'w', encoding='utf-8') as f:
                json.dump(list_to_save, f, indent=2, ensure_ascii=False)
            log.info(f"Successfully saved {len(list_to_save)} companies to the map.")
        except IOError as e:
            log.error(f"Failed to write company map file. Error: {e}")

    def _calculate_weighted_score(self, str1: str, str2: str) -> float:
        """
        Calculates a weighted average score from multiple fuzzy matching ratios to provide similarity score.
        Args:
            str1 (str): The first string to compare.
            str2 (str): The second string to compare.

        Returns:
            float: The weighted average similarity score.(0-100)
        """
        score_token_set = fuzz.token_set_ratio(str1, str2)
        score_partial = fuzz.partial_ratio(str1, str2)
        score_ratio = fuzz.ratio(str1, str2)

        weighted_score = (
            (score_token_set * 0.5) +
            (score_partial * 0.3) +
            (score_ratio * 0.2)
        )

        return weighted_score

    def _find_match_in_local_map(self, name: str) -> str | None:
        """
        Searches for a name in the local map using the optimized reverse index
        for exact matches, followed by a fuzzy search.
        """
        cleaned_name = name.upper().strip()

        # Step 1: fast exact match using the reverse index
        ticker = self._alias_to_ticker_map.get(cleaned_name)
        if ticker:
            log.debug(f"Exact alias match for '{name}' found in local map: {ticker}")
            return ticker

        # Step 2: Fuzzy match if no exact match was found
        best_match_ticker = None
        highest_score = 0
        for ticker, details in self.company_map.items():
            for alias in details["aliases"]:
                score = self._calculate_weighted_score(cleaned_name, alias)
                if score > highest_score:
                    highest_score = score
                    best_match_ticker = ticker
        
        if highest_score >= self.threshold:
            log.info(f"Fuzzy match for '{name}' found in local map: '{best_match_ticker}' (Score: {highest_score:.2f})")
            return best_match_ticker
            
        return None

    def _find_match_in_sec_list(self, name: str) -> Dict[str, Any] | None:
        """
        Searches for a name in the SEC master list using a two-stage process:
        1. Exact ticker match.
        2. Fuzzy title match.
        """
        cleaned_name = name.upper().strip()
        
        # Step 1: Exact Ticker Match (Highest Confidence)
        if cleaned_name in self._sec_ticker_lookup:
            log.info(f"Exact ticker match for '{name}' found in SEC master list.")
            return self._sec_ticker_lookup[cleaned_name]

        # Step 2: Fuzzy Title Match (If no ticker match)
        best_match_entry = None
        highest_score = 0
        for entry in self._sec_ticker_lookup.values():
            title = entry.get("title", "").upper()
            score = self._calculate_weighted_score(cleaned_name, title)
            if score > highest_score:
                highest_score = score
                best_match_entry = entry

        if highest_score >= self.threshold:
            log.info(f"Fuzzy title match for '{name}' found in SEC master list: '{best_match_entry.get('title')}' (Score: {highest_score:.2f})")
            return best_match_entry
            
        return None
    
    def get_company_details(self, ticker: str) -> Dict[str, Any] | None:
        """
        Retrieves the full canonical details for a company using its ticker.

        Args:
            ticker (str): The stock ticker symbol (our canonical ID).

        Returns:
            Dict[str, Any] | None: The company object from our map, or None if not found.
        """
        return self.company_map.get(ticker)

    def normalize_company(self, entity_name: str) -> str:
        """
        Normalizes a single company entity to its canonical ID (stock ticker).
        """
        # Step 1: Search our existing, learned map
        ticker = self._find_match_in_local_map(entity_name)
        if ticker:
            # If we found a match, learn the new name as an alias
            cleaned_name = entity_name.upper().strip()
            if cleaned_name not in self.company_map[ticker]["aliases"]:
                self.company_map[ticker]["aliases"].append(cleaned_name)
                self._alias_to_ticker_map[cleaned_name] = ticker # Update reverse index
            return ticker

        # Step 2: If no local match, search the master SEC list
        sec_entry = self._find_match_in_sec_list(entity_name)
        if sec_entry:
            sec_ticker = sec_entry.get("ticker")
            if not sec_ticker:
                log.warning(f"Found SEC entry for '{entity_name}' but it has no ticker. Treating as unresolved.")
            else:
                sec_ticker_upper = sec_ticker.upper()
                sec_title_upper = sec_entry.get("title", "").upper()
                
                log.info(f"Creating new entry in company map for '{sec_ticker_upper}'")
                
                # Create the new company object
                new_company_obj = {
                    "company_name": sec_title_upper,
                    "ticker_symbol": sec_ticker_upper,
                    "aliases": sorted(list({entity_name.upper().strip(), sec_ticker_upper, sec_title_upper}))
                }
                
                # Add it to our in-memory maps
                self.company_map[sec_ticker_upper] = new_company_obj
                for alias in new_company_obj["aliases"]:
                    self._alias_to_ticker_map[alias] = sec_ticker_upper
                
                return sec_ticker_upper

        # Step 3: If no match anywhere, return an unresolved ID
        log.warning(f"Could not resolve company '{entity_name}'. No match in local map or SEC list.")
        unresolved_id = "UNRESOLVED_COMPANY_" + entity_name.upper().replace(" ", "_").replace(".", "")
        return unresolved_id
    
    # this script is updating the maps in memory, it is not calling save map methods anywhere, that will be called after processing each entry in jsonl in the entity_resolution.py