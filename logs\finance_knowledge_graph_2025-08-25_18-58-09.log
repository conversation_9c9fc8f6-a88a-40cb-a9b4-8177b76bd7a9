2025-08-25 18:58:09,444 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-25_18-58-09.log
2025-08-25 18:58:11,887 - Finance_Knowledge_Graph - INFO - [ingestion.py:214] - ========================================================
2025-08-25 18:58:11,887 - Finance_Knowledge_Graph - INFO - [ingestion.py:215] - = Starting Neo4j Ingestion Pipeline                    =
2025-08-25 18:58:11,887 - Finance_Knowledge_Graph - INFO - [ingestion.py:216] - ========================================================
2025-08-25 18:58:11,895 - Finance_Knowledge_Graph - INFO - [ingestion.py:224] - Selected latest graph file for ingestion: cleaned_llm_output_2025-08-24_16-57-42.json
2025-08-25 18:58:11,895 - Finance_Knowledge_Graph - INFO - [ingestion.py:23] - Connecting to Neo4j database at neo4j+s://aab151a2.databases.neo4j.io...
2025-08-25 18:58:13,502 - Finance_Knowledge_Graph - INFO - [ingestion.py:27] - Neo4j connection successful.
2025-08-25 18:58:13,502 - Finance_Knowledge_Graph - INFO - [ingestion.py:53] - Setting up database constraints...
2025-08-25 18:58:13,555 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GeographicRegion' is set.
2025-08-25 18:58:13,613 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GovernmentAgency' is set.
2025-08-25 18:58:13,664 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'StockExchange' is set.
2025-08-25 18:58:13,728 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Technology' is set.
2025-08-25 18:58:13,788 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Company' is set.
2025-08-25 18:58:13,847 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'IndustrySector' is set.
2025-08-25 18:58:13,910 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Product' is set.
2025-08-25 18:58:13,987 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'BusinessSegment' is set.
2025-08-25 18:58:14,073 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'LawOrRegulation' is set.
2025-08-25 18:58:14,130 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Person' is set.
2025-08-25 18:58:14,130 - Finance_Knowledge_Graph - INFO - [ingestion.py:193] - --- Starting Neo4j Ingestion Process ---
2025-08-25 18:58:14,134 - Finance_Knowledge_Graph - INFO - [ingestion.py:74] - Ingesting 9 nodes in batches...
2025-08-25 18:58:14,332 - Finance_Knowledge_Graph - INFO - [ingestion.py:94] - Ingested batch of 9 nodes.
2025-08-25 18:58:14,332 - Finance_Knowledge_Graph - INFO - [ingestion.py:96] - Node ingestion complete.
2025-08-25 18:58:14,332 - Finance_Knowledge_Graph - ERROR - [ingestion.py:237] - The ingestion pipeline failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\ingestion.py", line 234, in main
    ingestor.run(latest_graph_file)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\ingestion.py", line 206, in run
    self.ingest_relationships(relationships)
TypeError: Neo4jIngestor.ingest_relationships() missing 1 required positional argument: 'all_nodes'
2025-08-25 18:58:14,332 - Finance_Knowledge_Graph - INFO - [ingestion.py:38] - Closing Neo4j connection.
2025-08-25 18:58:14,332 - Finance_Knowledge_Graph - INFO - [ingestion.py:242] - Ingestion pipeline completed.
