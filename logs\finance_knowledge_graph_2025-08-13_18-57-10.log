2025-08-13 18:57:10,889 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-13_18-57-10.log
2025-08-13 18:57:33,360 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-13 18:57:33,362 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-13 18:57:33,362 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-13 18:57:33,363 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-13 18:57:33,364 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-13 18:57:35,209 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-13 18:57:35,213 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-13 18:57:35,213 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-13 18:57:54,029 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-13 18:57:54,030 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-13 18:57:54,030 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-13 18:57:54,772 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-13 18:57:54,773 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:57:54,774 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-13 18:57:54,774 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-13 18:57:54,776 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-13 18:57:54,776 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-13 18:57:54,777 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-13 18:58:46,303 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-13 18:58:46,305 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-13 18:58:51,269 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:91] - Successfully extracted 3 relationships from the provided context.
2025-08-13 18:58:51,270 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 3 raw relationships for INTEL CORP.
2025-08-13 18:58:51,270 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Intel Products' of type 'Product' with ID 'product_intel_products'.
2025-08-13 18:58:51,270 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:58:51,272 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'DCAI' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:58:51,274 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'NEX' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:58:51,276 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-13 18:58:51,276 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 3
2025-08-13 18:58:51,277 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-13 18:58:51,278 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:58:51,280 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-13 18:58:51,281 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-13 18:58:51,282 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-13 18:58:51,282 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
