2025-08-18 16:40:49,491 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-18_16-40-49.log
2025-08-18 16:41:01,985 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-18 16:41:01,985 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-18 16:41:01,985 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-18 16:41:01,991 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-18 16:41:01,991 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-18 16:41:03,791 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-18 16:41:03,791 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-18 16:41:03,791 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-18 16:41:25,355 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-18 16:41:25,355 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-18 16:41:25,356 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-18 16:41:26,203 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-18 16:41:26,203 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 5 companies (Single Chunk Mode) ---
2025-08-18 16:41:26,203 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: NVIDIA CORP ---
2025-08-18 16:41:26,203 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-18 16:42:41,486 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-18 16:42:41,492 - Finance_Knowledge_Graph - INFO - [pipeline.py:72] - Processing chunk 1 of 46 for NVIDIA CORP (ID: dbe4fae5-62f2-4182-83f8-65f73161e70f)
2025-08-18 16:42:41,499 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6474
2025-08-18 16:44:03,624 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-18 16:44:03,624 - Finance_Knowledge_Graph - INFO - [pipeline.py:72] - Processing chunk 2 of 46 for NVIDIA CORP (ID: 2f395978-0140-42ea-a3ae-624aef280fdb)
2025-08-18 16:44:03,624 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3891
2025-08-18 16:45:24,320 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-18 16:45:24,320 - Finance_Knowledge_Graph - INFO - [pipeline.py:72] - Processing chunk 3 of 46 for NVIDIA CORP (ID: a0626742-c13b-493b-93d4-0bcd36c69416)
2025-08-18 16:45:24,321 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6464
