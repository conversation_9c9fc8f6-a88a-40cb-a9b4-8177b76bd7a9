2025-08-28 10:39:25,704 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-28_10-39-25.log
2025-08-28 10:39:41,375 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-28 10:39:41,375 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-28 10:39:41,375 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-28 10:39:41,375 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-28 10:39:41,375 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-28 10:39:43,314 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-28 10:39:43,315 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-28 10:39:43,315 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-28 10:40:07,330 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-28 10:40:07,330 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-28 10:40:07,330 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-28 10:40:08,073 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-28 10:40:08,073 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-28 10:40:08,076 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: QUALCOMM INC/DE ---
2025-08-28 10:40:08,076 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'QUALCOMM INC/DE'
2025-08-28 10:41:04,018 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 52 unique chunks for 'QUALCOMM INC/DE'.
2025-08-28 10:41:04,018 - Finance_Knowledge_Graph - ERROR - [main.py:28] - A fatal error occurred in the main pipeline execution.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\main.py", line 25, in main
    pipeline.run(companies=companies)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\pipeline.py", line 72, in run
    with open(chunks_save_path, 'w', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Autowiz\\Fin_knowledge_graph\\new_approach_11_08\\data\\output\\QUALCOMM INC\\DE_chunks.json'
2025-08-28 10:41:04,033 - Finance_Knowledge_Graph - INFO - [main.py:30] - =====================================================
2025-08-28 10:41:04,033 - Finance_Knowledge_Graph - INFO - [main.py:31] -       PIPELINE EXECUTION FINISHED                 
2025-08-28 10:41:04,033 - Finance_Knowledge_Graph - INFO - [main.py:32] - =====================================================
