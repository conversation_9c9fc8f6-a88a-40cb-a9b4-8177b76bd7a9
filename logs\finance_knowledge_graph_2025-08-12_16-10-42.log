2025-08-12 16:10:42,277 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-10-42.log
2025-08-12 16:10:42,281 - Finance_Knowledge_Graph - INFO - [normalization_service.py:119] - --- Running NormalizationService Test ---
2025-08-12 16:10:42,281 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 16:10:42,281 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:10:42,286 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 16:10:42,289 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'GeForce RTX 5090' of type 'Product' with ID 'product_geforce_rtx_5090'.
2025-08-12 16:10:42,294 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'RTX 5090' (Score: 100.0). Matched to 'product_geforce_rtx_5090'. Adding as new alias.
2025-08-12 16:10:42,295 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'IEEE' has type 'Other'. Skipping normalization and logging for review.
