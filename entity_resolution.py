import sys
import json
from pathlib import Path
from typing import Dict, Any, <PERSON>, Tuple
from datetime import datetime

# --- Setup Paths and Logger ---
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from logger import log
import config
from src.other_norm import run_normalization_pass

# --- Main Resolution and Cleaning Class ---

class EntityResolutionPipeline:
    """
    Orchestrates a two-pass process:
    1. "Learning": Updates all canonical maps based on the entire dataset.
    2. "Cleaning": Uses the updated maps to clean the raw LLM output, replacing
       raw names with canonical names and saving a new .json file.
    """

    def __init__(self, sec_ticker_path: Path, company_map_path: Path, others_alias_path: Path, company_review_path: Path, input_data_path: Path):
        """Initializes the pipeline with all necessary file paths."""
        log.info("Initializing Entity Resolution Pipeline...")
        self.sec_ticker_path = sec_ticker_path
        self.company_map_path = company_map_path
        self.others_alias_path = others_alias_path
        self.company_review_path = company_review_path
        self.input_data_path = input_data_path
        self.unique_entities, self.all_relationships = self._collect_data_from_file(self.input_data_path)
        log.info("Entity Resolution Pipeline initialized.")

    def _collect_data_from_file(self, input_path: Path) -> Tuple[List[Dict], List[Dict]]:
        """
        Reads the entire .jsonl file once to do two jobs:
        1. Collect a list of all unique entity objects.
        2. Collect a flat list of all relationship objects.

        Returns:
            A tuple containing: (unique_entity_list, all_relationships_list)
        """
        log.info(f"Collecting all unique entities and all relationships from {input_path.name}...")
        unique_entities: Dict[str, Dict] = {}
        all_relationships: List[Dict] = []
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line)
                    all_relationships.extend(data.get("relationships", []))
                    # Collect unique entities from this line
                    for entity in data.get("entities", []):
                        entity_name = entity.get("name")
                        if entity_name and entity_name not in unique_entities:
                            unique_entities[entity_name] = entity
            entity_list = list(unique_entities.values())
            log.info(f"Collected {len(entity_list)} unique entities and loaded {len(all_relationships)} relationships.")
            return entity_list, all_relationships   
        except FileNotFoundError:
            log.error(f"Input file not found: {input_path}. Cannot proceed.")
            return [], []

    def run_learning_pass(self):
        """
        Pass 1: Runs the normalization process for all entity types to update the
        canonical maps on disk and create the company review file.
        """
        log.info("--- Starting Pass 1: The Learning Pass ---")
        try:
            run_normalization_pass(
                entities=self.unique_entities,
                alias_path_others=self.others_alias_path,
                company_map_path=self.company_map_path,
                sec_ticker_path=self.sec_ticker_path,
                company_review_output_path=self.company_review_path
            )
            log.info("--- Learning Pass Complete. All canonical maps have been updated on disk. ---")
        except Exception as e:
            log.error("The Learning Pass failed with a critical error.", exc_info=True)
            raise 

    def run_cleaning_pass(self):
        """
        Pass 2: Uses the updated maps to clean the raw .jsonl file, replacing
        raw names with their resolved canonical names.
        """
        log.info("--- Starting Pass 2: The Cleaning Pass ---")
        
        # --- Step 1: Load the FINALIZED resolution maps into memory ---
        log.info("Loading finalized resolution maps for cleaning...")

        try:
            with open(self.company_map_path, 'r', encoding='utf-8') as f:
                company_resolution_map = json.load(f)

            with open(self.others_alias_path, 'r', encoding='utf-8') as f:
                other_resolution_map = json.load(f)

        except FileNotFoundError as e:
            log.error(f"A required map file was not found: {e}. Cannot proceed with cleaning.")
            return
        # make dictionary of entities from entities_to_process with canonical names
        entity_dict = {}
        for entity in self.unique_entities:
            if entity.get("type") == "Company":
                # check exact match with all the alias of the company
                name = entity.get("name")
                for obj in company_resolution_map:
                    if name.upper() in obj.get("aliases", []):
                        entity_dict[name] = [obj.get("company_name", name), obj.get("ticker_symbol")]  
                        break
                else:
                    entity_dict[name] = [f"UNRESOLVED_{name}",f"UNRESOLVED_{name}"]

            else:
                type = entity.get("type")
                name = entity.get("name")
                search_in = other_resolution_map.get(type, [])
                for obj in search_in:
                    if name in obj.get("aliases", []):
                        entity_dict[name] = [obj.get("canonical_name", name), obj.get("id")]
                        break
        return entity_dict
    
    def generate_graph_json(self, entity_dict:Dict, unresolved_entities: List[Dict], all_relationships: List[Dict]) -> Dict:
        """
        Generates the final, graph-ready JSON structure from the cleaned and
        resolved data.
        Args:
            entity_dict (Dict): A dictionary mapping raw entity names to their resolved names.
            resolved_entities (List[Dict]): The original unique list of entities.
            all_relationships (List[Dict]): The original flat list of all relationships

        Returns:
            Dict: The final graph-ready JSON structure.

        """
        final_nodes: Dict[str, Dict] = {}
        final_relationships: List[Dict] = []

        # step 1 Create the final unique list of nodes
        for entity in unresolved_entities:
            raw_name = entity.get("name")
            entity_type = entity.get("type")
            # look up the resoluved details in entity_dict
            resolved_details = entity_dict.get(raw_name)
            if not resolved_details:
                log.warning(f"Could not find resolved details for '{raw_name}. Skipping node creation")
                continue
            canonical_name, canonical_id = resolved_details

            # Add the node to our list if it's the first time we've seen this id
            if canonical_id not in final_nodes:
                node_properties = {"name": canonical_name}
                if entity_type == "Company":
                    node_properties["id"] = canonical_id
                else:
                    node_properties["id"] = canonical_id
                    
                final_nodes[canonical_id] = {
                    "id": canonical_id,
                    "labels": [entity_type],
                    "properties": node_properties
                }

        # step 2 Create the final list of relationships
        for rel in all_relationships:
            raw_source = rel.get("source_entity")
            raw_target = rel.get("target_entity")

            # look up the resolved ids for the source and target
            source_details = entity_dict.get(raw_source)
            target_details = entity_dict.get(raw_target)

            # only create a relationship if both source and target were successfully resolved
            if source_details and target_details:
                source_id = source_details[1]
                target_id = target_details[1]

                final_relationships.append({
                    "source_id": source_id,
                    "target_id": target_id,
                    "type": rel.get("relationship_type"),
                    "properties": {
                        "source": rel.get("source"),
                        "evidence": rel.get("evidence"),
                        "rationale": rel.get("rationale"),
                        "strength_score": rel.get("strength_score")
                    }
                })

        return {
            "nodes": list(final_nodes.values()),
            "relationships": final_relationships
        }


# --- Main Execution Block ---
def main():
    """Main function to run the full two-pass entity resolution and cleaning pipeline."""
    log.info("========================================================")
    log.info("= Starting Unified Entity Resolution & Cleaning Pipeline =")
    log.info("========================================================")
    
    # Find the latest raw LLM output file
    # raw_output_files = sorted(config.OUTPUT_DIR.glob("*.jsonl"), reverse=True)
    # if not raw_output_files:
    #     log.error(f"No raw .jsonl files found in {config.OUTPUT_DIR}. Please run main.py first.")
    #     return
    # latest_raw_file = raw_output_files[0]
    latest_raw_file = config.OUTPUT_DIR / "data.jsonl"
    log.info(f"Selected latest raw file for processing: {latest_raw_file.name}")
    
    # Define all necessary file paths
    sec_tickers_path = project_root / "tickers.json"
    company_map_path = config.DATA_DIR / "company_canonical_map.json"
    others_alias_path = config.DATA_DIR / "alias.json"
    company_review_path = config.OUTPUT_DIR / "review_company_nodes.json"
    
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    final_cleaned_path = config.OUTPUT_DIR / f"cleaned_llm_output_{timestamp}.json"
    
    try:
        # Initialize the main pipeline class
        pipeline = EntityResolutionPipeline(
            sec_tickers_path, company_map_path, others_alias_path, company_review_path, latest_raw_file
        )
        
        # Pass 1: the learning pass
        pipeline.run_learning_pass()

        # Pass 2: The cleaning and Generation Pass
        entity_translation_dict = pipeline.run_cleaning_pass()
        if entity_translation_dict:
            log.info("---Starting Final Graph Generation---")

            graph_ready_json = pipeline.generate_graph_json(
                entity_dict=entity_translation_dict,
                unresolved_entities=pipeline.unique_entities,
                all_relationships=pipeline.all_relationships
            )
            with open(final_cleaned_path, 'w', encoding='utf-8') as f:
                json.dump(graph_ready_json, f, ensure_ascii=False, indent=2)
                log.info(f"Final graph-ready JSON saved to {final_cleaned_path}")
    except Exception as e:
        log.error("The entity resolution pipeline failed with a critical error.", exc_info=True)
    else:
        log.info("Entity resolution and cleaning pipeline completed successfully.")

if __name__ == "__main__":
    main()