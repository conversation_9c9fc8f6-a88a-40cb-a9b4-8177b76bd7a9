2025-08-12 16:34:15,535 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-34-15.log
2025-08-12 16:34:25,518 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-12 16:34:25,518 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-12 16:34:25,518 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-12 16:34:25,523 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-12 16:34:25,524 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 16:34:29,963 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-12 16:34:29,967 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 16:34:29,967 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 16:34:42,885 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-12 16:34:42,885 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-12 16:34:42,885 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
