2025-08-14 13:44:09,211 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_13-44-09.log
2025-08-14 13:44:09,211 - Finance_Knowledge_Graph - INFO - [entity_processor.py:88] - --- Running EntityProcessor Test ---
2025-08-14 13:44:09,211 - Finance_Knowledge_Graph - INFO - [entity_processor.py:21] - Initializing EntityProcessor...
2025-08-14 13:44:09,284 - Finance_Knowledge_Graph - INFO - [entity_processor.py:24] - EntityProcessor initialized with 86557 terms from lexicon.
2025-08-14 13:44:09,749 - Finance_Knowledge_Graph - INFO - [entity_processor.py:72] - Snapped raw entity 'our entire lineup of next-generation graphics processing units' to lexicon term 'graphics' (Score: 90).
2025-08-14 13:44:10,063 - Finance_Knowledge_Graph - INFO - [entity_processor.py:72] - Snapped raw entity 'the company's primary data center operations' to lexicon term 'data center' (Score: 90).
2025-08-14 13:44:10,528 - Finance_Knowledge_Graph - INFO - [entity_processor.py:72] - Snapped raw entity 'machine learning algorithms' to lexicon term 'mach' (Score: 90).
2025-08-14 13:44:10,809 - Finance_Knowledge_Graph - INFO - [entity_processor.py:72] - Snapped raw entity 'a new credit facility' to lexicon term 'edit' (Score: 90).
