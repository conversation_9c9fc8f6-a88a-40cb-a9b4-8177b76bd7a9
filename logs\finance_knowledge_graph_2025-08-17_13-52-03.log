2025-08-17 13:52:03,573 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-52-03.log
2025-08-17 13:52:15,495 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:52:15,497 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:52:15,498 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:52:15,501 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:52:15,502 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:52:17,573 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:52:17,574 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:52:17,575 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:52:37,679 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:52:37,680 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:52:37,681 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:52:38,721 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:52:38,723 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 13:52:38,723 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: NVIDIA CORP ---
2025-08-17 13:52:38,723 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-17 13:53:22,768 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-17 13:53:22,772 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 46 for NVIDIA CORP (ID: a3a20ae7-aa9d-4cfd-9a91-a36d6e5c7562)
2025-08-17 13:53:22,774 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3582
2025-08-17 13:54:02,130 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 13:54:02,131 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 2 of 46 for NVIDIA CORP (ID: de0f523e-f6ac-4a0b-8469-5839492b46e1)
2025-08-17 13:54:02,131 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3601
