2025-08-12 16:32:37,804 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-32-37.log
2025-08-12 16:32:50,742 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-12 16:32:50,742 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-12 16:32:50,742 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-12 16:32:50,742 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-12 16:32:50,742 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 16:32:52,531 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-12 16:32:52,533 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 16:32:52,537 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 16:33:14,801 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 16:33:14,801 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 16:33:14,801 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 11 companies ---
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: N ---
2025-08-12 16:33:15,518 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'N'
