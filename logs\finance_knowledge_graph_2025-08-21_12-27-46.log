2025-08-21 12:27:46,997 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-21_12-27-46.log
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:186] - ========================================================
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:187] - = Starting Entity Resolution Pipeline                  =
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:188] - ========================================================
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:198] - Selected latest raw file for processing: sample_relationship.jsonl
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:40] - Initializing Entity Resolution Pipeline...
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:18] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-21 12:27:47,210 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:42] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-21 12:27:47,243 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:57] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-21 12:27:47,243 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:60] - Company map file 'company_canonical_map.json' is empty. Starting fresh.
2025-08-21 12:27:47,243 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - CompanyNormalizer initialized with 0 canonical companies.
2025-08-21 12:27:47,243 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:49] - Entity Resolution Pipeline initialized.
2025-08-21 12:27:47,243 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:74] - Starting processing for input file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\sample_relationship.jsonl
2025-08-21 12:27:47,448 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'NVIDIA Corporation' found in SEC master list: 'NVIDIA CORP' (Score: 83.20)
2025-08-21 12:27:47,448 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'NVDA'
2025-08-21 12:27:47,666 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Intel Corporation' found in SEC master list: 'INTEL CORP' (Score: 81.80)
2025-08-21 12:27:47,666 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'INTC'
2025-08-21 12:27:47,879 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Advanced Micro Devices' found in SEC master list: 'ADVANCED MICRO DEVICES INC' (Score: 98.40)
2025-08-21 12:27:47,879 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'AMD'
2025-08-21 12:27:48,083 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Texas Instruments' found in SEC master list: 'TEXAS INSTRUMENTS INC' (Score: 97.80)
2025-08-21 12:27:48,083 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'TXN'
2025-08-21 12:27:48,092 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:167] - Processing complete. Found 4 unique nodes and 3 relationships.
2025-08-21 12:27:48,092 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:168] - Clean graph data saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\graph_ready_output_2025-08-21_12-27-47.json
2025-08-21 12:27:48,092 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:84] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-21 12:27:48,099 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:96] - Successfully saved 4 companies to the map.
2025-08-21 12:27:48,099 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:178] - All canonical maps have been saved.
2025-08-21 12:27:48,107 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:223] - Entity resolution pipeline completed successfully.
