import json

input_path = "clean_relationships_2025-08-28_09-21-15.jsonl"
output_path = "clean_relationships_2025-08-28_09-21-15_upper.jsonl"

with open(input_path, "r", encoding="utf-8") as infile, \
     open(output_path, "w", encoding="utf-8") as outfile:
    for line in infile:
        if not line.strip():
            continue
        obj = json.loads(line)
        # Uppercase entity names
        for ent in obj.get("entities", []):
            if "name" in ent:
                ent["name"] = ent["name"].upper()
        # Uppercase source and target entity names in relationships
        for rel in obj.get("relationships", []):
            if "source_entity" in rel:
                rel["source_entity"] = rel["source_entity"].upper()
            if "target_entity" in rel:
                rel["target_entity"] = rel["target_entity"].upper()
        outfile.write(json.dumps(obj, ensure_ascii=False) + "\n")