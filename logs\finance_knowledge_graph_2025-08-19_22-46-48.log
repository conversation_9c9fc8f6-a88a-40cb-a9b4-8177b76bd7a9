2025-08-19 22:46:48,838 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-19_22-46-48.log
2025-08-19 22:47:02,997 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-19 22:47:02,998 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-19 22:47:02,998 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-19 22:47:03,000 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-19 22:47:03,000 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-19 22:47:04,806 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-19 22:47:04,808 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-19 22:47:04,808 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-19 22:47:26,081 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-19 22:47:26,081 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-19 22:47:26,082 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-19 22:47:26,857 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-19 22:47:26,858 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-19 22:47:26,858 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: NVIDIA CORP ---
2025-08-19 22:47:26,859 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-19 22:48:18,951 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-19 22:48:18,951 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 1 of 3 for NVIDIA CORP (ID: 87579cee-e3fd-4e76-8054-39c6008a7e5e)
2025-08-19 22:48:18,951 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6860
2025-08-19 22:49:35,308 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 22 entities and 12 relationships.
2025-08-19 22:49:35,311 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 2 of 3 for NVIDIA CORP (ID: fd5e9016-92e9-4019-b927-3f939ce7e7be)
2025-08-19 22:49:35,312 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1460
2025-08-19 22:50:23,459 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 3 entities and 2 relationships.
2025-08-19 22:50:23,462 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 3 of 3 for NVIDIA CORP (ID: d77c864f-ba44-4b5f-a87a-9dfbf1951034)
2025-08-19 22:50:23,463 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1093
2025-08-19 22:51:04,927 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 4 entities and 3 relationships.
2025-08-19 22:51:04,929 - Finance_Knowledge_Graph - INFO - [pipeline.py:102] - --- Pipeline Run Finished ---
2025-08-19 22:51:04,929 - Finance_Knowledge_Graph - INFO - [pipeline.py:103] - Total entities extracted: 29
2025-08-19 22:51:04,930 - Finance_Knowledge_Graph - INFO - [pipeline.py:104] - Total relationships extracted: 17
2025-08-19 22:51:04,930 - Finance_Knowledge_Graph - INFO - [main.py:30] - =====================================================
2025-08-19 22:51:04,931 - Finance_Knowledge_Graph - INFO - [main.py:31] -       PIPELINE EXECUTION FINISHED                 
2025-08-19 22:51:04,931 - Finance_Knowledge_Graph - INFO - [main.py:32] - =====================================================
