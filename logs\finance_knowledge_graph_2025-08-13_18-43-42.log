2025-08-13 18:43:42,008 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-13_18-43-42.log
2025-08-13 18:43:56,534 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-13 18:43:56,535 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-13 18:43:56,535 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-13 18:43:56,538 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-13 18:43:56,538 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-13 18:43:58,485 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-13 18:43:58,488 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-13 18:43:58,491 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-13 18:44:24,127 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-13 18:44:24,129 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-13 18:44:24,129 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-mini
2025-08-13 18:44:24,918 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-13 18:44:24,920 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:44:24,925 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-13 18:44:24,927 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-13 18:44:24,928 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-13 18:44:24,928 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-13 18:44:24,930 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-13 18:45:20,501 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-13 18:45:20,504 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-13 18:45:23,527 - Finance_Knowledge_Graph - ERROR - [llm_extractor.py:99] - An error occurred during OpenAI API call: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': None}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\llm_extractor.py", line 75, in extract_relationships
    response = self.client.responses.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_utils\_utils.py", line 279, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\resources\responses\responses.py", line 603, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1242, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 919, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1023, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': None}}
2025-08-13 18:45:23,544 - Finance_Knowledge_Graph - WARNING - [pipeline.py:70] - LLM found no relationships for INTEL CORP. Skipping.
2025-08-13 18:45:23,545 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-13 18:45:23,545 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 0
2025-08-13 18:45:23,546 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-13 18:45:23,546 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:45:23,549 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-13 18:45:23,550 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-13 18:45:23,551 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-13 18:45:23,551 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
