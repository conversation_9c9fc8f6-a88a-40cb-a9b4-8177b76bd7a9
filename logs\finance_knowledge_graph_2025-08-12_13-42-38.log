2025-08-12 13:42:38,713 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_13-42-38.log
2025-08-12 13:42:45,252 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 13:42:47,232 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\../data/search_queries.json
2025-08-12 13:42:47,242 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 13:42:47,243 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 13:43:08,328 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 13:43:08,328 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 13:43:08,328 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 13:43:09,067 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 13:43:55,401 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-12 13:43:55,401 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 5 chunks to OpenAI for extraction. Total context length: 22005
2025-08-12 13:44:16,868 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:92] - Successfully extracted 13 relationships from the provided context.
