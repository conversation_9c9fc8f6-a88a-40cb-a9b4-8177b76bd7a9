{"GovernmentAgency": [{"canonical_name": "United States Government", "aliases": ["United States Government", "U.S. Government", "UNITED STATES GOVERNMENT", "U.S. GOVERNMENT"], "id": "United States Government"}, {"canonical_name": "China", "aliases": ["China"], "id": "China"}, {"canonical_name": "Hong Kong", "aliases": ["Hong Kong"], "id": "Hong Kong"}, {"canonical_name": "Macau", "aliases": ["Macau"], "id": "Macau"}, {"canonical_name": "Russia", "aliases": ["Russia"], "id": "Russia"}, {"canonical_name": "Saudi Arabia", "aliases": ["Saudi Arabia"], "id": "Saudi Arabia"}, {"canonical_name": "United Arab Emirates", "aliases": ["United Arab Emirates"], "id": "United Arab Emirates"}, {"canonical_name": "Vietnam", "aliases": ["Vietnam"], "id": "Vietnam"}, {"canonical_name": "Israel", "aliases": ["Israel"], "id": "Israel"}, {"canonical_name": "Asia-Pacific region", "aliases": ["Asia-Pacific region"], "id": "Asia-Pacific region"}, {"canonical_name": "Santa Clara, California", "aliases": ["Santa Clara, California"], "id": "Santa Clara, California"}, {"canonical_name": "Ukraine", "aliases": ["Ukraine"], "id": "Ukraine"}, {"canonical_name": "Taiwan", "aliases": ["Taiwan"], "id": "Taiwan"}, {"canonical_name": "United States Department of Commerce", "aliases": ["United States Department of Commerce", "UNITED STATES DEPARTMENT OF COMMERCE"], "id": "United States Department of Commerce"}, {"canonical_name": "U.S. Department of Commerce", "aliases": ["U.S. Department of Commerce", "U.S. DEPARTMENT OF COMMERCE"], "id": "U.S. Department of Commerce"}, {"canonical_name": "State Administration for Market Regulation", "aliases": ["State Administration for Market Regulation"], "id": "State Administration for Market Regulation"}, {"canonical_name": "Securities and Exchange Commission", "aliases": ["Securities and Exchange Commission"], "id": "Securities and Exchange Commission"}, {"canonical_name": "French Competition Authority", "aliases": ["French Competition Authority"], "id": "French Competition Authority"}], "Product": [{"canonical_name": "NVIDIA A100 Tensor Core GPU", "aliases": ["A100", "NVIDIA A100", "NVIDIA A100 Tensor Core GPU"], "id": "A100"}, {"canonical_name": "NVIDIA H100 Tensor Core GPU", "aliases": ["H100", "NVIDIA H100", "NVIDIA H100 Tensor Core GPU"], "id": "H100"}, {"canonical_name": "A800", "aliases": ["A800"], "id": "A800"}, {"canonical_name": "H800", "aliases": ["H800"], "id": "H800"}, {"canonical_name": "L4", "aliases": ["L4"], "id": "L4"}, {"canonical_name": "L40S", "aliases": ["L40S"], "id": "L40S"}, {"canonical_name": "RTX 4090", "aliases": ["RTX 4090"], "id": "RTX 4090"}, {"canonical_name": "RTX 6000 Ada", "aliases": ["RTX 6000 Ada", "RTX6000D"], "id": "RTX 6000 Ada"}, {"canonical_name": "B100", "aliases": ["B100"], "id": "B100"}, {"canonical_name": "NVIDIA Blackwell architecture B200 processor", "aliases": ["B200", "NVIDIA Blackwell architecture B200 processor"], "id": "B200"}, {"canonical_name": "GB200 system", "aliases": ["GB200", "GB200 system"], "id": "GB200"}, {"canonical_name": "NVIDIA H200", "aliases": ["H200", "NVIDIA H200"], "id": "H200"}, {"canonical_name": "NVIDIA RTX GPU", "aliases": ["NVIDIA GPU", "NVIDIA GPUs", "NVIDIA RTX GPU", "NVIDIA GPUS"], "id": "NVIDIA GPU"}, {"canonical_name": "NVIDIA Grace CPU", "aliases": ["NVIDIA CPU", "NVIDIA CPUs", "NVIDIA Grace CPU"], "id": "NVIDIA CPU"}, {"canonical_name": "NVIDIA Data Processing Units (DPUs)", "aliases": ["NVIDIA DPU", "NVIDIA Data Processing Units (DPUs)", "Data Processing Unit"], "id": "NVIDIA DPU"}, {"canonical_name": "NVIDIA DGX Cloud software and service", "aliases": ["DGX Cloud", "NVIDIA DGX Cloud", "DGX Cloud software and service", "NVIDIA DGX Cloud software and service"], "id": "DGX Cloud"}, {"canonical_name": "NVIDIA AI Enterprise software", "aliases": ["NVIDIA AI Enterprise", "NVIDIA AI Enterprise software", "NVIDIA AI Enterprise Software"], "id": "NVIDIA AI Enterprise"}, {"canonical_name": "NVIDIA Inference Microservices", "aliases": ["NVIDIA NIM", "NVIDIA Inference Microservices"], "id": "NVIDIA NIM"}, {"canonical_name": "NVIDIA NeMo", "aliases": ["NVIDIA NeMo", "NeMo"], "id": "NVIDIA NeMo"}, {"canonical_name": "AI Blueprints", "aliases": ["AI Blueprints"], "id": "AI Blueprints"}, {"canonical_name": "GeForce Experience", "aliases": ["GeForce Experience"], "id": "GeForce Experience"}, {"canonical_name": "NVIDIA GeForce RTX platform", "aliases": ["GeForce RTX platform", "GeForce platform", "NVIDIA RTX platform", "NVIDIA GeForce RTX platform"], "id": "GeForce RTX platform"}, {"canonical_name": "NVIDIA Omniverse", "aliases": ["Omniverse", "NVIDIA Omniverse simulation", "NVIDIA Omniverse"], "id": "Omniverse"}, {"canonical_name": "NVIDIA DRIVE stack", "aliases": ["NVIDIA DRIVE", "NVIDIA DRIVE stack"], "id": "NVIDIA DRIVE"}, {"canonical_name": "GeForce NOW", "aliases": ["GeForce NOW", "GeForce NOW game streaming service"], "id": "GeForce NOW"}, {"canonical_name": "NVIDIA Data Center accelerated computing platform", "aliases": ["NVIDIA Data Center Platform", "Data Center accelerated computing platform", "NVIDIA Data Center accelerated computing platform"], "id": "NVIDIA Data Center Platform"}, {"canonical_name": "NVIDIA Interconnects", "aliases": ["NVIDIA Interconnects"], "id": "NVIDIA Interconnects"}, {"canonical_name": "NVIDIA Blackwell AI CHIPS", "aliases": ["NVIDIA Blackwell architecture", "Blackwell AI chip", "NVIDIA Blackwell AI chip", "BLACKWELL CHIPS", "BLACKWELL AI CHIPS", "NVIDIA Blackwell AI CHIPS"], "id": "NVIDIA Blackwell architecture"}, {"canonical_name": "H20 chips", "aliases": ["H20 chips", "H20 CHIP"], "id": "H20 chips"}, {"canonical_name": "NVIDIA processors", "aliases": ["NVIDIA processors", "NVIDIA PROCESSORS"], "id": "NVIDIA processors"}, {"canonical_name": "H20 AI CHIP", "aliases": ["H20", "H20 AI CHIP"], "id": "H20"}, {"canonical_name": "Azure", "aliases": ["Azure", "AZURE"], "id": "Azure"}, {"canonical_name": "Stargate Norway", "aliases": ["Stargate Norway", "STARGATE NORWAY"], "id": "Stargate Norway"}, {"canonical_name": "RTX Pro GPU", "aliases": ["RTX Pro GPU"], "id": "RTX Pro GPU"}, {"canonical_name": "Comet browser", "aliases": ["Comet browser"], "id": "Comet browser"}, {"canonical_name": "Chrome", "aliases": ["Chrome"], "id": "Chrome"}, {"canonical_name": "Safari", "aliases": ["Safari"], "id": "Safari"}, {"canonical_name": "NVIDIA Avatar Cloud Engine", "aliases": ["NVIDIA Avatar Cloud Engine"], "id": "NVIDIA Avatar Cloud Engine"}, {"canonical_name": "vGPU", "aliases": ["vGPU"], "id": "vGPU"}, {"canonical_name": "NVIDIA Clara", "aliases": ["NVIDIA Clara"], "id": "NVIDIA Clara"}, {"canonical_name": "NVIDIA Spectrum-X Ethernet networking platform", "aliases": ["NVIDIA Spectrum-X Ethernet networking platform"], "id": "NVIDIA Spectrum-X Ethernet networking platform"}, {"canonical_name": "NVIDIA AI Foundry service", "aliases": ["NVIDIA AI Foundry service"], "id": "NVIDIA AI Foundry service"}, {"canonical_name": "NVIDIA ACE generative AI microservices", "aliases": ["NVIDIA ACE generative AI microservices"], "id": "NVIDIA ACE generative AI microservices"}, {"canonical_name": "NVIDIA Metropolis vision AI", "aliases": ["NVIDIA Metropolis vision AI"], "id": "NVIDIA Metropolis vision AI"}, {"canonical_name": "NVIDIA Isaac AI robot development", "aliases": ["NVIDIA Isaac AI robot development"], "id": "NVIDIA Isaac AI robot development"}, {"canonical_name": "GeForce GPU", "aliases": ["GeForce GPU"], "id": "GeForce GPU"}, {"canonical_name": "vGPU software", "aliases": ["vGPU software"], "id": "vGPU software"}, {"canonical_name": "NVIDIA Accelerated Computing Platform", "aliases": ["NVIDIA Accelerated Computing Platform"], "id": "NVIDIA Accelerated Computing Platform"}, {"canonical_name": "NVIDIA VGPU Software", "aliases": ["NVIDIA VGPU Software"], "id": "NVIDIA VGPU Software"}, {"canonical_name": "GeForce RTX 40 Series GPU", "aliases": ["GeForce RTX 40 Series GPU"], "id": "GeForce RTX 40 Series GPU"}, {"canonical_name": "NVIDIA DGX system", "aliases": ["NVIDIA DGX system"], "id": "NVIDIA DGX system"}, {"canonical_name": "NVIDIA HGX system", "aliases": ["NVIDIA HGX system"], "id": "NVIDIA HGX system"}, {"canonical_name": "NVIDIA MGX system", "aliases": ["NVIDIA MGX system"], "id": "NVIDIA MGX system"}, {"canonical_name": "Studio Drivers", "aliases": ["Studio Drivers"], "id": "Studio Drivers"}, {"canonical_name": "Tensor Core GPU", "aliases": ["Tensor Core GPU"], "id": "Tensor Core GPU"}, {"canonical_name": "<PERSON>", "aliases": ["<PERSON>"], "id": "<PERSON>"}, {"canonical_name": "<PERSON>", "aliases": ["<PERSON>"], "id": "<PERSON>"}, {"canonical_name": "RTX AI PC", "aliases": ["RTX AI PC"], "id": "RTX AI PC"}, {"canonical_name": "Quantum for InfiniBand", "aliases": ["Quantum for InfiniBand"], "id": "Quantum for InfiniBand"}, {"canonical_name": "Spectrum for Ethernet", "aliases": ["Spectrum for Ethernet"], "id": "Spectrum for Ethernet"}, {"canonical_name": "Jetson robotics platform", "aliases": ["Jetson robotics platform"], "id": "Jetson robotics platform"}, {"canonical_name": "Automotive infotainment platform", "aliases": ["Automotive infotainment platform"], "id": "Automotive infotainment platform"}, {"canonical_name": "Gaming GPU", "aliases": ["Gaming GPU"], "id": "Gaming GPU"}, {"canonical_name": "Hopper computing platform", "aliases": ["Hopper computing platform"], "id": "Hopper computing platform"}, {"canonical_name": "High-speed network interconnect", "aliases": ["High-speed network interconnect"], "id": "High-speed network interconnect"}, {"canonical_name": "NVL 72", "aliases": ["NVL 72"], "id": "NVL 72"}, {"canonical_name": "NVL 36", "aliases": ["NVL 36"], "id": "NVL 36"}, {"canonical_name": "Blackwell systems", "aliases": ["Blackwell systems"], "id": "Blackwell systems"}, {"canonical_name": "ASCEND CHIPS", "aliases": ["ASCEND CHIPS"], "id": "ASCEND CHIPS"}, {"canonical_name": "KUNLUN CHIPS", "aliases": ["KUNLUN CHIPS"], "id": "KUNLUN CHIPS"}, {"canonical_name": "AI CHIP FOR THE CHINESE MARKET", "aliases": ["AI CHIP FOR THE CHINESE MARKET"], "id": "AI CHIP FOR THE CHINESE MARKET"}, {"canonical_name": "B30A", "aliases": ["B30A"], "id": "B30A"}, {"canonical_name": "NEXT-GENERATION AI CHIP", "aliases": ["NEXT-GENERATION AI CHIP"], "id": "NEXT-GENERATION AI CHIP"}, {"canonical_name": "COPILOT", "aliases": ["COPILOT"], "id": "COPILOT"}, {"canonical_name": "META RAY-BANS", "aliases": ["META RAY-BANS"], "id": "META RAY-BANS"}], "LawOrRegulation": [{"canonical_name": "ECCN 3A090.a", "aliases": ["ECCN 3A090.a"], "id": "ECCN 3A090.a"}, {"canonical_name": "ECCN 4A090.a", "aliases": ["ECCN 4A090.a"], "id": "ECCN 4A090.a"}, {"canonical_name": "Export license requirements", "aliases": ["Export licensing requirements", "Export license requirements", "Export license requirement"], "id": "Export licensing requirements"}, {"canonical_name": "Section 4(a)(2) of the Securities Act of 1933", "aliases": ["Section 4(a)(2) of the Securities Act", "Section 4(a)(2) of the Securities Act of 1933"], "id": "Section 4(a)(2) of the Securities Act"}, {"canonical_name": "Regulation D", "aliases": ["Regulation D"], "id": "Regulation D"}, {"canonical_name": "Regulation S", "aliases": ["Regulation S"], "id": "Regulation S"}, {"canonical_name": "Ukraine sanctions", "aliases": ["Ukraine sanctions"], "id": "Ukraine sanctions"}, {"canonical_name": "Export controls", "aliases": ["Export controls", "EXPORT CONTROLS"], "id": "Export controls"}, {"canonical_name": "AI Diffusion IFR", "aliases": ["AI Diffusion IFR"], "id": "AI Diffusion IFR"}, {"canonical_name": "U.S. AI CHIP EXPORT CONTROLS", "aliases": ["U.S. Export Controls", "U.S. EXPORT CONTROLS", "U.S. AI CHIP EXPORT CONTROLS"], "id": "U.S. Export Controls"}, {"canonical_name": "U.S. government rules", "aliases": ["U.S. government rules"], "id": "U.S. government rules"}, {"canonical_name": "United States export restrictions", "aliases": ["U.S. export restrictions", "EXPORT RESTRICTIONS IMPOSED BY THE UNITED STATES", "United States export restrictions"], "id": "U.S. export restrictions"}, {"canonical_name": "Foreign Corrupt Practices Act", "aliases": ["Foreign Corrupt Practices Act"], "id": "Foreign Corrupt Practices Act"}], "BusinessSegment": [{"canonical_name": "Data Center", "aliases": ["Data Center"], "id": "Data Center"}, {"canonical_name": "Gaming", "aliases": ["Gaming"], "id": "Gaming"}, {"canonical_name": "Professional Visualization", "aliases": ["Professional Visualization"], "id": "Professional Visualization"}, {"canonical_name": "Automotive", "aliases": ["Automotive"], "id": "Automotive"}, {"canonical_name": "Compute & Networking segment", "aliases": ["Compute & Networking segment", "Compute & Networking"], "id": "Compute & Networking segment"}, {"canonical_name": "Data Center computing", "aliases": ["Data Center computing"], "id": "Data Center computing"}, {"canonical_name": "Data Center networking", "aliases": ["Data Center networking"], "id": "Data Center networking"}, {"canonical_name": "Graphics", "aliases": ["Graphics"], "id": "Graphics"}, {"canonical_name": "All Other", "aliases": ["All Other"], "id": "All Other"}, {"canonical_name": "AUGMENTED-REALIT<PERSON> GLASSES CHIP BUSINESS", "aliases": ["AUGMENTED-REALIT<PERSON> GLASSES CHIP BUSINESS"], "id": "AUGMENTED-REALIT<PERSON> GLASSES CHIP BUSINESS"}], "Technology": [{"canonical_name": "CUDA-X", "aliases": ["CUDA", "CUDA-X"], "id": "CUDA"}, {"canonical_name": "NVIDIA Avatar Cloud Engine", "aliases": ["NVIDIA Avatar Cloud Engine"], "id": "NVIDIA Avatar Cloud Engine"}, {"canonical_name": "vGPU", "aliases": ["vGPU"], "id": "vGPU"}, {"canonical_name": "Tensor Cores", "aliases": ["Tensor Core", "Tensor Cores"], "id": "Tensor Core"}, {"canonical_name": "InfiniBand", "aliases": ["InfiniBand"], "id": "InfiniBand"}, {"canonical_name": "Ethernet", "aliases": ["Ethernet"], "id": "Ethernet"}, {"canonical_name": "Chip Design", "aliases": ["Chip Design"], "id": "Chip Design"}, {"canonical_name": "Ethereum", "aliases": ["Ethereum"], "id": "Ethereum"}, {"canonical_name": "Large Language Model", "aliases": ["Large Language Model"], "id": "Large Language Model"}, {"canonical_name": "Networking technology", "aliases": ["Networking technology"], "id": "Networking technology"}, {"canonical_name": "AlexNet neural network", "aliases": ["AlexNet neural network"], "id": "AlexNet neural network"}, {"canonical_name": "Llama 3.1 collection of models", "aliases": ["Llama 3.1 collection of models"], "id": "Llama 3.1 collection of models"}, {"canonical_name": "CoWoS technology", "aliases": ["CoWoS technology"], "id": "CoWoS technology"}, {"canonical_name": "Blackwell architecture", "aliases": ["Blackwell architecture", "BLACKWELL ARCHITECTURE"], "id": "Blackwell architecture"}, {"canonical_name": "NVIDIA Blackwell architecture", "aliases": ["NVIDIA Blackwell architecture"], "id": "NVIDIA Blackwell architecture"}, {"canonical_name": "NVLink compute fabric", "aliases": ["NVLink compute fabric"], "id": "NVLink compute fabric"}, {"canonical_name": "Patent", "aliases": ["Patent"], "id": "Patent"}, {"canonical_name": "Trademark", "aliases": ["Trademark"], "id": "Trademark"}, {"canonical_name": "Trade secret", "aliases": ["Trade secret"], "id": "Trade secret"}, {"canonical_name": "Employee nondisclosure agreement", "aliases": ["Employee nondisclosure agreement"], "id": "Employee nondisclosure agreement"}, {"canonical_name": "Third-party nondisclosure agreement", "aliases": ["Third-party nondisclosure agreement"], "id": "Third-party nondisclosure agreement"}, {"canonical_name": "Licensing arrangement", "aliases": ["Licensing arrangement"], "id": "Licensing arrangement"}, {"canonical_name": "Trade Secret", "aliases": ["Trade Secret"], "id": "Trade Secret"}, {"canonical_name": "Nondisclosure Agreement", "aliases": ["Nondisclosure Agreement"], "id": "Nondisclosure Agreement"}, {"canonical_name": "Licensing Arrangement", "aliases": ["Licensing Arrangement"], "id": "Licensing Arrangement"}, {"canonical_name": "COMPUTE ARCHITECTURE FOR NEURAL NETWORKS (CANN)", "aliases": ["COMPUTE ARCHITECTURE FOR NEURAL NETWORKS (CANN)"], "id": "COMPUTE ARCHITECTURE FOR NEURAL NETWORKS (CANN)"}], "IndustrySector": [{"canonical_name": "Automotive OEMs", "aliases": ["Automotive OEMs"], "id": "Automotive OEMs"}, {"canonical_name": "Tier-1 suppliers", "aliases": ["Tier-1 suppliers"], "id": "Tier-1 suppliers"}, {"canonical_name": "Start-ups", "aliases": ["Start-ups"], "id": "Start-ups"}, {"canonical_name": "Artificial Intelligence", "aliases": ["Artificial Intelligence"], "id": "Artificial Intelligence"}, {"canonical_name": "Healthcare", "aliases": ["Healthcare"], "id": "Healthcare"}, {"canonical_name": "Financial Services", "aliases": ["Financial Services"], "id": "Financial Services"}, {"canonical_name": "Manufacturing", "aliases": ["Manufacturing"], "id": "Manufacturing"}, {"canonical_name": "Retail", "aliases": ["Retail"], "id": "Retail"}, {"canonical_name": "Cloud service provider", "aliases": ["Cloud service provider"], "id": "Cloud service provider"}, {"canonical_name": "Consumer internet company", "aliases": ["Consumer internet company"], "id": "Consumer internet company"}, {"canonical_name": "Enterprise software industry", "aliases": ["Enterprise software industry"], "id": "Enterprise software industry"}, {"canonical_name": "Healthcare industry", "aliases": ["Healthcare industry"], "id": "Healthcare industry"}, {"canonical_name": "Transportation industry", "aliases": ["Transportation industry"], "id": "Transportation industry"}, {"canonical_name": "Financial services industry", "aliases": ["Financial services industry"], "id": "Financial services industry"}, {"canonical_name": "Gaming industry", "aliases": ["Gaming industry"], "id": "Gaming industry"}], "GeographicRegion": [{"canonical_name": "People's Republic of China", "aliases": ["China", "People's Republic of China", "CHINA", "PEOPLE'S REPUBLIC OF CHINA"], "id": "China"}, {"canonical_name": "Country Group D1", "aliases": ["Country Group D1"], "id": "Country Group D1"}, {"canonical_name": "Country Group D4", "aliases": ["Country Group D4"], "id": "Country Group D4"}, {"canonical_name": "Country Group D5", "aliases": ["Country Group D5"], "id": "Country Group D5"}, {"canonical_name": "Asia-Pacific region", "aliases": ["Asia-Pacific region"], "id": "Asia-Pacific region"}, {"canonical_name": "Santa Clara, California", "aliases": ["Santa Clara, California"], "id": "Santa Clara, California"}, {"canonical_name": "Santa Clara", "aliases": ["Santa Clara"], "id": "Santa Clara"}, {"canonical_name": "California", "aliases": ["California"], "id": "California"}, {"canonical_name": "Taiwan", "aliases": ["Taiwan"], "id": "Taiwan"}, {"canonical_name": "United States", "aliases": ["United States"], "id": "United States"}, {"canonical_name": "India", "aliases": ["India"], "id": "India"}, {"canonical_name": "Israel", "aliases": ["Israel"], "id": "Israel"}, {"canonical_name": "Russia", "aliases": ["Russia"], "id": "Russia"}, {"canonical_name": "Hong Kong", "aliases": ["Hong Kong"], "id": "Hong Kong"}, {"canonical_name": "ARIZONA", "aliases": ["ARIZONA"], "id": "ARIZONA"}, {"canonical_name": "NORWAY", "aliases": ["NORWAY"], "id": "NORWAY"}], "Person": [{"canonical_name": "<PERSON>", "aliases": ["<PERSON>", "TIM HOETTGES"], "id": "<PERSON>"}, {"canonical_name": "JENSEN HUANG", "aliases": ["JENSEN HUANG"], "id": "JENSEN HUANG"}, {"canonical_name": "AMY SHAPERO", "aliases": ["AMY SHAPERO"], "id": "AMY SHAPERO"}]}