2025-08-19 16:27:19,011 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-19_16-27-19.log
2025-08-19 16:27:32,736 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-19 16:27:32,736 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-19 16:27:32,736 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-19 16:27:32,736 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-19 16:27:32,736 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-19 16:27:34,583 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-19 16:27:34,583 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-19 16:27:34,583 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-19 16:27:58,106 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-19 16:27:58,106 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-19 16:27:58,114 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-19 16:27:58,837 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-19 16:27:58,837 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-19 16:27:58,837 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: NVIDIA CORP ---
2025-08-19 16:27:58,837 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-19 16:28:45,153 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-19 16:28:45,153 - Finance_Knowledge_Graph - INFO - [pipeline.py:76] - Processing chunk 1 of 46 for NVIDIA CORP (ID: a3a20ae7-aa9d-4cfd-9a91-a36d6e5c7562)
2025-08-19 16:28:45,161 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3582
2025-08-19 16:30:36,886 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 20 entities and 19 relationships.
2025-08-19 16:30:36,886 - Finance_Knowledge_Graph - INFO - [pipeline.py:76] - Processing chunk 2 of 46 for NVIDIA CORP (ID: de0f523e-f6ac-4a0b-8469-5839492b46e1)
2025-08-19 16:30:36,886 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3601
2025-08-19 16:31:48,334 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 20 entities and 19 relationships.
2025-08-19 16:31:48,343 - Finance_Knowledge_Graph - INFO - [pipeline.py:76] - Processing chunk 3 of 46 for NVIDIA CORP (ID: dbe4fae5-62f2-4182-83f8-65f73161e70f)
2025-08-19 16:31:48,343 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6474
2025-08-19 16:34:32,624 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 17 entities and 16 relationships.
2025-08-19 16:34:32,624 - Finance_Knowledge_Graph - INFO - [pipeline.py:76] - Processing chunk 4 of 46 for NVIDIA CORP (ID: 87579cee-e3fd-4e76-8054-39c6008a7e5e)
2025-08-19 16:34:32,624 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6860
2025-08-19 16:36:10,516 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 19 entities and 18 relationships.
2025-08-19 16:36:10,516 - Finance_Knowledge_Graph - INFO - [pipeline.py:76] - Processing chunk 5 of 46 for NVIDIA CORP (ID: 51e0fce9-1d7f-4f68-b17b-6bb144f2c436)
2025-08-19 16:36:10,516 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1488
2025-08-19 16:37:17,714 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 7 entities and 2 relationships.
2025-08-19 16:37:17,714 - Finance_Knowledge_Graph - INFO - [pipeline.py:90] - --- Pipeline Run Finished ---
2025-08-19 16:37:17,714 - Finance_Knowledge_Graph - INFO - [pipeline.py:91] - Total entities extracted: 83
2025-08-19 16:37:17,714 - Finance_Knowledge_Graph - INFO - [pipeline.py:92] - Total relationships extracted: 74
2025-08-19 16:37:17,719 - Finance_Knowledge_Graph - INFO - [main.py:30] - =====================================================
2025-08-19 16:37:17,719 - Finance_Knowledge_Graph - INFO - [main.py:31] -       PIPELINE EXECUTION FINISHED                 
2025-08-19 16:37:17,719 - Finance_Knowledge_Graph - INFO - [main.py:32] - =====================================================
