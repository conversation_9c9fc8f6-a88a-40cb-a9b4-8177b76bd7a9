2025-08-14 18:07:41,304 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_18-07-41.log
2025-08-14 18:07:55,546 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-14 18:07:55,546 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-14 18:07:55,546 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-14 18:07:55,546 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-14 18:07:55,546 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-14 18:07:58,230 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-14 18:07:58,230 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-14 18:07:58,232 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-14 18:08:32,957 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-14 18:08:32,961 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-14 18:08:32,961 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-14 18:08:34,045 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-14 18:08:34,050 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-14 18:08:34,055 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-14 18:08:34,055 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-14 18:08:34,057 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-14 18:08:34,058 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-14 18:08:34,058 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-14 18:09:45,461 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-14 18:09:45,471 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-14 18:11:19,606 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:91] - Successfully extracted 4 relationships from the provided context.
2025-08-14 18:11:19,606 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 4 raw relationships for INTEL CORP.
2025-08-14 18:11:19,606 - Finance_Knowledge_Graph - ERROR - [pipeline.py:120] - An unexpected error occurred during normalization: string indices must be integers, not 'str'. Raw relationship: {'entity_1': 'Intel Products', 'entity_2': 'Client Computing Group (CCG)', 'relationship_type': 'is comprised of', 'strength_score': 0.95, 'evidence': 'Intel Products is comprised of three operating segments: CCG, DCAI, and NEX.', 'source': {'report_type': '10-Q', 'period_of_report': '2024-06-29', 'section': "Management's Discussion and Analysis Overview and Intel Products Financial Performance", 'source_url': 'https://www.sec.gov/Archives/edgar/data/50863/000005086324000124/intc-20240629.htm'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\pipeline.py", line 83, in run
    entity_name=entity_1_raw['name'],
                ~~~~~~~~~~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'
2025-08-14 18:11:19,616 - Finance_Knowledge_Graph - ERROR - [pipeline.py:120] - An unexpected error occurred during normalization: string indices must be integers, not 'str'. Raw relationship: {'entity_1': 'Intel Products', 'entity_2': 'Data Center and Artificial Intelligence (DCAI)', 'relationship_type': 'is comprised of', 'strength_score': 0.95, 'evidence': 'Intel Products is comprised of three operating segments: CCG, DCAI, and NEX.', 'source': {'report_type': '10-Q', 'period_of_report': '2024-06-29', 'section': "Management's Discussion and Analysis Overview and Intel Products Financial Performance", 'source_url': 'https://www.sec.gov/Archives/edgar/data/50863/000005086324000124/intc-20240629.htm'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\pipeline.py", line 83, in run
    entity_name=entity_1_raw['name'],
                ~~~~~~~~~~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'
2025-08-14 18:11:19,619 - Finance_Knowledge_Graph - ERROR - [pipeline.py:120] - An unexpected error occurred during normalization: string indices must be integers, not 'str'. Raw relationship: {'entity_1': 'Intel Products', 'entity_2': 'Networking and Edge (NEX)', 'relationship_type': 'is comprised of', 'strength_score': 0.95, 'evidence': 'Intel Products is comprised of three operating segments: CCG, DCAI, and NEX.', 'source': {'report_type': '10-Q', 'period_of_report': '2024-06-29', 'section': "Management's Discussion and Analysis Overview and Intel Products Financial Performance", 'source_url': 'https://www.sec.gov/Archives/edgar/data/50863/000005086324000124/intc-20240629.htm'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\pipeline.py", line 83, in run
    entity_name=entity_1_raw['name'],
                ~~~~~~~~~~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'
2025-08-14 18:11:19,621 - Finance_Knowledge_Graph - ERROR - [pipeline.py:120] - An unexpected error occurred during normalization: string indices must be integers, not 'str'. Raw relationship: {'entity_1': 'INTEL CORP', 'entity_2': '10-Q', 'relationship_type': 'filed by', 'strength_score': 0.85, 'evidence': 'This excerpt is from the 10-Q report filed by INTEL CORP for the fiscal period ending on 2024-06-29.', 'source': {'report_type': '10-Q', 'period_of_report': '2024-06-29', 'section': "Management's Discussion and Analysis Overview and Intel Products Financial Performance", 'source_url': 'https://www.sec.gov/Archives/edgar/data/50863/000005086324000124/intc-20240629.htm'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\pipeline.py", line 83, in run
    entity_name=entity_1_raw['name'],
                ~~~~~~~~~~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'
2025-08-14 18:11:19,625 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-14 18:11:19,625 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 0
2025-08-14 18:11:19,628 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-14 18:11:19,628 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-14 18:11:19,637 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-14 18:11:19,643 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-14 18:11:19,648 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-14 18:11:19,648 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
