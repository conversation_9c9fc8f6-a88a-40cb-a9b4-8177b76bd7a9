2025-08-17 14:00:57,896 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_14-00-57.log
2025-08-17 14:01:07,477 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 14:01:07,477 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 14:01:07,477 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 14:01:07,477 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 14:01:07,477 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 14:01:09,380 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 14:01:09,382 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 14:01:09,382 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 14:01:28,180 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 14:01:28,180 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 14:01:28,180 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 14:01:28,892 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 14:01:28,897 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 14:01:28,897 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: NVIDIA CORP ---
2025-08-17 14:01:28,897 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-17 14:01:46,519 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-17 14:01:46,523 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 46 for NVIDIA CORP (ID: a3a20ae7-aa9d-4cfd-9a91-a36d6e5c7562)
2025-08-17 14:01:46,527 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3582
2025-08-17 14:04:01,696 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 27 entities and 18 relationships.
2025-08-17 14:04:01,696 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 2 of 46 for NVIDIA CORP (ID: de0f523e-f6ac-4a0b-8469-5839492b46e1)
2025-08-17 14:04:01,696 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3601
2025-08-17 14:05:05,721 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 14:05:05,723 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 3 of 46 for NVIDIA CORP (ID: dbe4fae5-62f2-4182-83f8-65f73161e70f)
2025-08-17 14:05:05,725 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6474
2025-08-17 14:06:56,194 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 21 entities and 16 relationships.
2025-08-17 14:06:56,196 - Finance_Knowledge_Graph - INFO - [pipeline.py:79] - --- Pipeline Run Finished ---
2025-08-17 14:06:56,197 - Finance_Knowledge_Graph - INFO - [pipeline.py:80] - Total entities extracted: 48
2025-08-17 14:06:56,197 - Finance_Knowledge_Graph - INFO - [pipeline.py:81] - Total relationships extracted: 34
2025-08-17 14:06:56,198 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-17 14:06:56,199 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-17 14:06:56,199 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
