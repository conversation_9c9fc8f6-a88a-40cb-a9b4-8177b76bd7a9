import asyncio
from typing import List, Dict, Any
import random
import json
from openai import OpenAI
from google import genai
from google.genai import types
from pydantic import BaseModel
# from Eco_RAG.prompt import prompts
# from Eco_RAG.schema import Ent_Rel
# from Eco_RAG.config import config
import json
import os
import asyncio
from typing import List, Dict, Optional, Set
from pydantic import BaseModel, Field
from thefuzz import fuzz, process
from pathlib import Path

# We will need to import your CompanyNormalizer and logger
from src.company_normalizer import CompanyNormalizer
from logger import log
import config

class DisambiguationOutput(BaseModel):
    entity_name: str
    matches_with: Optional[str] = None
    preferred_name: Optional[str] = None



def random_choice_from_list(strings: List[str]) -> str:
    """
    Return a random string from the given list.
    """
    if not strings:
        raise ValueError("strings list is empty")
    return random.choice(strings)



def json_schema_from_basemodel(
    basemodel_class: BaseModel, mode: str = "openai"
) -> dict:
    schema = basemodel_class.model_json_schema()
    if mode == "openai":
        return {
            "type": "json_schema",
            "json_schema": {
                "name": "assistant_response",
                "strict": True,
                "schema": schema,
            },
        }
    elif mode == "gemini":
        return schema
    else:
        raise ValueError(
            f"Unsupported mode: {mode}. Supported modes are 'openai' and 'gemini'."
        )


def call_openai_api(
    system_prompt: str,
    user_prompt: str,
    api_key=None,
    response_model: BaseModel = None,
    model_name="gpt-5-nano",
    base_url=None,
    reasoning_effort=None
) -> dict:
    client = OpenAI(
        api_key=random_choice_from_list(config["openai_api_key"]) if api_key==None else api_key,
        base_url=base_url,
    )
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]

    if response_model:
        # Assuming instructor is used to patch the client.
        # The response_model kwarg is used by instructor to return a pydantic object.
        response = client.chat.completions.parse(
            model=model_name,
            messages=messages,
            response_format=response_model,
            reasoning_effort=reasoning_effort
        )
        # The response is the pydantic model instance, dump it to a dict.
        response_content = json.loads(response.choices[0].message.content)
    else:
        response = client.chat.completions.create(
            model=model_name,
            messages=messages,
        )
        response_content = response.choices[0].message.content

    print("##################################")
    print(response_content)
    print("##################################")

    return {"content": response_content}

def call_gemini_api(
    system_prompt: str,
    user_prompt: str,
    schema: dict = None,
    model_name: str = "gemini-2.5-flash",
    thinking_token=0,
    process_name: str = "Unknown Process",
    max_output_tokens: int = 0,
    stream: bool = False,
) -> dict:
    client = genai.Client(api_key="AIzaSyAa3N5cZIPcjg6S5ADiAmJJMqtSOVVUFNw")
    config_payload = types.GenerateContentConfig(
        temperature=0,
        thinking_config=(
            None
            if thinking_token == None
            else types.ThinkingConfig(
                thinking_budget=thinking_token, include_thoughts=False
            )
        ),
        system_instruction=system_prompt,
    )

    if max_output_tokens > 0:
        config_payload.max_output_tokens = max_output_tokens
    if schema:
        config_payload.response_mime_type = "application/json"
        config_payload.response_schema = schema

    if stream:
        response_stream = client.models.generate_content_stream(
            model=model_name,
            contents=[user_prompt],
            config=config_payload,
        )
        full_response_text = ""
        print(f"--- [Streaming Output for {process_name}] ---")
        for chunk in response_stream:
            if hasattr(chunk, "text"):
                print(chunk.text, end="", flush=True)
                full_response_text += chunk.text
        print("\n-----------------------------")
        response_content = (
            json.loads(full_response_text) if schema else full_response_text
        )
        return {"content": response_content}

    response = client.models.generate_content(
        model=model_name,
        contents=[user_prompt],
        config=config_payload,
    )

    full_response_text = response.text
    response_content = (
        json.loads(full_response_text) if schema else full_response_text
    )
    print(
        f"--- [Output for {process_name}] ---\n{response_content}\n-----------------------------"
    )

    return {"content": response_content}


# async def _process_single_article(article: dict):
#     """Helper function to process a single article asynchronously."""
#     system_prompt = prompts["ent_rel_extract"]
    
#     # Format the user prompt with the article content
#     formatted_chunks = json.dumps(article.get("content", ""), indent=2)
#     user_prompt = prompts["user_prompt_template"].format(formatted_chunks=formatted_chunks)
    
#     # This is a synchronous call, so we'll run it in an executor
#     loop = asyncio.get_running_loop()
#     llm_response = await loop.run_in_executor(
#         None,
#         lambda: call_openai_api(
#             system_prompt=system_prompt,
#             user_prompt=user_prompt,
#             response_model=Ent_Rel,
#         ),
#     )

#     if llm_response and "content" in llm_response:
#         extracted_data = llm_response["content"]

#         # Remove entities that are not part of any relation
#         if "relationships" in extracted_data and "entities" in extracted_data:
#             relationships = extracted_data.get("relationships", [])
#             entities = extracted_data.get("entities", [])

#             active_entities = set()
#             for rel in relationships:
#                 if rel.get("source_entity"):
#                     active_entities.add(rel["source_entity"])
#                 if rel.get("target_entity"):
#                     active_entities.add(rel["target_entity"])
            
#             original_entity_count = len(entities)
#             filtered_entities = [ent for ent in entities if ent.get("name") in active_entities]
            
#             if len(filtered_entities) < original_entity_count:
#                 print(f"Filtered out {original_entity_count - len(filtered_entities)} unused entities.")

#             extracted_data["entities"] = filtered_entities

#         # Add the link to the top-level of the extracted data object
#         extracted_data['link'] = article.get("link")
#         return extracted_data
    
#     return None



#####################################################
def _fuzzy_match(entities: List[Dict], alias_path: str) -> List[Dict]:
    """
    Performs fuzzy matching for a list of entities against an alias file.

    Args:
        entities: A list of entity dictionaries, e.g., [{'name': '...', 'type': '...'}]
        alias_path: The file path for the aliases.json file.

    Returns:
        A list of dictionaries, each containing the original entity and a list of
        canonical name candidates. e.g., [{'entity': {...}, 'candidates': [...]}]
    """
    if not os.path.exists(alias_path):
        print(f"Alias file not found at {alias_path}. No candidates can be matched.")
        return [{'entity': ent, 'candidates': []} for ent in entities]

    with open(alias_path, 'r', encoding='utf-8') as f:
        aliases_data = json.load(f)

    results = []
    for entity in entities:
        entity_name = entity['name']
        entity_type = entity['type']
        
        # Build a lookup for all aliases of the correct type for fast matching
        type_aliases = aliases_data.get(entity_type, [])
        if not type_aliases:
            results.append({'entity': entity, 'candidates': []})
            continue

        # Create flat maps for exact and fuzzy matching
        exact_match_map = {alias.lower(): entry['canonical_name'] for entry in type_aliases for alias in entry['aliases']}
        fuzzy_match_choices = {alias: entry['canonical_name'] for entry in type_aliases for alias in entry['aliases']}

        # 1. Check for a case-insensitive exact match first for efficiency
        if entity_name.lower() in exact_match_map:
            results.append({'entity': entity, 'candidates': [exact_match_map[entity_name.lower()]]})
            continue

        # 2. If no exact match, perform fuzzy matching
        partial_matches = process.extractBests(entity_name, fuzzy_match_choices.keys(), scorer=fuzz.partial_ratio, score_cutoff=80, limit=5)
        token_set_matches = process.extractBests(entity_name, fuzzy_match_choices.keys(), scorer=fuzz.token_set_ratio, score_cutoff=80, limit=5)

        candidate_names: Set[str] = set()
        for match_tuple in partial_matches + token_set_matches:
            matched_alias = match_tuple[0]
            canonical_name = fuzzy_match_choices[matched_alias]
            candidate_names.add(canonical_name)
        
        results.append({'entity': entity, 'candidates': sorted(list(candidate_names))})
        
    return results


def _Update_alias(matched_entities: List[Dict], alias_path: str):
    """
    Uses an LLM to disambiguate candidates and updates the alias file synchronously.

    Args:
        matched_entities: The output from _fuzzy_match.
        alias_path: The file path for the aliases.json file.
    """
    if not os.path.exists(alias_path):
        aliases_data = {}
    else:
        with open(alias_path, 'r', encoding='utf-8') as f:
            aliases_data = json.load(f)

    system_prompt = """You are a precision-focused entity resolution expert. Your task is to analyze an incoming `entity_name` and determine if it refers to the same underlying entity as one of the names in a provided `candidate_names` list.

You must follow the rules below with strict adherence and respond **ONLY** with a single, valid JSON object.

#### **Core Logic**

1. **Analyze**: Given an `entity_name` and a list of `candidate_names`, analyze if there exist any name in the `candidate_names` that refers to **EXACTLY SAME ENTITY* as `entity_name` does.
2. **Respond**: Structure your conclusion in a JSON object with the three keys defined below.

#### **Output Schema and Field Definitions**

Your entire response must be a JSON object containing the following three keys:

* `entity_name` **(string)**
  * **Definition**: This is the original entity name that is being evaluated.
  * **Rule**: You MUST return this value as an exact, unmodified copy from the input.

* `matches_with` **(string | null)**
  * **Definition**: This field identifies any name from `candidate_names` list that refers to **EXACTLY SAME ENTITY* as `entity_name` does .
  * **Rules**:
    * If you determine a match exists, set this field's value to the matching string from the `candidate_names` list.
    * If no candidate is a match, this field **MUST** be `null`.

* `preferred_name` **(string | null)**
  * **Definition**: This field represents the ideal, most complete, and canonical name for the resolved entity.
  * **Rules**:
    * If `matches_with` is `null`, this field **MUST** also be `null`.
    * If a match was found, determine the most descriptive name by comparing the `entity_name` and the `matches_with`. This may require you to **intelligently combine** information from both strings.

#### **Examples**

* **Example 1: Combining Names**
  * **Input**: `{"entity_name": "GeForce RTX", "candidate_names": ["NVIDIA GeForce", "Radeon RX"]}`
  * **Output**: `{"entity_name": "GeForce RTX", "matches_with": "NVIDIA GeForce", "preferred_name": "NVIDIA GeForce RTX"}`
* **Example 2: Selecting the More Complete Name**

  * **Input**: `{"entity_name": "AMD", "candidate_names": ["Intel Corp", "Advanced Micro Devices, Inc."]`
  * **Output**: `{"entity_name": "AMD", "matches_with": "Advanced Micro Devices, Inc.", "preferred_name": "Advanced Micro Devices, Inc."}`
* **Example 3: No Match Found**

  * **Input**: `{"entity_name": "B100", "candidate_names": ["NVIDIA A100", "NVIDIA H100"]}`
  * **Output**: `{"entity_name": "B100", "matches_with": null, "preferred_name": null}`"""
    
    entities_without_candidates = []

    for item in matched_entities:
        entity = item['entity']
        candidates = item['candidates']
        
        if not candidates:
            entities_without_candidates.append(entity)
            continue
        
        # Check for an existing exact match to avoid unnecessary LLM calls
        exact_match_found = False
        for entry in aliases_data.get(entity['type'], []):
            if entry['canonical_name'] in candidates:
                if entity['name'].lower() in [alias.lower() for alias in entry['aliases']]:
                    exact_match_found = True
                    print(f"Skipping LLM for exact match: '{entity['name']}' -> '{entry['canonical_name']}'")
                    if entity['name'] not in entry['aliases']:
                        entry['aliases'].append(entity['name'])
                    break
        if exact_match_found:
            continue

        user_prompt_data = {"entity_name": entity['name'], "candidate_names": candidates}
        user_prompt = json.dumps(user_prompt_data)
        
        # Synchronous API call for each entity that needs disambiguation
        llm_result = call_gemini_api(system_prompt=system_prompt,
                                     user_prompt=user_prompt,
                                     schema=DisambiguationOutput.model_json_schema(),
                                     model_name= "gemini-2.5-flash-lite",
                                     thinking_token=1000)

        # Process the result immediately
        if llm_result and 'content' in llm_result:
            res_content = llm_result['content']
            original_name = res_content['entity_name']
            
            # Find the original entity details (like its type)
            original_entity = next((item['entity'] for item in matched_entities if item['entity']['name'] == original_name), None)
            if not original_entity: continue
            entity_type = original_entity['type']

            if res_content['matches_with']:
                print(f"LLM matched '{original_name}' -> '{res_content['matches_with']}'")
                for entry in aliases_data.get(entity_type, []):
                    if entry['canonical_name'] == res_content['matches_with']:
                        if res_content.get('preferred_name'):
                            entry['canonical_name'] = res_content['preferred_name']
                        
                        if original_name not in entry['aliases']:
                            entry['aliases'].append(original_name)
                        if res_content.get('preferred_name') and res_content['preferred_name'] not in entry['aliases']:
                             entry['aliases'].append(res_content['preferred_name'])
                        break
            else:
                print(f"LLM found no match for '{original_name}'. Adding as new.")
                if entity_type not in aliases_data:
                    aliases_data[entity_type] = []
                if not any(e['canonical_name'] == original_name for e in aliases_data[entity_type]):
                    aliases_data[entity_type].append({
                        'canonical_name': original_name, 
                        'aliases': [original_name],
                        'id': original_name  # Set id to the original entity name when first created
                    })

    # Process entities that had no candidates to begin with
    for entity in entities_without_candidates:
        entity_type = entity['type']
        if entity_type not in aliases_data:
            aliases_data[entity_type] = []
        
        if not any(e['canonical_name'] == entity['name'] for e in aliases_data[entity_type]):
            print(f"Adding new entity (no candidates): '{entity['name']}'")
            aliases_data[entity_type].append({
                'canonical_name': entity['name'], 
                'aliases': [entity['name']],
                'id': entity['name']  # Set id to the original entity name when first created
            })

    with open(alias_path, 'w', encoding='utf-8') as f:
        json.dump(aliases_data, f, indent=2)


def run_normalization_pass(
    entities: List[Dict], 
    alias_path_others: str,
    company_map_path: Path,
    sec_ticker_path: Path,
    company_review_output_path: Path
):
    """
    Runs the full entity normalization pipeline, separating company logic
    from other entity types.

    Args:
        entities: A list of all unique entity dictionaries from a batch.
        alias_path_others: The file path for the alias map for non-company entities.
        company_map_path: The file path for the company_canonical_map.json.
        sec_ticker_path: The file path for the SEC tickers.json.
        company_review_output_path: The file path to save the company resolution review file.
    """
    log.info(f"\n--- Starting Normalization Pass for {len(entities)} total entities ---")

    # --- Step 1: Separate Entities by Type ---
    company_entities = [entity for entity in entities if entity.get('type') == 'Company']
    other_entities = [entity for entity in entities if entity.get('type') != 'Company']
    log.info(f"Separated entities: {len(company_entities)} Companies, {len(other_entities)} Others.")

    # --- Step 2: Process Company Entities (Your Part) ---
    if company_entities:
        log.info("--- Processing Company Entities ---")
        # Initialize your normalizer
        company_normalizer = CompanyNormalizer(sec_ticker_path, company_map_path)
        
        company_resolution_map = {}
        
        # Iterate and normalize each company entity
        for entity in company_entities:
            entity_name = entity.get("name")
            if entity_name:
                resolved_id = company_normalizer.normalize_company(entity_name)
                company_resolution_map[entity_name] = resolved_id
                log.info(f"Resolved Company '{entity_name}' -> '{resolved_id}'")

        # Save the review file
        try:
            with open(company_review_output_path, 'w', encoding='utf-8') as f:
                json.dump(company_resolution_map, f, indent=2, ensure_ascii=False)
            log.info(f"Company resolution review map saved to: {company_review_output_path}")
        except IOError as e:
            log.error(f"Failed to save company review file. Error: {e}")

        # IMPORTANT: Save the updated canonical map with any new learnings
        company_normalizer.save_map()
        log.info("--- Finished Processing Company Entities ---")

    # --- Step 3: Process Other Entities ---
    if other_entities:
        log.info("--- Processing Other Entity Types ---")
        # This part calls your teammate's functions as they designed them.
        matched_entities = _fuzzy_match(other_entities, alias_path_others)
        _Update_alias(matched_entities, alias_path_others)
        log.info("--- Finished Processing Other Entity Types ---")

    log.info("--- Normalization Pass Complete ---")
