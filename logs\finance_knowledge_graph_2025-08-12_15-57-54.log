2025-08-12 15:57:54,959 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_15-57-54.log
2025-08-12 15:57:54,962 - Finance_Knowledge_Graph - INFO - [normalization_service.py:119] - --- Running NormalizationService Test ---
2025-08-12 15:57:54,963 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 15:57:54,963 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 15:57:54,968 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 15:57:54,972 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'GeForce RTX 5090' of type 'Product' with ID 'product_geforce_rtx_5090'.
