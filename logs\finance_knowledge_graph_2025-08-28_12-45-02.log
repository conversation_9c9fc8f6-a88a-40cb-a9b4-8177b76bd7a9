2025-08-28 12:45:02,746 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-28_12-45-02.log
2025-08-28 12:45:09,079 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:203] - ========================================================
2025-08-28 12:45:09,079 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:204] - = Starting Unified Entity Resolution & Cleaning Pipeline =
2025-08-28 12:45:09,080 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:205] - ========================================================
2025-08-28 12:45:09,081 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:214] - Selected latest raw file for processing: clean_relationships_2025-08-27_22-07-42.jsonl
2025-08-28 12:45:09,081 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:27] - Initializing Entity Resolution Pipeline...
2025-08-28 12:45:09,082 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:45] - Collecting all unique entities and all relationships from clean_relationships_2025-08-27_22-07-42.jsonl...
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:59] - Collected 180 unique entities and loaded 318 relationships.
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:34] - Entity Resolution Pipeline initialized.
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:70] - --- Starting Pass 1: The Learning Pass ---
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [other_norm.py:433] - 
--- Starting Normalization Pass for 180 total entities ---
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [other_norm.py:438] - Separated entities: 42 Companies, 138 Others.
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [other_norm.py:442] - --- Processing Company Entities ---
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:18] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-28 12:45:09,118 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:42] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-28 12:45:09,198 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:57] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-28 12:45:09,198 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - CompanyNormalizer initialized with 12 canonical companies.
2025-08-28 12:45:09,198 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'NVIDIA Corporation' -> 'NVDA'
2025-08-28 12:45:09,198 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Advanced Micro Devices, Inc.' -> 'AMD'
2025-08-28 12:45:09,232 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Huawei Technologies Co. Ltd.' found in local map: 'MHUA' (Score: 98.40)
2025-08-28 12:45:09,232 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Huawei Technologies Co. Ltd.' -> 'MHUA'
2025-08-28 12:45:09,232 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Intel Corporation' -> 'INTC'
2025-08-28 12:45:09,236 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Alibaba Group' found in local map: 'BABA' (Score: 93.60)
2025-08-28 12:45:09,237 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Alibaba Group' -> 'BABA'
2025-08-28 12:45:09,428 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Alphabet Inc.' found in SEC master list: 'Alphabet Inc.' (Score: 100.00)
2025-08-28 12:45:09,428 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'GOOGL'
2025-08-28 12:45:09,428 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Alphabet Inc.' -> 'GOOGL'
2025-08-28 12:45:09,591 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Amazon, Inc.' found in SEC master list: 'AAON, INC.' (Score: 89.70)
2025-08-28 12:45:09,591 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'AAON'
2025-08-28 12:45:09,591 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Amazon, Inc.' -> 'AAON'
2025-08-28 12:45:09,758 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Baidu, Inc.' found in SEC master list: 'Baidu, Inc.' (Score: 100.00)
2025-08-28 12:45:09,758 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'BIDU'
2025-08-28 12:45:09,758 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Baidu, Inc.' -> 'BIDU'
2025-08-28 12:45:09,758 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Microsoft Corporation' -> 'INTC'
2025-08-28 12:45:09,924 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Ambarella, Inc.' found in SEC master list: 'AMBARELLA INC' (Score: 96.20)
2025-08-28 12:45:09,924 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'AMBA'
2025-08-28 12:45:09,924 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Ambarella, Inc.' -> 'AMBA'
2025-08-28 12:45:10,102 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Broadcom Inc.' found in SEC master list: 'Broadcom Inc.' (Score: 100.00)
2025-08-28 12:45:10,102 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'AVGO'
2025-08-28 12:45:10,103 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Broadcom Inc.' -> 'AVGO'
2025-08-28 12:45:10,273 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Qualcomm Incorporated'. No match in local map or SEC list.
2025-08-28 12:45:10,273 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Qualcomm Incorporated' -> 'UNRESOLVED_COMPANY_QUALCOMM_INCORPORATED'
2025-08-28 12:45:10,463 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Renesas Electronics Corporation'. No match in local map or SEC list.
2025-08-28 12:45:10,463 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Renesas Electronics Corporation' -> 'UNRESOLVED_COMPANY_RENESAS_ELECTRONICS_CORPORATION'
2025-08-28 12:45:10,664 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Samsung Electronics Co., Ltd.'. No match in local map or SEC list.
2025-08-28 12:45:10,665 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Samsung Electronics Co., Ltd.' -> 'UNRESOLVED_COMPANY_SAMSUNG_ELECTRONICS_CO,_LTD'
2025-08-28 12:45:10,827 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Tesla, Inc.' found in SEC master list: 'Tesla, Inc.' (Score: 100.00)
2025-08-28 12:45:10,828 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'TSLA'
2025-08-28 12:45:10,828 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Tesla, Inc.' -> 'TSLA'
2025-08-28 12:45:11,006 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Arista Networks, Inc.' found in SEC master list: 'Arista Networks, Inc.' (Score: 100.00)
2025-08-28 12:45:11,006 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'ANET'
2025-08-28 12:45:11,006 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Arista Networks, Inc.' -> 'ANET'
2025-08-28 12:45:11,173 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Cisco Systems, Inc.' found in SEC master list: 'CISCO SYSTEMS, INC.' (Score: 100.00)
2025-08-28 12:45:11,173 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'CSCO'
2025-08-28 12:45:11,173 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Cisco Systems, Inc.' -> 'CSCO'
2025-08-28 12:45:11,384 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Hewlett Packard Enterprise Company' found in SEC master list: 'Hewlett Packard Enterprise Co' (Score: 95.90)
2025-08-28 12:45:11,384 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'HPE'
2025-08-28 12:45:11,384 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Hewlett Packard Enterprise Company' -> 'HPE'
2025-08-28 12:45:11,571 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Lumentum Holdings, Inc.' found in SEC master list: 'Lumentum Holdings Inc.' (Score: 98.10)
2025-08-28 12:45:11,572 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'LITE'
2025-08-28 12:45:11,572 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Lumentum Holdings, Inc.' -> 'LITE'
2025-08-28 12:45:11,767 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Marvell Technology Group' found in SEC master list: 'Marvell Technology, Inc.' (Score: 87.20)
2025-08-28 12:45:11,768 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'MRVL'
2025-08-28 12:45:11,768 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Marvell Technology Group' -> 'MRVL'
2025-08-28 12:45:11,962 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Samsung Electronics Co. Ltd.'. No match in local map or SEC list.
2025-08-28 12:45:11,962 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Samsung Electronics Co. Ltd.' -> 'UNRESOLVED_COMPANY_SAMSUNG_ELECTRONICS_CO_LTD'
2025-08-28 12:45:11,962 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Lumentum Holdings Inc.' -> 'LITE'
2025-08-28 12:45:11,962 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Marvell Technology, Inc.' -> 'MRVL'
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Mellanox Technologies, Ltd.' found in SEC master list: 'TAT TECHNOLOGIES LTD' (Score: 86.00)
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'TATT'
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Mellanox Technologies, Ltd.' -> 'TATT'
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Mellanox Technologies' found in local map: 'TATT' (Score: 97.60)
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Mellanox Technologies' -> 'TATT'
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'NVIDIA CORP' -> 'NVDA'
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Taiwan Semiconductor Manufacturing Company Limited' found in local map: 'TSM' (Score: 98.20)
2025-08-28 12:45:12,165 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Taiwan Semiconductor Manufacturing Company Limited' -> 'TSM'
2025-08-28 12:45:12,186 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Micron Technology, Inc.' found in local map: 'MRVL' (Score: 81.70)
2025-08-28 12:45:12,187 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Micron Technology, Inc.' -> 'MRVL'
2025-08-28 12:45:12,369 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'SK hynix Inc.'. No match in local map or SEC list.
2025-08-28 12:45:12,369 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'SK hynix Inc.' -> 'UNRESOLVED_COMPANY_SK_HYNIX_INC'
2025-08-28 12:45:12,571 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Hon Hai Precision Industry Co., Ltd.'. No match in local map or SEC list.
2025-08-28 12:45:12,571 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Hon Hai Precision Industry Co., Ltd.' -> 'UNRESOLVED_COMPANY_HON_HAI_PRECISION_INDUSTRY_CO,_LTD'
2025-08-28 12:45:12,571 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Wistron Corporation' found in local map: 'INTC' (Score: 81.20)
2025-08-28 12:45:12,571 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Wistron Corporation' -> 'INTC'
2025-08-28 12:45:12,744 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Fabrinet' found in SEC master list: 'Fabrinet' (Score: 100.00)
2025-08-28 12:45:12,744 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'FN'
2025-08-28 12:45:12,744 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Fabrinet' -> 'FN'
2025-08-28 12:45:12,900 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Third party'. No match in local map or SEC list.
2025-08-28 12:45:12,900 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Third party' -> 'UNRESOLVED_COMPANY_THIRD_PARTY'
2025-08-28 12:45:13,073 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer A'. No match in local map or SEC list.
2025-08-28 12:45:13,073 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer A' -> 'UNRESOLVED_COMPANY_CUSTOMER_A'
2025-08-28 12:45:13,240 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer B'. No match in local map or SEC list.
2025-08-28 12:45:13,240 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer B' -> 'UNRESOLVED_COMPANY_CUSTOMER_B'
2025-08-28 12:45:13,421 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer C'. No match in local map or SEC list.
2025-08-28 12:45:13,421 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer C' -> 'UNRESOLVED_COMPANY_CUSTOMER_C'
2025-08-28 12:45:13,572 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer D'. No match in local map or SEC list.
2025-08-28 12:45:13,572 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer D' -> 'UNRESOLVED_COMPANY_CUSTOMER_D'
2025-08-28 12:45:13,740 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer E'. No match in local map or SEC list.
2025-08-28 12:45:13,740 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer E' -> 'UNRESOLVED_COMPANY_CUSTOMER_E'
2025-08-28 12:45:13,923 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer A'. No match in local map or SEC list.
2025-08-28 12:45:13,923 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer A' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_A'
2025-08-28 12:45:14,104 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer B'. No match in local map or SEC list.
2025-08-28 12:45:14,104 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer B' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_B'
2025-08-28 12:45:14,312 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer C'. No match in local map or SEC list.
2025-08-28 12:45:14,313 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer C' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_C'
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Acquired Company'. No match in local map or SEC list.
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Acquired Company' -> 'UNRESOLVED_COMPANY_ACQUIRED_COMPANY'
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - INFO - [other_norm.py:460] - Company resolution review map saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\review_company_nodes.json
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:84] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:96] - Successfully saved 25 companies to the map.
2025-08-28 12:45:14,489 - Finance_Knowledge_Graph - INFO - [other_norm.py:466] - --- Finished Processing Company Entities ---
2025-08-28 12:45:14,501 - Finance_Knowledge_Graph - INFO - [other_norm.py:470] - --- Processing Other Entity Types ---
2025-08-28 12:48:43,010 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:81] - The Learning Pass failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 473, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 358, in _Update_alias
    llm_result = call_gemini_api(system_prompt=system_prompt,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 160, in call_gemini_api
    json.loads(full_response_text) if schema else full_response_text
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '
TypeError: the JSON object must be str, bytes or bytearray, not NoneType
2025-08-28 12:48:43,103 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:248] - The entity resolution pipeline failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 232, in main
    pipeline.run_learning_pass()
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 473, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 358, in _Update_alias
    llm_result = call_gemini_api(system_prompt=system_prompt,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 160, in call_gemini_api
    json.loads(full_response_text) if schema else full_response_text
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\__init__.py", line 339, in loads
    raise TypeError(f'the JSON object must be str, bytes or bytearray, '
TypeError: the JSON object must be str, bytes or bytearray, not NoneType
