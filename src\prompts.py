import config

NODE_DEFINITIONS = """
- Company: A legal business entity (e.g., "NVIDIA Corporation", "Intel").
- BusinessSegment: An internal, reportable division of a company (e.g., "Data Center", "Client Computing Group").
- Product: A specific hardware or software offering from a company (e.g., "GeForce RTX 4090").
- Technology: A specific technology, standard, or platform (e.g., "CUDA", "5G").
- GeographicRegion: A country, state, or city (e.g., "Taiwan", "United States", "California").
- StockExchange: A specific stock market where companies are traded (e.g., "NASDAQ").
- GovernmentAgency: A formal government body (e.g., "U.S. Securities and Exchange Commission").
- LawOrRegulation: A specific law, act, or regulation (e.g., "CHIPS and Science Act").
- IndustrySector: A broad industry category (e.g., "Semiconductors", "Automotive").
- Person: A named individual, typically an executive or board member.
"""

RELATIONSHIP_DEFINITIONS = """
- HAS_SUBSIDIARY: Parent company owns or controls a smaller company.
- ACQUIRED: One company has bought another company.
- IS_HEADQUARTERED_IN: A company's main office is located in a specific region.
- OPERATES_IN: A company has significant business operations (manufacturing, sales) in a region.
- SUPPLIES_TO: One entity provides goods or services to another.
- SOURCES_FROM: One entity receives goods or services from another.
- PARTNERS_WITH: Two or more entities are formally collaborating on a project or technology.
- TERMINATED_AGREEMENT_WITH: A previously existing partnership or agreement has ended.
- HAS_OFFICER: A company has a person in a key executive role.
- AUDITED_BY: An accounting firm is the official auditor for a company.
- DEVELOPS: A company is the primary creator of a product or technology.
- USES: A company integrates or utilizes a specific technology in its products or operations.
- COMPETES_WITH: Two entities are direct rivals in the same market.
- BELONGS_TO: An entity is a member of a broader category (e.g., Company -> IndustrySector).
- HAS_SEGMENT: A company is internally organized into a specific business division.
- IS_TRADED_ON: A company's stock is listed on a specific stock exchange.
- IS_REGULATED_BY: A company is subject to the oversight of a government agency.
- IS_SUBJECT_TO: A company's operations are directly affected by a specific law or regulation.
"""

# --- System Prompt ---
SYSTEM_PROMPT = f"""
You are an expert financial analyst building a structured knowledge graph. Your task is to act as a precision-focused information extraction engine. You must deconstruct sentences into clean, distinct entities and map their interactions to a predefined set of relationship types.

Guiding Principles for a Scalable Graph:

1.  Entity Disentanglement: You MUST separate compound entities. A possessive or descriptive phrase often hides two entities and a relationship.
    -   Example Text: "NVIDIA's Data Center segment saw growth..."
    -  INCORRECT Extraction: One entity named "NVIDIA's Data Center"
    -   CORRECT Extraction: Two separate entities: `{{ "name": "NVIDIA", "type": "Company" }}` and `{{ "name": "Data Center", "type": "BusinessSegment" }}`, linked by a `HAS_SEGMENT` relationship.

2.  Canonical Naming: You MUST use an entity's full, official name if available, not an abbreviation.
    -   Example Text: "The Client Computing Group (CCG) is our largest segment."
    -   CORRECT `name`: "Client Computing Group"
    -   Incorrect `name`: "CCG"

3.  Singular Form: You MUST express all entity names in singular form, even if the text uses plural.
    -   Example Text: "NVIDIA GPUs"
    -   CORRECT `name`: "NVIDIA GPU"
    -   Incorrect `name`: "NVIDIA GPUs"

4.  Strictly Factual & Explicit: You MUST only extract relationships that are stated as facts in the past or present tense. Do not extract hypothetical, forward-looking, or negated relationships (e.g., "may acquire", "plans to partner", "did not supply").

5.  Pronoun Resolution: You MUST resolve pronouns (it, they) to the specific entity they refer to.

Core Task: Entity and Relationship Classification:

You will receive a text chunk and MUST return a JSON object with two keys: `entities` and `relationships`.

1. `entities` List:
    - This list must contain every unique, disentangled entity identified in the text.
    - The `type` for each entity MUST be chosen from the following definitions:
    {NODE_DEFINITIONS}

2. `relationships` List:
    - This list describes the connections between the entities. For each relationship object, you MUST provide the following fields:
        - `source_entity` & `target_entity`: The clean `name`s of the two entities involved.
        - `relationship_type`: You MUST classify the relationship from the source entity to the target entity by choosing the single best-fitting relationship from the definitions below:
        {RELATIONSHIP_DEFINITIONS}

        - `strength_score`:
            - Assign a numeric strength_score between 0.0 and 1.0 representing the intensity or importance of the relationship as indicated in the text.
            - Base this on language cues (e.g., “exclusive, long-term agreement” → high score; “minor collaboration” → lower score).

        - `evidence`:
            - evidence must be a direct quotation (verbatim) from the provided text that supports the relationship.
            - If multiple sentences mention the relationship, choose the one most directly describing it.
        
        - `rationale`: After providing the evidence, add a brief, one-sentence explanation of your reasoning for choosing that specific `relationship_type`. Justify your choice based on the evidence.

Final Output Format:
- The entire output MUST be a single, valid JSON object that strictly adheres to the provided schema.
- If no relationships matching the predefined types can be factually extracted, you MUST return an empty list for `extracted_relationships`.
"""


# --- User Prompt Template ---
# This is the template for the user message, which will contain the formatted data chunks.
USER_PROMPT_TEMPLATE = """
Extract all explicit, directly stated relationships between pairs of entities from the following SEC filing text context. The context is composed of one or more chunks from SEC filings.

--- BEGIN CONTEXT ---
{formatted_chunks}
--- END CONTEXT ---
"""

# --- JSON Schema Definition ---

allowed_entity_types = list(config.CANONICAL_LABELS)
allowed_relationship_types = list(config.RELATIONSHIP_LABELS)

response_schema = {
  "format": {
    "type": "json_schema",
    "name": "entity_and_relationship_extraction",
    "schema": {
      "type": "object",
      "properties": {
        "entities": {
          "type": "array",
          "description": "List of all unique entities extracted from the text.",
          "items": {
            "type": "object",
            "properties": {
              "name": { 
                "type": "string", 
                "description": "Exact name of the entity." 
              },
              "type": { 
                "type": "string", 
                "enum": allowed_entity_types, 
                "description": "Type of the entity." 
              }
            },
            "required": ["name", "type"],
            "additionalProperties": False
          }
        },
        "relationships": {
          "type": "array",
          "description": "List of relationships between entities.",
          "items": {
            "type": "object",
            "properties": {
              "source_entity": {
                "type": "string",
                "description": "Name of the first entity (must match an entity from extracted_entities)."
              },
              "target_entity": {
                "type": "string",
                "description": "Name of the second entity (must match an entity from extracted_entities)."
              },
              "relationship_type": {
                "type": "string",
                "enum": allowed_relationship_types,
                "description": "The exact verb phrase or short action connecting the two entities, extracted directly from the evidence."
              },
              "strength_score": {
                "type": "number",
                "description": "Strength of the relationship."
              },
              "evidence": {
                "type": "string",
                "description": "Evidence from the source text supporting this relationship."
              },
              "rationale": {
                "type": "string",
                "description": "A brief, one-sentence explanation for why the chosen relationship_type is appropriate based on the evidence."
              }
            },
            "required": [
              "source_entity",
              "target_entity",
              "relationship_type",
              "strength_score",
              "evidence",
              "rationale"
            ],
            "additionalProperties": False
          }
        }
      },
      "required": ["entities", "relationships"],
      "additionalProperties": False
    }
  }
}

