from logger import log
import config
from src.pipeline import KnowledgeGraphPipeline

def main():
    """
    The main entry point for the application.
    """
    log.info("=====================================================")
    log.info("      STARTING ENTITY EXTRACTION PIPELINE      ")
    log.info("=====================================================")

    # Ensure all necessary directories exist before starting
    config.ensure_directories()


    companies = ["TEXAS INSTRUMENTS INC"]


    try:
        # Create an instance of our main pipeline class
        pipeline = KnowledgeGraphPipeline() 
        
        # Run the pipeline with the list of companies from our config file
        pipeline.run(companies=companies)

    except Exception as e:
        log.error("A fatal error occurred in the main pipeline execution.", exc_info=True)
    finally:
        log.info("=====================================================")
        log.info("      PIPELINE EXECUTION FINISHED                 ")
        log.info("=====================================================")

if __name__ == "__main__":
    main()