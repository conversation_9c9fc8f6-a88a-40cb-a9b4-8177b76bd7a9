2025-08-25 19:07:00,742 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-25_19-07-00.log
2025-08-25 19:07:02,912 - Finance_Knowledge_Graph - INFO - [ingestion.py:170] - ========================================================
2025-08-25 19:07:02,912 - Finance_Knowledge_Graph - INFO - [ingestion.py:171] - = Starting Neo4j Ingestion Pipeline                    =
2025-08-25 19:07:02,912 - Finance_Knowledge_Graph - INFO - [ingestion.py:172] - ========================================================
2025-08-25 19:07:02,925 - Finance_Knowledge_Graph - INFO - [ingestion.py:180] - Selected latest graph file for ingestion: cleaned_llm_output_2025-08-24_16-57-42.json
2025-08-25 19:07:02,925 - Finance_Knowledge_Graph - INFO - [ingestion.py:23] - Connecting to Neo4j database at neo4j+s://aab151a2.databases.neo4j.io...
2025-08-25 19:07:04,363 - Finance_Knowledge_Graph - INFO - [ingestion.py:27] - Neo4j connection successful.
2025-08-25 19:07:04,363 - Finance_Knowledge_Graph - INFO - [ingestion.py:53] - Setting up database constraints...
2025-08-25 19:07:04,429 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'LawOrRegulation' is set.
2025-08-25 19:07:04,493 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'IndustrySector' is set.
2025-08-25 19:07:04,544 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'StockExchange' is set.
2025-08-25 19:07:04,610 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GeographicRegion' is set.
2025-08-25 19:07:04,666 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Technology' is set.
2025-08-25 19:07:04,733 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Product' is set.
2025-08-25 19:07:04,799 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Company' is set.
2025-08-25 19:07:04,858 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Person' is set.
2025-08-25 19:07:04,914 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GovernmentAgency' is set.
2025-08-25 19:07:04,975 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'BusinessSegment' is set.
2025-08-25 19:07:04,975 - Finance_Knowledge_Graph - INFO - [ingestion.py:149] - --- Starting Neo4j Ingestion Process ---
2025-08-25 19:07:04,975 - Finance_Knowledge_Graph - INFO - [ingestion.py:76] - Ingesting 9 nodes in batches...
2025-08-25 19:07:05,098 - Finance_Knowledge_Graph - INFO - [ingestion.py:98] - Ingested batch of 9 nodes.
2025-08-25 19:07:05,098 - Finance_Knowledge_Graph - INFO - [ingestion.py:100] - Node ingestion complete.
2025-08-25 19:07:05,098 - Finance_Knowledge_Graph - INFO - [ingestion.py:114] - Ingesting 10 relationships in batches...
2025-08-25 19:07:05,473 - Finance_Knowledge_Graph - INFO - [ingestion.py:141] - Ingested batch of 10 relationships.
2025-08-25 19:07:05,473 - Finance_Knowledge_Graph - INFO - [ingestion.py:143] - Relationship ingestion complete.
2025-08-25 19:07:05,473 - Finance_Knowledge_Graph - INFO - [ingestion.py:165] - --- Neo4j Ingestion Process Finished ---
2025-08-25 19:07:05,473 - Finance_Knowledge_Graph - INFO - [ingestion.py:38] - Closing Neo4j connection.
2025-08-25 19:07:05,473 - Finance_Knowledge_Graph - INFO - [ingestion.py:198] - Ingestion pipeline completed.
