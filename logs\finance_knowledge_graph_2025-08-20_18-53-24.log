2025-08-20 18:53:24,298 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-20_18-53-24.log
2025-08-20 18:53:24,419 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:176] - ========================================================
2025-08-20 18:53:24,419 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:177] - = Starting Entity Resolution Pipeline                  =
2025-08-20 18:53:24,428 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:178] - ========================================================
2025-08-20 18:53:24,428 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:188] - Selected latest raw file for processing: sample_relationship.jsonl
2025-08-20 18:53:24,428 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:40] - Initializing Entity Resolution Pipeline...
2025-08-20 18:53:24,428 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:22] - Initializing CompanyNormalizer...
2025-08-20 18:53:24,436 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-20 18:53:24,469 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:54] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 18:53:24,469 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:57] - Company map file is empty, Starting fresh
2025-08-20 18:53:24,469 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:34] - CompanyNormalizer initialized successfully.
2025-08-20 18:53:24,469 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:49] - Entity Resolution Pipeline initialized.
2025-08-20 18:53:24,469 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:74] - Starting processing for input file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\sample_relationship.jsonl
2025-08-20 18:53:24,649 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:139] - Match for 'NVIDIA Corporation' found in SEC master list: 'NVIDIA CORP' (Score: 83.2)
2025-08-20 18:53:24,649 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:172] - Creating new entry in company map for 'NVDA'
2025-08-20 18:53:24,836 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:139] - Match for 'Intel Corporation' found in SEC master list: 'INTEL CORP' (Score: 81.8)
2025-08-20 18:53:24,836 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:172] - Creating new entry in company map for 'INTC'
2025-08-20 18:53:25,065 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:139] - Match for 'Advanced Micro Devices' found in SEC master list: 'ADVANCED MICRO DEVICES INC' (Score: 98.4)
2025-08-20 18:53:25,065 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:172] - Creating new entry in company map for 'AMD'
2025-08-20 18:53:25,065 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:157] - Processing complete. Found 3 unique nodes and 2 relationships.
2025-08-20 18:53:25,065 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:158] - Clean graph data saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\graph_ready_output_2025-08-20_18-53-24.json
2025-08-20 18:53:25,065 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:69] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 18:53:25,073 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:168] - All canonical maps have been saved.
2025-08-20 18:53:25,073 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:209] - Entity resolution pipeline completed successfully.
