{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f022ae2b", "metadata": {}, "outputs": [], "source": ["import sys\n", "PROJECT_ROOT = r\"c:\\Users\\<USER>\\Desktop\\Autowiz\\Fin_knowledge_graph\\new_approach_11_08\"\n", "if PROJECT_ROOT not in sys.path:\n", "    sys.path.insert(0, PROJECT_ROOT)\n", "\n", "import src.prompts as prompts"]}, {"cell_type": "code", "execution_count": 2, "id": "e65a09cd", "metadata": {}, "outputs": [], "source": ["import config"]}, {"cell_type": "code", "execution_count": 5, "id": "e7511619", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Company',\n", " 'Country',\n", " 'Government Agency',\n", " 'Industry Sector',\n", " 'Person',\n", " 'Product',\n", " 'Technology'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["config.CANONICAL_LABELS"]}, {"cell_type": "code", "execution_count": 3, "id": "d39965f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'format': {'type': 'json_schema',\n", "  'name': 'entity_and_relationship_extraction',\n", "  'schema': {'type': 'object',\n", "   'properties': {'extracted_relationships': {'type': 'array',\n", "     'items': {'type': 'object',\n", "      'properties': {'entity_1': {'type': 'object',\n", "        'properties': {'name': {'type': 'string',\n", "          'description': 'Exact name of the first entity.'},\n", "         'type': {'type': 'string',\n", "          'enum': ['Government Agency',\n", "           'Country',\n", "           'Technology',\n", "           'Person',\n", "           'Product',\n", "           'Industry Sector',\n", "           'Other',\n", "           'Company'],\n", "          'description': 'Type of the first entity.'}},\n", "        'required': ['name', 'type'],\n", "        'additionalProperties': False},\n", "       'entity_2': {'type': 'object',\n", "        'properties': {'name': {'type': 'string',\n", "          'description': 'Exact name of the second entity.'},\n", "         'type': {'type': 'string',\n", "          'enum': ['Government Agency',\n", "           'Country',\n", "           'Technology',\n", "           'Person',\n", "           'Product',\n", "           'Industry Sector',\n", "           'Other',\n", "           'Company'],\n", "          'description': 'Type of the second entity.'}},\n", "        'required': ['name', 'type'],\n", "        'additionalProperties': False},\n", "       'relationship_type': {'type': 'string',\n", "        'description': \"The exact verb phrase or short action that connects the two entities, extracted directly from the evidence. Examples: 'partners with', 'competes against', 'is a supplier for', 'acquired', 'manufactures for'.\"},\n", "       'strength_score': {'type': 'number',\n", "        'description': 'Strength of the relationship .'},\n", "       'evidence': {'type': 'string',\n", "        'description': 'Evidence from the source text supporting this relationship.'},\n", "       'source': {'type': 'object',\n", "        'description': '<PERSON><PERSON><PERSON> about the source document section.',\n", "        'properties': {'report_type': {'type': 'string',\n", "          'description': \"The filing type, e.g., '10-K' or '10-Q'.\"},\n", "         'period_of_report': {'type': 'string',\n", "          'format': 'date',\n", "          'description': 'The period of the report in YYYY-MM-DD format.'},\n", "         'section': {'type': 'string',\n", "          'description': 'The section of the filing where this information appears.'},\n", "         'source_url': {'type': 'string',\n", "          'description': 'The exact URL to the filing section.'}},\n", "        'required': ['report_type',\n", "         'period_of_report',\n", "         'section',\n", "         'source_url'],\n", "        'additionalProperties': False}},\n", "      'required': ['entity_1',\n", "       'entity_2',\n", "       'relationship_type',\n", "       'strength_score',\n", "       'evidence',\n", "       'source'],\n", "      'additionalProperties': False}}},\n", "   'required': ['extracted_relationships'],\n", "   'additionalProperties': False}}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["prompts.response_schema"]}, {"cell_type": "code", "execution_count": 1, "id": "46190656", "metadata": {}, "outputs": [], "source": ["DATE_PATTERN = '%Y-%m-%d'\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e1189b52", "metadata": {}, "outputs": [], "source": ["import math\n", "from typing import Dict, Union\n", "from datetime import date, datetime, timedelta\n", "def adjust_fiscal_date_ending(date_: Union[str, datetime]) -> str:\n", "        date_of_interest: Union[date, datetime] = datetime.strptime(date_, DATE_PATTERN) \\\n", "            if isinstance(date_, str) else date_\n", "        first_of_this_month = date_of_interest.replace(day=1)\n", "        last_of_previous_month = first_of_this_month - <PERSON><PERSON><PERSON>(days=1)\n", "        next_month = first_of_this_month + <PERSON><PERSON><PERSON>(days=32)\n", "        first_of_next_month = next_month.replace(day=1)\n", "        last_of_this_month = first_of_next_month - <PERSON><PERSON><PERSON>(days=1)\n", "        if abs((date_of_interest - last_of_previous_month).days) <= abs((date_of_interest - last_of_this_month).days):\n", "            return last_of_previous_month.strftime(DATE_PATTERN)\n", "        else:\n", "            return last_of_this_month.strftime(DATE_PATTERN)"]}, {"cell_type": "code", "execution_count": 4, "id": "ce16f884", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-31\n"]}], "source": ["res = adjust_fiscal_date_ending('2023-01-27')\n", "print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "13961b1e", "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Dict, Any, List\n", "from thefuzz import fuzz\n", "\n", "from logger import log\n", "import config\n", "\n", "class CompanyNormalizer:\n", "    \"\"\"\n", "    Handles the normalization of Company entities by matching them against a master\n", "    SEC ticker list and a learned canonical map. Uses a list-based map, ticker\n", "    symbols as IDs, and an optimized two-stage search.\n", "    \"\"\"\n", "\n", "    def __init__(self, sec_ticker_path: str, company_map_path: str):\n", "        \"\"\"\n", "        Initializes the CompanyNormalizer.\n", "        \"\"\"\n", "        log.info(\"Initializing CompanyNormalizer with new list-based map strategy...\")\n", "        self.company_map_path = company_map_path\n", "        \n", "        # --- In-memory data structures for high performance ---\n", "        # self.company_map: Main lookup dict { Ticker -> Company Object }\n", "        # self._alias_to_ticker_map: Reverse index { Alias -> Ticker }\n", "        self.company_map: Dict[str, Dict[str, Any]] = {}\n", "        self._alias_to_ticker_map: Dict[str, str] = {}\n", "        \n", "        # --- Load and process external data ---\n", "        sec_master_list = self._load_sec_ticker_list(sec_ticker_path)\n", "        # Create a fast lookup for the SEC list, keyed by ticker\n", "        self._sec_ticker_lookup = {\n", "            entry.get(\"ticker\", \"\").upper(): entry for entry in sec_master_list if entry.get(\"ticker\")\n", "        }\n", "        \n", "        # Load the list-based map and build our in-memory lookups\n", "        self._load_and_build_maps()\n", "\n", "        self.threshold = config.FUZZY_MATCH_THRESHOLD\n", "        log.info(f\"CompanyNormalizer initialized with {len(self.company_map)} canonical companies.\")\n", "\n", "    def _load_sec_ticker_list(self, file_path: str) -> List[Dict[str, Any]]:\n", "        \"\"\"Loads the master SEC ticker list from its JSON file.\"\"\"\n", "        log.info(f\"Loading SEC master ticker list from: {file_path}\")\n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                data = json.load(f)\n", "                return list(data.values())\n", "        except (FileNot<PERSON><PERSON>nd<PERSON><PERSON>r, json.JSONDecodeError) as e:\n", "            log.error(f\"FATAL: Failed to load SEC ticker list from {file_path}. Error: {e}\")\n", "            raise\n", "\n", "    def _load_and_build_maps(self):\n", "        \"\"\"\n", "        Loads the list-based canonical map and builds the in-memory lookup dictionaries.\n", "        \"\"\"\n", "        try:\n", "            with open(self.company_map_path, 'r', encoding='utf-8') as f:\n", "                log.info(f\"Loading company canonical map from: {self.company_map_path}\")\n", "                content = f.read()\n", "                if not content.strip():\n", "                    log.warning(f\"Company map file '{self.company_map_path.name}' is empty. Starting fresh.\")\n", "                    return\n", "                \n", "                # Load the list of company objects\n", "                list_of_companies = json.loads(content)\n", "                \n", "                # Build the in-memory lookup maps from this list\n", "                for company_obj in list_of_companies:\n", "                    ticker = company_obj.get(\"ticker_symbol\")\n", "                    if ticker:\n", "                        self.company_map[ticker] = company_obj\n", "                        for alias in company_obj.get(\"aliases\", []):\n", "                            self._alias_to_ticker_map[alias.upper()] = ticker\n", "\n", "        except FileNotFoundError:\n", "            log.warning(f\"Company map file not found at {self.company_map_path}. Starting fresh.\")\n", "        except json.JSONDecodeError:\n", "            log.error(f\"FATAL: Could not decode JSON from {self.company_map_path}. File may be corrupt.\")\n", "            raise\n", "\n", "    def save_map(self):\n", "        \"\"\"\n", "        Saves the in-memory map back to the list-based JSON file.\n", "        \"\"\"\n", "        log.info(f\"Saving updated company canonical map to: {self.company_map_path}\")\n", "        \n", "        # Convert the in-memory dictionary back to a sorted list for saving\n", "        list_to_save = sorted(self.company_map.values(), key=lambda x: x['ticker_symbol'])\n", "        \n", "        if not list_to_save:\n", "            log.warning(\"In-memory company map is empty. Nothing to save.\")\n", "            return\n", "\n", "        try:\n", "            with open(self.company_map_path, 'w', encoding='utf-8') as f:\n", "                json.dump(list_to_save, f, indent=2, ensure_ascii=False)\n", "            log.info(f\"Successfully saved {len(list_to_save)} companies to the map.\")\n", "        except IOError as e:\n", "            log.error(f\"Failed to write company map file. Error: {e}\")\n", "\n", "    def _calculate_weighted_score(self, str1: str, str2: str) -> float:\n", "        \"\"\"\n", "        Calculates a weighted average score from multiple fuzzy matching ratios to provide similarity score.\n", "        Args:\n", "            str1 (str): The first string to compare.\n", "            str2 (str): The second string to compare.\n", "\n", "        Returns:\n", "            float: The weighted average similarity score.(0-100)\n", "        \"\"\"\n", "        score_token_set = fuzz.token_set_ratio(str1, str2)\n", "        score_partial = fuzz.partial_ratio(str1, str2)\n", "        score_ratio = fuzz.ratio(str1, str2)\n", "\n", "        weighted_score = (\n", "            (score_token_set * 0.5) +\n", "            (score_partial * 0.3) +\n", "            (score_ratio * 0.2)\n", "        )\n", "\n", "        return weighted_score\n", "\n", "    def _find_match_in_local_map(self, name: str) -> str | None:\n", "        \"\"\"\n", "        Searches for a name in the local map using the optimized reverse index\n", "        for exact matches, followed by a fuzzy search.\n", "        \"\"\"\n", "        cleaned_name = name.upper().strip()\n", "\n", "        # Step 1: Ultra-fast exact match using the reverse index\n", "        ticker = self._alias_to_ticker_map.get(cleaned_name)\n", "        if ticker:\n", "            log.debug(f\"Exact alias match for '{name}' found in local map: {ticker}\")\n", "            return ticker\n", "\n", "        # Step 2: Fuzzy match if no exact match was found\n", "        best_match_ticker = None\n", "        highest_score = 0\n", "        for ticker, details in self.company_map.items():\n", "            for alias in details[\"aliases\"]:\n", "                score = self._calculate_weighted_score(cleaned_name, alias)\n", "                if score > highest_score:\n", "                    highest_score = score\n", "                    best_match_ticker = ticker\n", "        \n", "        if highest_score >= self.threshold:\n", "            log.info(f\"Fuzzy match for '{name}' found in local map: '{best_match_ticker}' (Score: {highest_score:.2f})\")\n", "            return best_match_ticker\n", "            \n", "        return None\n", "\n", "    def _find_match_in_sec_list(self, name: str) -> Dict[str, Any] | None:\n", "        \"\"\"\n", "        Searches for a name in the SEC master list using a two-stage process:\n", "        1. Exact ticker match.\n", "        2. Fuzzy title match.\n", "        \"\"\"\n", "        cleaned_name = name.upper().strip()\n", "        \n", "        # Step 1: Exact Ticker Match (Highest Confidence)\n", "        if cleaned_name in self._sec_ticker_lookup:\n", "            log.info(f\"Exact ticker match for '{name}' found in SEC master list.\")\n", "            return self._sec_ticker_lookup[cleaned_name]\n", "\n", "        # Step 2: Fuzzy Title Match (If no ticker match)\n", "        best_match_entry = None\n", "        highest_score = 0\n", "        for entry in self._sec_ticker_lookup.values():\n", "            title = entry.get(\"title\", \"\").upper()\n", "            score = self._calculate_weighted_score(cleaned_name, title)\n", "            if score > highest_score:\n", "                highest_score = score\n", "                best_match_entry = entry\n", "\n", "        if highest_score >= self.threshold:\n", "            log.info(f\"Fuzzy title match for '{name}' found in SEC master list: '{best_match_entry.get('title')}' (Score: {highest_score:.2f})\")\n", "            return best_match_entry\n", "            \n", "        return None\n", "\n", "    def normalize_company(self, entity_name: str) -> str:\n", "        \"\"\"\n", "        Normalizes a single company entity to its canonical ID (stock ticker).\n", "        \"\"\"\n", "        # Step 1: Search our existing, learned map\n", "        ticker = self._find_match_in_local_map(entity_name)\n", "        if ticker:\n", "            # If we found a match, learn the new name as an alias\n", "            cleaned_name = entity_name.upper().strip()\n", "            if cleaned_name not in self.company_map[ticker][\"aliases\"]:\n", "                self.company_map[ticker][\"aliases\"].append(cleaned_name)\n", "                self._alias_to_ticker_map[cleaned_name] = ticker # Update reverse index\n", "            return ticker\n", "\n", "        # Step 2: If no local match, search the master SEC list\n", "        sec_entry = self._find_match_in_sec_list(entity_name)\n", "        if sec_entry:\n", "            sec_ticker = sec_entry.get(\"ticker\")\n", "            if not sec_ticker:\n", "                log.warning(f\"Found SEC entry for '{entity_name}' but it has no ticker. Treating as unresolved.\")\n", "            else:\n", "                sec_ticker_upper = sec_ticker.upper()\n", "                sec_title_upper = sec_entry.get(\"title\", \"\").upper()\n", "                \n", "                log.info(f\"Creating new entry in company map for '{sec_ticker_upper}'\")\n", "                \n", "                # Create the new company object\n", "                new_company_obj = {\n", "                    \"company_name\": sec_title_upper,\n", "                    \"ticker_symbol\": sec_ticker_upper,\n", "                    \"aliases\": sorted(list({entity_name.upper().strip(), sec_ticker_upper, sec_title_upper}))\n", "                }\n", "                \n", "                # Add it to our in-memory maps\n", "                self.company_map[sec_ticker_upper] = new_company_obj\n", "                for alias in new_company_obj[\"aliases\"]:\n", "                    self._alias_to_ticker_map[alias] = sec_ticker_upper\n", "                \n", "                return sec_ticker_upper\n", "\n", "        # Step 3: If no match anywhere, return an unresolved ID\n", "        log.warning(f\"Could not resolve company '{entity_name}'. No match in local map or SEC list.\")\n", "        unresolved_id = \"UNRESOLVED_COMPANY_\" + entity_name.upper().replace(\" \", \"_\").replace(\".\", \"\")\n", "        return unresolved_id"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}