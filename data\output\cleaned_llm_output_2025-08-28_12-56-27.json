{"nodes": [{"id": "NVDA", "labels": ["Company"], "properties": {"name": "NVIDIA CORP", "id": "NVDA"}}, {"id": "AMD", "labels": ["Company"], "properties": {"name": "ADVANCED MICRO DEVICES INC", "id": "AMD"}}, {"id": "MHUA", "labels": ["Company"], "properties": {"name": "MEIHUA INTERNATIONAL MEDICAL TECHNOLOGIES CO., LTD.", "id": "MHUA"}}, {"id": "INTC", "labels": ["Company"], "properties": {"name": "INTEL CORP", "id": "INTC"}}, {"id": "BABA", "labels": ["Company"], "properties": {"name": "ALIBABA GROUP HOLDING LTD", "id": "BABA"}}, {"id": "GOOGL", "labels": ["Company"], "properties": {"name": "ALPHABET INC.", "id": "GOOGL"}}, {"id": "AAON", "labels": ["Company"], "properties": {"name": "AAON, INC.", "id": "AAON"}}, {"id": "BIDU", "labels": ["Company"], "properties": {"name": "BAIDU, INC.", "id": "BIDU"}}, {"id": "AMBA", "labels": ["Company"], "properties": {"name": "AMBARELLA INC", "id": "AMBA"}}, {"id": "AVGO", "labels": ["Company"], "properties": {"name": "BROADCOM INC.", "id": "AVGO"}}, {"id": "UNRESOLVED_Qualcomm Incorporated", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Qualcomm Incorporated", "id": "UNRESOLVED_Qualcomm Incorporated"}}, {"id": "UNRESOLVED_Renesas Electronics Corporation", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Renesas Electronics Corporation", "id": "UNRESOLVED_Renesas Electronics Corporation"}}, {"id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Samsung Electronics Co., Ltd.", "id": "UNRESOLVED_Samsung Electronics Co., Ltd."}}, {"id": "TSLA", "labels": ["Company"], "properties": {"name": "TESLA, INC.", "id": "TSLA"}}, {"id": "ANET", "labels": ["Company"], "properties": {"name": "ARISTA NETWORKS, INC.", "id": "ANET"}}, {"id": "CSCO", "labels": ["Company"], "properties": {"name": "CISCO SYSTEMS, INC.", "id": "CSCO"}}, {"id": "HPE", "labels": ["Company"], "properties": {"name": "HEWLETT PACKARD ENTERPRISE CO", "id": "HPE"}}, {"id": "LITE", "labels": ["Company"], "properties": {"name": "LUMENTUM HOLDINGS INC.", "id": "LITE"}}, {"id": "MRVL", "labels": ["Company"], "properties": {"name": "MARVELL TECHNOLOGY, INC.", "id": "MRVL"}}, {"id": "UNRESOLVED_Samsung Electronics Co. Ltd.", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Samsung Electronics Co. Ltd.", "id": "UNRESOLVED_Samsung Electronics Co. Ltd."}}, {"id": "Data Center", "labels": ["BusinessSegment"], "properties": {"name": "Data Center", "id": "Data Center"}}, {"id": "Gaming", "labels": ["BusinessSegment"], "properties": {"name": "Gaming", "id": "Gaming"}}, {"id": "Professional Visualization", "labels": ["BusinessSegment"], "properties": {"name": "Professional Visualization", "id": "Professional Visualization"}}, {"id": "Automotive", "labels": ["BusinessSegment"], "properties": {"name": "Automotive", "id": "Automotive"}}, {"id": "CUDA", "labels": ["Technology"], "properties": {"name": "CUDA-X", "id": "CUDA"}}, {"id": "GeForce RTX platform", "labels": ["Product"], "properties": {"name": "NVIDIA GeForce RTX platform", "id": "GeForce RTX platform"}}, {"id": "Tensor Core", "labels": ["Technology"], "properties": {"name": "Tensor Cores", "id": "Tensor Core"}}, {"id": "NVIDIA Avatar Cloud Engine", "labels": ["Product"], "properties": {"name": "NVIDIA Avatar Cloud Engine", "id": "NVIDIA Avatar Cloud Engine"}}, {"id": "DGX Cloud", "labels": ["Product"], "properties": {"name": "NVIDIA DGX Cloud software and service", "id": "DGX Cloud"}}, {"id": "NVIDIA NeMo", "labels": ["Product"], "properties": {"name": "NVIDIA NeMo", "id": "NVIDIA NeMo"}}, {"id": "Large Language Model", "labels": ["Technology"], "properties": {"name": "Large Language Model", "id": "Large Language Model"}}, {"id": "GeForce NOW", "labels": ["Product"], "properties": {"name": "GeForce NOW", "id": "GeForce NOW"}}, {"id": "Omniverse", "labels": ["Product"], "properties": {"name": "NVIDIA Omniverse", "id": "Omniverse"}}, {"id": "Studio Drivers", "labels": ["Product"], "properties": {"name": "Studio Drivers", "id": "Studio Drivers"}}, {"id": "GeForce Experience", "labels": ["Product"], "properties": {"name": "GeForce Experience", "id": "GeForce Experience"}}, {"id": "NVIDIA DRIVE", "labels": ["Product"], "properties": {"name": "NVIDIA DRIVE stack", "id": "NVIDIA DRIVE"}}, {"id": "Networking technology", "labels": ["Technology"], "properties": {"name": "Networking technology", "id": "Networking technology"}}, {"id": "vGPU", "labels": ["Product"], "properties": {"name": "vGPU", "id": "vGPU"}}, {"id": "Compute & Networking segment", "labels": ["BusinessSegment"], "properties": {"name": "Compute & Networking segment", "id": "Compute & Networking segment"}}, {"id": "Graphics", "labels": ["BusinessSegment"], "properties": {"name": "Graphics", "id": "Graphics"}}, {"id": "TATT", "labels": ["Company"], "properties": {"name": "TAT TECHNOLOGIES LTD", "id": "TATT"}}, {"id": "AlexNet neural network", "labels": ["Technology"], "properties": {"name": "AlexNet neural network", "id": "AlexNet neural network"}}, {"id": "NVIDIA GPU", "labels": ["Product"], "properties": {"name": "NVIDIA RTX GPU", "id": "NVIDIA GPU"}}, {"id": "Tensor Core GPU", "labels": ["Product"], "properties": {"name": "Tensor Core GPU", "id": "Tensor Core GPU"}}, {"id": "<PERSON>", "labels": ["Product"], "properties": {"name": "<PERSON>", "id": "<PERSON>"}}, {"id": "NVIDIA Clara", "labels": ["Product"], "properties": {"name": "NVIDIA Clara", "id": "NVIDIA Clara"}}, {"id": "NVIDIA AI Enterprise", "labels": ["Product"], "properties": {"name": "NVIDIA AI Enterprise software", "id": "NVIDIA AI Enterprise"}}, {"id": "NVIDIA DPU", "labels": ["Product"], "properties": {"name": "NVIDIA Data Processing Units (DPUs)", "id": "NVIDIA DPU"}}, {"id": "<PERSON>", "labels": ["Product"], "properties": {"name": "<PERSON>", "id": "<PERSON>"}}, {"id": "NVIDIA CPU", "labels": ["Product"], "properties": {"name": "NVIDIA Grace CPU", "id": "NVIDIA CPU"}}, {"id": "NVIDIA Spectrum-X Ethernet networking platform", "labels": ["Product"], "properties": {"name": "NVIDIA Spectrum-X Ethernet networking platform", "id": "NVIDIA Spectrum-X Ethernet networking platform"}}, {"id": "NVIDIA NIM", "labels": ["Product"], "properties": {"name": "NVIDIA Inference Microservices", "id": "NVIDIA NIM"}}, {"id": "NVIDIA AI Foundry service", "labels": ["Product"], "properties": {"name": "NVIDIA AI Foundry service", "id": "NVIDIA AI Foundry service"}}, {"id": "Llama 3.1 collection of models", "labels": ["Technology"], "properties": {"name": "Llama 3.1 collection of models", "id": "Llama 3.1 collection of models"}}, {"id": "H200", "labels": ["Product"], "properties": {"name": "NVIDIA H200", "id": "H200"}}, {"id": "B200", "labels": ["Product"], "properties": {"name": "NVIDIA Blackwell architecture B200 processor", "id": "B200"}}, {"id": "RTX AI PC", "labels": ["Product"], "properties": {"name": "RTX AI PC", "id": "RTX AI PC"}}, {"id": "NVIDIA ACE generative AI microservices", "labels": ["Product"], "properties": {"name": "NVIDIA ACE generative AI microservices", "id": "NVIDIA ACE generative AI microservices"}}, {"id": "NVIDIA Metropolis vision AI", "labels": ["Product"], "properties": {"name": "NVIDIA Metropolis vision AI", "id": "NVIDIA Metropolis vision AI"}}, {"id": "NVIDIA Isaac AI robot development", "labels": ["Product"], "properties": {"name": "NVIDIA Isaac AI robot development", "id": "NVIDIA Isaac AI robot development"}}, {"id": "Santa Clara, California", "labels": ["GeographicRegion"], "properties": {"name": "Santa Clara, California", "id": "Santa Clara, California"}}, {"id": "Santa Clara", "labels": ["GeographicRegion"], "properties": {"name": "Santa Clara", "id": "Santa Clara"}}, {"id": "NVIDIA Data Center Platform", "labels": ["Product"], "properties": {"name": "NVIDIA Data Center accelerated computing platform", "id": "NVIDIA Data Center Platform"}}, {"id": "Quantum for InfiniBand", "labels": ["Product"], "properties": {"name": "Quantum for InfiniBand", "id": "Quantum for InfiniBand"}}, {"id": "Spectrum for Ethernet", "labels": ["Product"], "properties": {"name": "Spectrum for Ethernet", "id": "Spectrum for Ethernet"}}, {"id": "Jetson robotics platform", "labels": ["Product"], "properties": {"name": "Jetson robotics platform", "id": "Jetson robotics platform"}}, {"id": "Automotive infotainment platform", "labels": ["Product"], "properties": {"name": "Automotive infotainment platform", "id": "Automotive infotainment platform"}}, {"id": "GeForce GPU", "labels": ["Product"], "properties": {"name": "GeForce GPU", "id": "GeForce GPU"}}, {"id": "vGPU software", "labels": ["Product"], "properties": {"name": "vGPU software", "id": "vGPU software"}}, {"id": "InfiniBand", "labels": ["Technology"], "properties": {"name": "InfiniBand", "id": "InfiniBand"}}, {"id": "Ethernet", "labels": ["Technology"], "properties": {"name": "Ethernet", "id": "Ethernet"}}, {"id": "California", "labels": ["GeographicRegion"], "properties": {"name": "California", "id": "California"}}, {"id": "TSM", "labels": ["Company"], "properties": {"name": "TAIWAN SEMICONDUCTOR MANUFACTURING CO LTD", "id": "TSM"}}, {"id": "UNRESOLVED_SK hynix Inc.", "labels": ["Company"], "properties": {"name": "UNRESOLVED_SK hynix Inc.", "id": "UNRESOLVED_SK hynix Inc."}}, {"id": "UNRESOLVED_Hon Hai Precision Industry Co., Ltd.", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Hon Hai Precision Industry Co., Ltd.", "id": "UNRESOLVED_Hon Hai Precision Industry Co., Ltd."}}, {"id": "FN", "labels": ["Company"], "properties": {"name": "FABRINET", "id": "FN"}}, {"id": "CoWoS technology", "labels": ["Technology"], "properties": {"name": "CoWoS technology", "id": "CoWoS technology"}}, {"id": "Asia-Pacific region", "labels": ["GeographicRegion"], "properties": {"name": "Asia-Pacific region", "id": "Asia-Pacific region"}}, {"id": "Blackwell architecture", "labels": ["Technology"], "properties": {"name": "Blackwell architecture", "id": "Blackwell architecture"}}, {"id": "Gaming GPU", "labels": ["Product"], "properties": {"name": "Gaming GPU", "id": "Gaming GPU"}}, {"id": "Ethereum", "labels": ["Technology"], "properties": {"name": "Ethereum", "id": "Ethereum"}}, {"id": "Taiwan", "labels": ["GeographicRegion"], "properties": {"name": "Taiwan", "id": "Taiwan"}}, {"id": "China", "labels": ["GeographicRegion"], "properties": {"name": "People's Republic of China", "id": "China"}}, {"id": "UNRESOLVED_Third party", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Third party", "id": "UNRESOLVED_Third party"}}, {"id": "UNRESOLVED_Customer A", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Customer A", "id": "UNRESOLVED_Customer A"}}, {"id": "NVIDIA Accelerated Computing Platform", "labels": ["Product"], "properties": {"name": "NVIDIA Accelerated Computing Platform", "id": "NVIDIA Accelerated Computing Platform"}}, {"id": "NVIDIA VGPU Software", "labels": ["Product"], "properties": {"name": "NVIDIA VGPU Software", "id": "NVIDIA VGPU Software"}}, {"id": "Healthcare", "labels": ["IndustrySector"], "properties": {"name": "Healthcare", "id": "Healthcare"}}, {"id": "Financial Services", "labels": ["IndustrySector"], "properties": {"name": "Financial Services", "id": "Financial Services"}}, {"id": "Manufacturing", "labels": ["IndustrySector"], "properties": {"name": "Manufacturing", "id": "Manufacturing"}}, {"id": "Retail", "labels": ["IndustrySector"], "properties": {"name": "Retail", "id": "Retail"}}, {"id": "NVIDIA Blackwell architecture", "labels": ["Technology"], "properties": {"name": "NVIDIA Blackwell architecture", "id": "NVIDIA Blackwell architecture"}}, {"id": "Cloud service provider", "labels": ["IndustrySector"], "properties": {"name": "Cloud service provider", "id": "Cloud service provider"}}, {"id": "Consumer internet company", "labels": ["IndustrySector"], "properties": {"name": "Consumer internet company", "id": "Consumer internet company"}}, {"id": "Enterprise software industry", "labels": ["IndustrySector"], "properties": {"name": "Enterprise software industry", "id": "Enterprise software industry"}}, {"id": "Healthcare industry", "labels": ["IndustrySector"], "properties": {"name": "Healthcare industry", "id": "Healthcare industry"}}, {"id": "Transportation industry", "labels": ["IndustrySector"], "properties": {"name": "Transportation industry", "id": "Transportation industry"}}, {"id": "Financial services industry", "labels": ["IndustrySector"], "properties": {"name": "Financial services industry", "id": "Financial services industry"}}, {"id": "Gaming industry", "labels": ["IndustrySector"], "properties": {"name": "Gaming industry", "id": "Gaming industry"}}, {"id": "Securities and Exchange Commission", "labels": ["GovernmentAgency"], "properties": {"name": "Securities and Exchange Commission", "id": "Securities and Exchange Commission"}}, {"id": "UNRESOLVED_Customer B", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Customer B", "id": "UNRESOLVED_Customer B"}}, {"id": "UNRESOLVED_Customer C", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Customer C", "id": "UNRESOLVED_Customer C"}}, {"id": "UNRESOLVED_Customer D", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Customer D", "id": "UNRESOLVED_Customer D"}}, {"id": "UNRESOLVED_Customer E", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Customer E", "id": "UNRESOLVED_Customer E"}}, {"id": "All Other", "labels": ["BusinessSegment"], "properties": {"name": "All Other", "id": "All Other"}}, {"id": "Hopper computing platform", "labels": ["Product"], "properties": {"name": "Hopper computing platform", "id": "Hopper computing platform"}}, {"id": "GeForce RTX 40 Series GPU", "labels": ["Product"], "properties": {"name": "GeForce RTX 40 Series GPU", "id": "GeForce RTX 40 Series GPU"}}, {"id": "UNRESOLVED_Direct Customer A", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Direct Customer A", "id": "UNRESOLVED_Direct Customer A"}}, {"id": "UNRESOLVED_Direct Customer B", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Direct Customer B", "id": "UNRESOLVED_Direct Customer B"}}, {"id": "UNRESOLVED_Direct Customer C", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Direct Customer C", "id": "UNRESOLVED_Direct Customer C"}}, {"id": "Data Center computing", "labels": ["BusinessSegment"], "properties": {"name": "Data Center computing", "id": "Data Center computing"}}, {"id": "Data Center networking", "labels": ["BusinessSegment"], "properties": {"name": "Data Center networking", "id": "Data Center networking"}}, {"id": "GB200", "labels": ["Product"], "properties": {"name": "GB200 system", "id": "GB200"}}, {"id": "NVLink compute fabric", "labels": ["Technology"], "properties": {"name": "NVLink compute fabric", "id": "NVLink compute fabric"}}, {"id": "United States", "labels": ["GeographicRegion"], "properties": {"name": "United States", "id": "United States"}}, {"id": "India", "labels": ["GeographicRegion"], "properties": {"name": "India", "id": "India"}}, {"id": "Israel", "labels": ["GeographicRegion"], "properties": {"name": "Israel", "id": "Israel"}}, {"id": "United States Government", "labels": ["GovernmentAgency"], "properties": {"name": "United States Government", "id": "United States Government"}}, {"id": "A100", "labels": ["Product"], "properties": {"name": "NVIDIA A100 Tensor Core GPU", "id": "A100"}}, {"id": "H100", "labels": ["Product"], "properties": {"name": "NVIDIA H100 Tensor Core GPU", "id": "H100"}}, {"id": "Export licensing requirements", "labels": ["LawOrRegulation"], "properties": {"name": "Export license requirements", "id": "Export licensing requirements"}}, {"id": "Russia", "labels": ["GeographicRegion"], "properties": {"name": "Russia", "id": "Russia"}}, {"id": "Hong Kong", "labels": ["GeographicRegion"], "properties": {"name": "Hong Kong", "id": "Hong Kong"}}, {"id": "High-speed network interconnect", "labels": ["Product"], "properties": {"name": "High-speed network interconnect", "id": "High-speed network interconnect"}}, {"id": "Patent", "labels": ["Technology"], "properties": {"name": "Patent", "id": "Patent"}}, {"id": "Trademark", "labels": ["Technology"], "properties": {"name": "Trademark", "id": "Trademark"}}, {"id": "Trade secret", "labels": ["Technology"], "properties": {"name": "Trade secret", "id": "Trade secret"}}, {"id": "Employee nondisclosure agreement", "labels": ["Technology"], "properties": {"name": "Employee nondisclosure agreement", "id": "Employee nondisclosure agreement"}}, {"id": "Third-party nondisclosure agreement", "labels": ["Technology"], "properties": {"name": "Third-party nondisclosure agreement", "id": "Third-party nondisclosure agreement"}}, {"id": "Licensing arrangement", "labels": ["Technology"], "properties": {"name": "Licensing arrangement", "id": "Licensing arrangement"}}, {"id": "Trade Secret", "labels": ["Technology"], "properties": {"name": "Trade Secret", "id": "Trade Secret"}}, {"id": "Nondisclosure Agreement", "labels": ["Technology"], "properties": {"name": "Nondisclosure Agreement", "id": "Nondisclosure Agreement"}}, {"id": "Licensing Arrangement", "labels": ["Technology"], "properties": {"name": "Licensing Arrangement", "id": "Licensing Arrangement"}}, {"id": "NVIDIA DGX system", "labels": ["Product"], "properties": {"name": "NVIDIA DGX system", "id": "NVIDIA DGX system"}}, {"id": "NVIDIA HGX system", "labels": ["Product"], "properties": {"name": "NVIDIA HGX system", "id": "NVIDIA HGX system"}}, {"id": "NVIDIA MGX system", "labels": ["Product"], "properties": {"name": "NVIDIA MGX system", "id": "NVIDIA MGX system"}}, {"id": "RTX 4090", "labels": ["Product"], "properties": {"name": "RTX 4090", "id": "RTX 4090"}}, {"id": "RTX 6000 Ada", "labels": ["Product"], "properties": {"name": "RTX 6000 Ada", "id": "RTX 6000 Ada"}}, {"id": "A800", "labels": ["Product"], "properties": {"name": "A800", "id": "A800"}}, {"id": "H800", "labels": ["Product"], "properties": {"name": "H800", "id": "H800"}}, {"id": "B100", "labels": ["Product"], "properties": {"name": "B100", "id": "B100"}}, {"id": "L4", "labels": ["Product"], "properties": {"name": "L4", "id": "L4"}}, {"id": "L40S", "labels": ["Product"], "properties": {"name": "L40S", "id": "L40S"}}, {"id": "NVL 72", "labels": ["Product"], "properties": {"name": "NVL 72", "id": "NVL 72"}}, {"id": "NVL 36", "labels": ["Product"], "properties": {"name": "NVL 36", "id": "NVL 36"}}, {"id": "Blackwell systems", "labels": ["Product"], "properties": {"name": "Blackwell systems", "id": "Blackwell systems"}}, {"id": "Foreign Corrupt Practices Act", "labels": ["LawOrRegulation"], "properties": {"name": "Foreign Corrupt Practices Act", "id": "Foreign Corrupt Practices Act"}}, {"id": "French Competition Authority", "labels": ["GovernmentAgency"], "properties": {"name": "French Competition Authority", "id": "French Competition Authority"}}, {"id": "Section 4(a)(2) of the Securities Act", "labels": ["LawOrRegulation"], "properties": {"name": "Section 4(a)(2) of the Securities Act of 1933", "id": "Section 4(a)(2) of the Securities Act"}}, {"id": "Regulation D", "labels": ["LawOrRegulation"], "properties": {"name": "Regulation D", "id": "Regulation D"}}, {"id": "Regulation S", "labels": ["LawOrRegulation"], "properties": {"name": "Regulation S", "id": "Regulation S"}}, {"id": "UNRESOLVED_Acquired Company", "labels": ["Company"], "properties": {"name": "UNRESOLVED_Acquired Company", "id": "UNRESOLVED_Acquired Company"}}], "relationships": [{"source_id": "NVDA", "target_id": "AMD", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "AMD is explicitly named as a current competitor.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "MHUA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "<PERSON><PERSON><PERSON> is explicitly named as a current competitor.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "INTC", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "Intel is explicitly named as a current competitor.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "BABA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "large cloud services companies with internal teams designing hardware and software that incorporate accelerated or AI computing functionality as part of their internal solutions or platforms, such as Alibaba Group, Alphabet Inc., Amazon, Inc., or Amazon, Baidu, Inc., Huawei, and Microsoft Corporation, or Microsoft;", "rationale": "Alibaba Group is listed among large cloud services companies described as competitors.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GOOGL", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "large cloud services companies with internal teams designing hardware and software that incorporate accelerated or AI computing functionality as part of their internal solutions or platforms, such as Alibaba Group, Alphabet Inc., Amazon, Inc., or Amazon, Baidu, Inc., Huawei, and Microsoft Corporation, or Microsoft;", "rationale": "Alphabet Inc. is listed among large cloud services companies described as competitors.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "AAON", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "large cloud services companies with internal teams designing hardware and software that incorporate accelerated or AI computing functionality as part of their internal solutions or platforms, such as Alibaba Group, Alphabet Inc., Amazon, Inc., or Amazon, Baidu, Inc., Huawei, and Microsoft Corporation, or Microsoft;", "rationale": "Amazon, Inc. is listed among large cloud services companies described as competitors.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "BIDU", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "large cloud services companies with internal teams designing hardware and software that incorporate accelerated or AI computing functionality as part of their internal solutions or platforms, such as Alibaba Group, Alphabet Inc., Amazon, Inc., or Amazon, Baidu, Inc., Huawei, and Microsoft Corporation, or Microsoft;", "rationale": "Baidu, Inc. is listed among large cloud services companies described as competitors.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "INTC", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "large cloud services companies with internal teams designing hardware and software that incorporate accelerated or AI computing functionality as part of their internal solutions or platforms, such as Alibaba Group, Alphabet Inc., Amazon, Inc., or Amazon, Baidu, Inc., Huawei, and Microsoft Corporation, or Microsoft;", "rationale": "Microsoft Corporation is listed among large cloud services companies described as competitors.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "AMBA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Ambarella is named as a supplier of SoC products, placing it in direct competitive context.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "AVGO", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Broadcom Inc. is named as a supplier of SoC products, implying competitive overlap.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Qualcomm Incorporated", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Qualcomm Incorporated is named as a supplier of SoC products, indicating competition.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Renesas Electronics Corporation", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Renesas Electronics Corporation is named as a supplier of SoC products, indicating competition.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Samsung Electronics Co., Ltd. is named as a supplier of SoC products, indicating competition.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "TSLA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.", "rationale": "Tesla, Inc. is described in the list of entities involved in SoC product design, indicating competitive relevance.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "ANET", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Arista Networks is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "CSCO", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Cisco Systems, Inc. is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "HPE", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Hewlett Packard Enterprise Company is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "INTC", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Intel is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "LITE", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Lumentum Holdings is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "MRVL", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings, and Marvell Technology Group as well as internal teams of system vendors and large cloud services companies.", "rationale": "Marvell Technology Group is named among networking product suppliers, indicating competitive overlap.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "AMD", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists AMD as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "MHUA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Huawei as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "INTC", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Intel as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "BABA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Alibaba Group as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GOOGL", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Alphabet Inc. as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "AAON", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Amazon, Inc. as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "BIDU", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Baidu, Inc. as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "INTC", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our current competitors include: suppliers and licensors of hardware and software for discrete and integrated GPUs, custom chips and other accelerated computing solutions, including solutions offered for AI, such as Advanced Micro Devices, Inc., or AMD, Huawei Technologies Co. Ltd., or Huawei, and Intel Corporation, or Intel;", "rationale": "The text explicitly lists Microsoft Corporation as a current competitor of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "AMBA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom, Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.;", "rationale": "The text lists Ambarella, Inc. as a supplier of SoC products, placing it in competition with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "AVGO", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom, Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.;", "rationale": "The text lists Broadcom, Inc. as a supplier of SoC products, placing it in competition with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Qualcomm Incorporated", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom, Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.;", "rationale": "The text lists Qualcomm Incorporated as a supplier of SoC products, placing it in competition with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Renesas Electronics Corporation", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom, Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.;", "rationale": "The text lists Renesas Electronics Corporation as a supplier of SoC products, placing it in competition with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co. Ltd.", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "suppliers of hardware and software for SoC products that are used in servers or embedded into automobiles, autonomous machines, and gaming devices, such as Ambarella, Inc., AMD, Broadcom, Inc., or Broadcom, Intel, Qualcomm Incorporated, Renesas Electronics Corporation, and Samsung, or companies with internal teams designing SoC products for their own products and services, such as Tesla, Inc.;", "rationale": "The text lists Samsung Electronics Co. Ltd. as a supplier of SoC products, placing it in competition with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "TSLA", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "such as Tesla, Inc.;", "rationale": "The text explicitly mentions Tesla, Inc. as part of the set of competitors in SoC product contexts.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "ANET", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings Inc., and Marvell Technology, Inc. as well as internal teams of system vendors and large cloud services companies..", "rationale": "The text lists Arista Networks, Inc. as a competitor in networking products context.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "CSCO", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings Inc., and Marvell Technology, Inc. as well as internal teams of system vendors and large cloud services companies..", "rationale": "The text lists Cisco Systems, Inc. as a competitor in networking products context.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "HPE", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings Inc., and Marvell Technology, Inc. as well as internal teams of system vendors and large cloud services companies..", "rationale": "The text lists Hewlett Packard Enterprise Company as a competitor in networking products context.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "LITE", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings Inc., and Marvell Technology, Inc. as well as internal teams of system vendors and large cloud services companies..", "rationale": "The text lists Lumentum Holdings Inc. as a competitor in networking products context.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "MRVL", "type": "COMPETES_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "networking products consisting of switches, network adapters (including DPUs), and cable solutions (including optical modules) include such as AMD, Arista Networks, Broadcom, Cisco Systems, Inc., Hewlett Packard Enterprise Company, Huawei, Intel, Lumentum Holdings Inc., and Marvell Technology, Inc. as well as internal teams of system vendors and large cloud services companies..", "rationale": "The text lists Marvell Technology, Inc. as a competitor in networking products context.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "target markets, which include Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "Explicitly lists Data Center as one of the target markets/segments for NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Gaming", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "target markets, which include Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "Explicitly lists Gaming as one of the target markets/segments for NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Professional Visualization", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "target markets, which include Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "Explicitly lists Professional Visualization as one of the target markets/segments for NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "target markets, which include Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "Explicitly lists Automotive as one of the target markets/segments for NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "leveraging our GPUs, CUDA and networking technologies as the fundamental building blocks.", "rationale": "CUDA is cited as a fundamental building block used by NVIDIA.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Tensor Core", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Tensor Cores included in all RTX-class GPUs.", "rationale": "Tensor Cores are a technology included in RTX GPUs, implying use by NVIDIA.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "NVIDIA NeMo", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "NeMo – a complete solution for building enterprise-ready Large Language Models, or LLMs, using open source and proprietary LLMs created by NVIDIA and third parties.", "rationale": "NeMo is presented as a complete solution developed by NVIDIA for building LLMs.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "NVIDIA DRIVE", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "under the DRIVE brand, which we are bringing to market through our partnerships with automotive OEMs.", "rationale": "DRIVE is presented as a NVIDIA automotive AI compute brand/product.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Omniverse", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Omniverse is real-time 3D design collaboration and virtual world simulation software that empowers artists, designers, and creators to connect and collaborate in leading design applications.", "rationale": "Omniverse is described as a NVIDIA software product.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "DGX Cloud", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "DGX Cloud, an AI-training-as-a-service platform,", "rationale": "DGX Cloud is a NVIDIA product/platform developed by NVIDIA.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "GeForce NOW", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "GeForce NOW for gaming.", "rationale": "GeForce NOW is a NVIDIA cloud gaming platform developed by NVIDIA.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "GeForce RTX platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "on our GeForce and NVIDIA RTX platforms", "rationale": "GeForce platform is a platform offered/developed by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "GeForce RTX platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "on our GeForce and NVIDIA RTX platforms", "rationale": "NVIDIA RTX platform is a platform offered/developed by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "GeForce Experience", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "GeForce Experience enhances each gamer's experience by optimizing their PC's settings, as well as enabling the recording and sharing of gameplay.", "rationale": "GeForce Experience is a NVIDIA software product developed by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "Studio Drivers", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our Studio drivers enhance and accelerate a number of popular creative applications.", "rationale": "Studio Drivers are a NVIDIA software product developed by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "NVIDIA Avatar Cloud Engine", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "NVIDIA Avatar Cloud Engine, or ACE, is a suite of technologies that help developers bring digital avatars to life with generative AI, running in the cloud or locally on the PC.", "rationale": "ACE is a NVIDIA product/technology suite.", "strength_score": 0.8}}, {"source_id": "NVIDIA NeMo", "target_id": "Large Language Model", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "NeMo – a complete solution for building enterprise-ready Large Language Models, or LLMs,", "rationale": "NeMo enables building Large Language Models, i.e., it develops LLMs.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"which include Data Center, Gaming, Professional Visualization, and Automotive.\"", "rationale": "Data Center is listed as a target market/segment for NVIDIA's business.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Gaming", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"which include Data Center, Gaming, Professional Visualization, and Automotive.\"", "rationale": "Gaming is listed as a target market/segment for NVIDIA's business.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Professional Visualization", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"which include Data Center, Gaming, Professional Visualization, and Automotive.\"", "rationale": "Professional Visualization is listed as a target market/segment for NVIDIA's business.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"which include Data Center, Gaming, Professional Visualization, and Automotive.\"", "rationale": "Automotive is listed as a target market/segment for NVIDIA's business.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"CUDA and networking technologies as the fundamental building blocks.\"", "rationale": "CUDA is explicitly named as a building block NVIDIA uses.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Networking technology", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"CUDA and networking technologies as the fundamental building blocks.\"", "rationale": "Networking technologies are explicitly named as a building block NVIDIA uses.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "DGX Cloud", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"we offer DGX Cloud, a fully managed AI-training-as-a-service platform.\"", "rationale": "DGX Cloud is presented as an NVIDIA product/offer.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "GeForce Experience", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"GeForce Experience enhances each gamer’s experience by optimizing their PC’s settings.\"", "rationale": "GeForce Experience is a NVIDIA product described as improving user experience.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "Omniverse", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"Omniverse is real-time 3D design collaboration and virtual world simulation software that empowers artists, designers, and creators to connect and collaborate in leading design applications.\"", "rationale": "Omniverse is a NVIDIA product described as a real-time collaboration platform.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "GeForce NOW", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"cloud platforms such as vGPU for enterprise and GeForce NOW for gaming.\"", "rationale": "GeForce NOW is a cloud gaming platform offered by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "vGPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"cloud platforms such as vGPU for enterprise\"", "rationale": "vGPU is presented as a NVIDIA cloud platform offering.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "GeForce RTX platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"GeForce and NVIDIA RTX platforms.\"", "rationale": "NVIDIA RTX platform is referenced as a platform offered by NVIDIA.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "NVIDIA DRIVE", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"DRIVE brand\"", "rationale": "NVIDIA DRIVE is referenced as a platform/brand associated with NVIDIA in AV/EV market.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "Tensor Core", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"The Tensor Cores included in all RTX-class GPUs.\"", "rationale": "Tensor Cores are used as a feature in RTX GPUs.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "The Compute & Networking segment includes our Data Center accelerated computing platforms and AI solutions and software; networking; automotive platforms and autonomous and electric vehicle solutions; Jetson for robotics and other embedded platforms; and DGX Cloud computing services.", "rationale": "The text explicitly states the Compute & Networking segment as a segment of NVIDIA, establishing a HAS_SEGMENT relation.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "The Graphics segment includes GeForce GPUs for gaming and PCs, the GeForce NOW game streaming service and related infrastructure, and solutions for gaming platforms; Quadro/NVIDIA RTX GPUs for enterprise workstation graphics; virtual GPU, or vGPU, software for cloud-based visual and virtual computing; automotive platforms for infotainment systems; and Omniverse Enterprise software for building and operating industrial AI and digital twin applications.", "rationale": "The text explicitly states the Graphics segment as a segment of NVIDIA, establishing a HAS_SEGMENT relation.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "With our introduction of the CUDA programming model in 2006, we opened the parallel processing capabilities of our GPU to a broad range of compute-intensive applications, paving the way for the emergence of modern AI.", "rationale": "NVIDIA introduced CUDA as a programming model.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Tensor Core GPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We introduced our first Tensor Core GPU in 2017, built from the ground-up for the new era of AI.", "rationale": "NVIDIA introduced Tensor Core GPU.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "<PERSON>", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "In 2023, we introduced our first data center CPU, Grace, built for giant-scale AI and high-performance computing.", "rationale": "NVIDIA introduced Grace as a data center CPU product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA DRIVE", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "NVIDIA develops the NVIDIA DRIVE stack.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "NVIDIA Clara", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "NVIDIA develops Clara.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Omniverse", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "NVIDIA develops Omniverse.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "NVIDIA AI Enterprise", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "and introduced the NVIDIA AI Enterprise software, essentially an operating system for enterprise AI applications.", "rationale": "NVIDIA develops NVIDIA AI Enterprise software.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "NVIDIA DPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "led to the introduction of a new processor class, the data processing unit, or DPU.", "rationale": "NVIDIA introduced the Data Processing Unit (DPU).", "strength_score": 0.75}}, {"source_id": "TATT", "target_id": "NVDA", "type": "ACQUIRED", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our acquisition of Mellanox in 2020 expanded our innovation canvas to include networking and led to the introduction of a new processor class, the data processing unit, or DPU.", "rationale": "NVIDIA acquired Mellanox.", "strength_score": 0.95}}, {"source_id": "AlexNet neural network", "target_id": "NVIDIA GPU", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "the AlexNet neural network, trained on NVIDIA GPUs, won the ImageNet computer image recognition competition", "rationale": "AlexNet neural network was trained on NVIDIA GPUs.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "TATT", "type": "ACQUIRED", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "our acquisition of Mellanox in 2020 expanded our innovation canvas to include networking, enabled our platforms to be data center scale, and led to the introduction of a new processor class – the data processing unit, or DPU.", "rationale": "NVIDIA states it acquired Mellanox in 2020, explicitly indicating an acquisition relationship.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "With our introduction of the CUDA programming model in 2006, we opened the parallel processing capabilities of our GPU to a broad range of compute-intensive applications, paving the way for the emergence of modern AI.", "rationale": "NVIDIA describes the CUDA programming model as something it introduced/developed, indicating the source developed CUDA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "<PERSON>", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "In 2023, we introduced our first data center CPU, Grace, built for giant-scale AI and high performance computing, or HPC.", "rationale": "The text states NVIDIA introduced Grace, implying development by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA DRIVE", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "over the past 5 years, we have built full software stacks that run on top of our GPUs and CUDA to bring AI to the world’s largest industries, including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "The sentence explicitly lists NVIDIA DRIVE stack as part of the software stacks developed by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "<PERSON>", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "over the past 5 years, we have built full software stacks that run on top of our GPUs and CUDA to bring AI to the world’s largest industries, including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "The sentence explicitly lists <PERSON> as part of the software stacks developed by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Omniverse", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "over the past 5 years, we have built full software stacks that run on top of our GPUs and CUDA to bring AI to the world’s largest industries, including NVIDIA DRIVE stack for autonomous driving, Clara for healthcare, and Omniverse for industrial digitalization;", "rationale": "The sentence explicitly lists Omniverse as part of the software stacks developed by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA AI Enterprise", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "..., and introduced the NVIDIA AI Enterprise software – essentially an operating system for enterprise AI applications.", "rationale": "The text explicitly states NVIDIA introduced NVIDIA AI Enterprise software, indicating development by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA DPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "our acquisition of Mellanox in 2020 expanded our innovation canvas to include networking, enabled our platforms to be data center scale, and led to the introduction of a new processor class – the data processing unit, or DPU.", "rationale": "The same sentence that discusses the Mellanox acquisition mentions the introduction of the DPU, implying NVIDIA develops the DPU.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Data Center revenue for the second quarter of fiscal year 2025 was $26.3 billion, up 16% from the previous quarter and up 154% from a year ago.", "rationale": "Data Center is listed as a named business segment with corresponding revenue.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Gaming", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Gaming revenue for the second quarter of fiscal year 2025 was $2.9 billion, up 9% from the previous quarter and up 16% from a year ago.", "rationale": "Gaming is listed as a named business segment with corresponding revenue.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Professional Visualization", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Professional Visualization revenue for the second quarter of fiscal year 2025 was $454 million, up 6% from the previous quarter and up 20% from a year ago.", "rationale": "Professional Visualization is listed as a named business segment with revenue details.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Automotive revenue for the second quarter of fiscal year 2025 was $346 million, up 5% from the previous quarter and up 37% from a year ago.", "rationale": "Automotive is listed as a named business segment with revenue details.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "NVIDIA CPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We unveiled an array of NVIDIA Blackwell-powered systems featuring NVIDIA Grace CPUs, networking and infrastructure from top manufacturers.", "rationale": "Grace CPU is presented as a NVIDIA product integrated into systems.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA Spectrum-X Ethernet networking platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We announced broad adoption of the NVIDIA Spectrum-X Ethernet networking platform by cloud service providers, GPU cloud providers and enterprises, as well as partners incorporating it into their offerings.", "rationale": "Spectrum-X is presented as a NVIDIA networking product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA NIM", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We released NVIDIA Inference Microservices, or NIM, for broad availability to developers globally and unveiled that more than 150 companies are integrating NIM into their platforms.", "rationale": "NVIDIA released/instrumented NIM as a product offering.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA AI Foundry service", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We introduced an NVIDIA AI Foundry service and NIM inference microservices to accelerate generative AI for the world’s enterprises with the Llama 3.1 collection of models.", "rationale": "NVIDIA AI Foundry service is explicitly introduced as a NVIDIA offering.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Llama 3.1 collection of models", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "... with the Llama 3.1 collection of models.", "rationale": "NVIDIA references using the Llama 3.1 collection of models in its offerings.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "H200", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We announced that the combination of NVIDIA H200 and NVIDIA Blackwell architecture B200 processors swept the latest industry-standard MLPerf results for inference.", "rationale": "H200 is presented as a NVIDIA product developed by the company.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "B200", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We announced that the combination of NVIDIA H200 and NVIDIA Blackwell architecture B200 processors swept the latest industry-standard MLPerf results for inference.", "rationale": "B200 processor is presented as a NVIDIA product developed by the company.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GeForce NOW", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We surpassed 2,000 games on GeForce NOW and expanded the service into Japan.", "rationale": "GeForce NOW is a NVIDIA service/product with ongoing development/expansion.", "strength_score": 0.9}}, {"source_id": "RTX AI PC", "target_id": "NVIDIA ACE generative AI microservices", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "NVIDIA ACE generative AI microservices are in early access for RTX AI PCs.", "rationale": "RTX AI PCs use the ACE generative AI microservices.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "NVIDIA ACE generative AI microservices", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "NVIDIA ACE generative AI microservices are in early access for RTX AI PCs.", "rationale": "ACE microservices are NVIDIA’s product offerings introduced by the company.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA Metropolis vision AI", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "... reference workflow that combines NVIDIA Metropolis vision AI, NVIDIA Omniverse simulation and NVIDIA Isaac AI robot development.", "rationale": "Metropolis vision AI is presented as a NVIDIA technology/product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Omniverse", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "... reference workflow that combines NVIDIA Metropolis vision AI, NVIDIA Omniverse simulation and NVIDIA Isaac AI robot development.", "rationale": "Omniverse simulation is presented as a NVIDIA technology/product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA Isaac AI robot development", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "... reference workflow that combines NVIDIA Metropolis vision AI, NVIDIA Omniverse simulation and NVIDIA Isaac AI robot development.", "rationale": "Isaac AI robot development is presented as a NVIDIA technology/product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "\"Our two operating segments are Compute & Networking and Graphics, as described in Note 13 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.\"", "rationale": "The sentence directly states that Compute & Networking is an operating segment of NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "\"Our two operating segments are Compute & Networking and Graphics, as described in Note 13 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.\"", "rationale": "The sentence directly states that Graphics is an operating segment of NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Santa Clara, California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "\"Headquartered in Santa Clara, California, NVIDIA was incorporated in California in April 1993 and reincorporated in Delaware in April 1998..\"", "rationale": "The sentence explicitly states NVIDIA is headquartered in Santa Clara, California.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We report our business results in two segments. The Compute & Networking segment is comprised of our Data Center accelerated computing platforms and end-to-end networking platforms including Quantum for InfiniBand and Spectrum for Ethernet; our NVIDIA DRIVE automated-driving platform and automotive development agreements; Jetson robotics and other embedded platforms; NVIDIA AI Enterprise and other software; and DGX Cloud software and services.", "rationale": "The sentence explicitly defines Compute & Networking as a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We report our business results in two segments. The Graphics segment includes GeForce GPUs for gaming and PCs, the GeForce NOW game streaming service and related infrastructure; Quadro/NVIDIA RTX GPUs for enterprise workstation graphics; virtual GPU, or vGPU, software for cloud-based visual and virtual computing; automotive platforms for infotainment systems; and Omniverse Enterprise software for building and operating metaverse and 3D internet applications.", "rationale": "The sentence explicitly defines Graphics as a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Santa Clara", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Headquartered in Santa Clara, California, NVIDIA was incorporated in California in April 1993 and reincorporated in Delaware in April 1998.", "rationale": "The sentence states the company is headquartered in Santa Clara.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "NVIDIA Data Center Platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "The Compute & Networking segment is comprised of our Data Center accelerated computing platforms and end-to-end networking platforms including Quantum for InfiniBand and Spectrum for Ethernet;", "rationale": "The phrase indicates the Data Center accelerated computing platform is a NVIDIA product offering.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Quantum for InfiniBand", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "The Compute & Networking segment is comprised of our Data Center accelerated computing platforms and end-to-end networking platforms including Quantum for InfiniBand and Spectrum for Ethernet;", "rationale": "Quantum for InfiniBand is listed as part of the segment's platforms.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Spectrum for Ethernet", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "The Compute & Networking segment is comprised of our Data Center accelerated computing platforms and end-to-end networking platforms including Quantum for InfiniBand and Spectrum for Ethernet;", "rationale": "Spectrum for Ethernet is listed as part of the segment's platforms.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Jetson robotics platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Jetson robotics and other embedded platforms", "rationale": "Jetson robotics is described as part of NVIDIA's platform lineup.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "NVIDIA AI Enterprise", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "NVIDIA AI Enterprise and other software", "rationale": "NVIDIA AI Enterprise is named as a NVIDIA-developed product.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "DGX Cloud", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "DGX Cloud software and services", "rationale": "DGX Cloud software and services is named as a NVIDIA product.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "Automotive infotainment platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "automotive platforms for infotainment systems", "rationale": "Automotive infotainment platform is part of NVIDIA's automotive offerings.", "strength_score": 0.88}}, {"source_id": "NVDA", "target_id": "GeForce GPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "The Graphics segment includes GeForce GPUs for gaming and PCs", "rationale": "GeForce GPU is a product developed by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GeForce NOW", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "the GeForce NOW game streaming service and related infrastructure", "rationale": "GeForce NOW game streaming service is listed as a NVIDIA product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA GPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Quadro/NVIDIA RTX GPUs for enterprise workstation graphics", "rationale": "NVIDIA RTX GPU is a product in the RTX GPU family developed by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "vGPU software", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "virtual GPU, or vGPU, software for cloud-based visual and virtual computing", "rationale": "vGPU software is a NVIDIA-developed product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our platforms address four large markets where our expertise is critical: Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "The sentence lists Data Center as one of the markets NVIDIA's platforms address, indicating operation in that market.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Gaming", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our platforms address four large markets where our expertise is critical: Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "The sentence lists Gaming as one of the markets NVIDIA's platforms address, indicating operation in that market.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Professional Visualization", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our platforms address four large markets where our expertise is critical: Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "The sentence lists Professional Visualization as one of the markets NVIDIA's platforms address, indicating operation in that market.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our platforms address four large markets where our expertise is critical: Data Center, Gaming, Professional Visualization, and Automotive.", "rationale": "The sentence lists Automotive as one of the markets NVIDIA's platforms address, indicating operation in that market.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "InfiniBand", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "our networking offerings include end-to-end platforms for InfiniBand and Ethernet, consisting of network adapters, cables, DPUs, and switch systems, as well as a full software stack.", "rationale": "The sentence explicitly states InfiniBand is a technology used in NVIDIA's networking offerings.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Ethernet", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "our networking offerings include end-to-end platforms for InfiniBand and Ethernet, consisting of network adapters, cables, DPUs, and switch systems, as well as a full software stack.", "rationale": "The sentence explicitly states Ethernet is a technology used in NVIDIA's networking offerings.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics.\"", "rationale": "The text explicitly states that the company has two operating segments named Compute & Networking and Graphics.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics.\"", "rationale": "The text explicitly states that the company has two operating segments named Compute & Networking and Graphics.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Santa Clara", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Headquartered in Santa Clara, California,", "rationale": "The text states the company is headquartered in Santa Clara, California, indicating headquarters location in Santa Clara.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Headquartered in Santa Clara, California,", "rationale": "The text states the company is headquartered in Santa Clara, California, indicating headquarters location in California.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics.\"", "rationale": "The text explicitly lists the two operating segments of the company, which matches the HAS_SEGMENT relationship.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics.\"", "rationale": "Same explicit statement showing Graphics as one of the operating segments.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Headquartered in Santa Clara, California, NVIDIA was incorporated in California in April 1993 and reincorporated in Delaware in April 1998..", "rationale": "The text states the company is headquartered in Santa Clara, California, supporting HQ location in California.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "TSM", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We utilize foundries, such as Taiwan Semiconductor Manufacturing Company Limited, or TSMC, and Samsung Electronics Co., Ltd., or Samsung, to produce our semiconductor wafers.", "rationale": "Direct mention of NVIDIA using a named foundry (TSMC) for wafer production.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We utilize foundries, such as Taiwan Semiconductor Manufacturing Company Limited, or TSMC, and Samsung Electronics Co., Ltd., or Samsung, to produce our semiconductor wafers.", "rationale": "Direct mention of NVIDIA using a named foundry (Samsung) for wafer production.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Hon Hai Precision Industry Co., Ltd.", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "Explicit statement that NVIDIA engages with subcontractors/contract manufacturers including Hon Hai.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "INTC", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "Explicit statement that NVIDIA engages with subcontractors/contract manufacturers including Wistron.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "FN", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "Explicit statement that NVIDIA engages with subcontractors/contract manufacturers including Fabrinet.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "MRVL", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We purchase memory from Micron Technology, Inc.", "rationale": "Direct statement that Micron supplies memory to NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_SK hynix Inc.", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We purchase memory from Micron Technology, Inc., SK hynix Inc., and Samsung.", "rationale": "Direct statement that SK hynix supplies memory to NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We purchase memory from Micron Technology, Inc., SK hynix Inc., and Samsung.", "rationale": "Direct statement that Samsung supplies memory to NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "CoWoS technology", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We utilize CoWoS technology for semiconductor packaging.", "rationale": "Explicit statement that NVIDIA uses CoWoS technology.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Asia-Pacific region", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our supply chain is concentrated in the Asia-Pacific region.", "rationale": "Direct statement indicating NVIDIA operates with a supply chain concentrated in Asia-Pacific.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "TSM", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We utilize foundries, such as Taiwan Semiconductor Manufacturing Company Limited, or TSMC, and Samsung Electronics Co., Ltd., or Samsung, to produce our semiconductor wafers.", "rationale": "NVIDIA uses TSMC as a foundry to produce semiconductor wafers.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We utilize foundries, such as Taiwan Semiconductor Manufacturing Company Limited, or TSMC, and Samsung Electronics Co., Ltd., or Samsung, to produce our semiconductor wafers.", "rationale": "NVIDIA uses Samsung as a foundry to produce semiconductor wafers.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "CoWoS technology", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We utilize CoWoS technology for semiconductor packaging.", "rationale": "NVIDIA uses CoWoS technology for semiconductor packaging.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Hon Hai Precision Industry Co., Ltd.", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "NVIDIA engages subcontractors to perform assembly, testing and packaging.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "INTC", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "NVIDIA engages subcontractors to perform assembly, testing and packaging.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "FN", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We engage with independent subcontractors and contract manufacturers such as Hon Hai Precision Industry Co., Ltd., Wistron Corporation, and Fabrinet to perform assembly, testing and packaging of our final products.", "rationale": "NVIDIA engages subcontractors to perform assembly, testing and packaging.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_SK hynix Inc.", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We purchase memory from SK Hynix Inc., Micron Technology, Inc., and Samsung.", "rationale": "NVIDIA sources memory from SK hynix.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "MRVL", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We purchase memory from SK Hynix Inc., Micron Technology, Inc., and Samsung.", "rationale": "NVIDIA sources memory from Micron Technology.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Samsung Electronics Co., Ltd.", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We purchase memory from SK Hynix Inc., Micron Technology, Inc., and Samsung.", "rationale": "NVIDIA sources memory from Samsung.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Asia-Pacific region", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our supply chain is mainly concentrated in the Asia-Pacific region.", "rationale": "NVIDIA's supply chain is concentrated in the Asia-Pacific region.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics,\" as described in Note 14 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.", "rationale": "The sentence explicitly states that NVIDIA's two operating segments are Compute & Networking and Graphics.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics,\" as described in Note 14 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.", "rationale": "The sentence explicitly states that NVIDIA's two operating segments are Compute & Networking and Graphics.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Santa Clara, California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Headquartered in Santa Clara, California,\" NVIDIA was incorporated in California in April 1993 and reincorporated in Delaware in April 1998.", "rationale": "The text explicitly states headquarters location.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Blackwell architecture", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "We shipped customer samples of our Blackwell architecture in the second quarter.", "rationale": "The phrase 'our Blackwell architecture' indicates NVIDIA developed the architecture.", "strength_score": 0.9}}, {"source_id": "Gaming GPU", "target_id": "Ethereum", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "For example, several years ago, our Gaming GPUs began to be used for mining digital currencies, such as Ethereum.", "rationale": "The sentence states that GPUs began to be used for Ethereum mining, indicating a usage relationship from the GPU product to the Ethereum technology.", "strength_score": 0.6}}, {"source_id": "NVIDIA GPU", "target_id": "Ethereum", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Gaming GPUs began to be used for mining digital currencies, such as Ethereum.", "rationale": "The sentence states GPUs were used to mine Ethereum, indicating a use of Ethereum by the GPU.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"we are generally in various stages of transitioning the architectures of our Data Center, Gaming, Professional Visualization, and Automotive products.\"", "rationale": "Data Center is explicitly listed among the architectures being transitioned, indicating it is a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Gaming", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"we are generally in various stages of transitioning the architectures of our Data Center, Gaming, Professional Visualization, and Automotive products.\"", "rationale": "Gaming is explicitly listed among the architectures being transitioned, indicating it is a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Professional Visualization", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"we are generally in various stages of transitioning the architectures of our Data Center, Gaming, Professional Visualization, and Automotive products.\"", "rationale": "Professional Visualization is explicitly listed among the architectures being transitioned, indicating it is a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"we are generally in various stages of transitioning the architectures of our Data Center, Gaming, Professional Visualization, and Automotive products.\"", "rationale": "Automotive is explicitly listed among the architectures being transitioned, indicating it is a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Taiwan", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"geopolitical tensions, such as those involving Taiwan and China, which comprise a significant portion of our revenue and where we have suppliers, contract manufacturers, and assembly partners who are critical to our supply continuity, could have a material adverse impact on us.\"", "rationale": "The text notes we have suppliers in Taiwan, implying sources from that region.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "China", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"geopolitical tensions, such as those involving Taiwan and China, which comprise a significant portion of our revenue and where we have suppliers, contract manufacturers, and assembly partners who are critical to our supply continuity, could have a material adverse impact on us.\"", "rationale": "The text notes we have suppliers in China, implying sources from that region.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Third party", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "We use third parties to manufacture and assemble our products, and we have long manufacturing lead times.", "rationale": "NVIDIA states it uses third parties to manufacture and assemble its products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Sales to one customer, Customer A, represented 13% of total revenue for fiscal 2024, attributable to the Compute & Networking segment.", "rationale": "The sentence links revenue to the Compute & Networking segment, indicating the segment is part of NVIDIA.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "One indirect customer purchasing mainly through system integrators and distributors, including Customer A, estimated to represent approx. 19% of total revenue for fiscal 2024, Compute & Networking segment.", "rationale": "The sentence identifies Customer A as a customer in the revenue context, implying NVIDIA sells to Customer A.", "strength_score": 0.86}}, {"source_id": "NVDA", "target_id": "DGX Cloud", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "In fiscal year 2024, we launched the NVIDIA DGX Cloud, an AI-training-as-a-service platform which includes cloud-based infrastructure and software for AI, customizable pretrained AI models, and access to NVIDIA experts.", "rationale": "The sentence states NVIDIA launched the DGX Cloud, indicating development by NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA AI Enterprise", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "In addition to software delivered to customers as an integral part of our data center computing platform, we offer paid licenses to NVIDIA AI Enterprise, a comprehensive suite of enterprise-grade AI software and NVIDIA vGPU software for graphics-rich virtual desktops and workstations.", "rationale": "The sentence describes NVIDIA offering NVIDIA AI Enterprise, indicating development/ownership of the product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA VGPU Software", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "In addition to software delivered to customers as an integral part of our data center computing platform, we offer paid licenses to NVIDIA AI Enterprise, a comprehensive suite of enterprise-grade AI software and NVIDIA vGPU software for graphics-rich virtual desktops and workstations.", "rationale": "The sentence lists NVIDIA vGPU software as part of the NVIDIA AI Enterprise offering, indicating it is an NVIDIA product.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "... including the CUDA parallel programming model, the CUDA-X collection of acceleration libraries, APIs, SDKs, and domain-specific application frameworks.", "rationale": "CUDA is mentioned as part of NVIDIA's software stack, indicating development by NVIDIA.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "... including the CUDA parallel programming model, the CUDA-X collection of acceleration libraries, APIs, SDKs, and domain-specific application frameworks.", "rationale": "CUDA-X is named as part of the software stack developed by NVIDIA.", "strength_score": 0.8}}, {"source_id": "NVIDIA Accelerated Computing Platform", "target_id": "NVIDIA GPU", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "At the foundation of the NVIDIA accelerated computing platform are our GPUs.", "rationale": "The GPUs form the foundation of the platform, implying the platform uses GPUs.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "Automotive", "type": "PARTNERS_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We also have partnerships in automotive, healthcare, financial services, manufacturing, and retail among others, to accelerate the adoption of AI.", "rationale": "The sentence explicitly states partnerships in automotive.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Healthcare", "type": "PARTNERS_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We also have partnerships in automotive, healthcare, financial services, manufacturing, and retail among others, to accelerate the adoption of AI.", "rationale": "The sentence explicitly states partnerships in healthcare.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Financial Services", "type": "PARTNERS_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We also have partnerships in automotive, healthcare, financial services, manufacturing, and retail among others, to accelerate the adoption of AI.", "rationale": "The sentence explicitly states partnerships in financial services.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Manufacturing", "type": "PARTNERS_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We also have partnerships in automotive, healthcare, financial services, manufacturing, and retail among others, to accelerate the adoption of AI.", "rationale": "The sentence explicitly states partnerships in manufacturing.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Retail", "type": "PARTNERS_WITH", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We also have partnerships in automotive, healthcare, financial services, manufacturing, and retail among others, to accelerate the adoption of AI.", "rationale": "The sentence explicitly states partnerships in retail.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "NVIDIA Blackwell architecture", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "In fiscal year 2025, we launched the NVIDIA Blackwell architecture, a full set of data center scale infrastructure that includes GPUs, CPUs, DPUs, interconnects, switch chips and systems, and networking adapters.", "rationale": "The sentence states NVIDIA launched the Blackwell architecture, indicating development by NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "InfiniBand", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our networking offerings include end-to-end platforms for InfiniBand and Ethernet, consisting of network adapters, cables, DPUs, switch chips and systems, as well as a full software stack.", "rationale": "The sentence indicates InfiniBand is part of NVIDIA's networking offerings.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Ethernet", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Our networking offerings include end-to-end platforms for InfiniBand and Ethernet, consisting of network adapters, cables, DPUs, switch chips and systems, as well as a full software stack.", "rationale": "The sentence indicates Ethernet is part of NVIDIA's networking offerings.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Santa Clara, California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Headquartered in Santa Clara, California, NVIDIA was incorporated in California in April 1993 and reincorporated in Delaware in April 1998..", "rationale": "The text states that NVIDIA is headquartered in Santa Clara, California, which maps to the IS_HEADQUARTERED_IN relationship.", "strength_score": 0.95}}, {"source_id": "Gaming industry", "target_id": "NVIDIA GPU", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Gamers choose NVIDIA GPUs to enjoy immersive, increasingly cinematic virtual worlds.", "rationale": "Explicit statement that gamers use NVIDIA GPUs.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Securities and Exchange Commission", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "we file with the Securities and Exchange Commission, or the SEC.", "rationale": "The sentence states that NVIDIA files with the SEC, indicating regulatory oversight by the Securities and Exchange Commission.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Customer A 12 %", "rationale": "Direct customer listed with a material share of total revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Customer B 12 %", "rationale": "Direct customer listed with a material share of total revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer C", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Customer C 12 %", "rationale": "Direct customer listed with a material share of total revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer D", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Customer D *", "rationale": "Direct customer listed with a revenue share indicated as less than 10%.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "higher mix of Data Center revenue", "rationale": "Data Center is referenced as a revenue-mixing segment within the company.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Compute & Networking segment", "rationale": "Compute & Networking is described as a company segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Sales to direct customers which represented 10% or more of total revenue, all of which were primarily attributable to the Compute & Networking segment, are presented in the following table:\"", "rationale": "The Compute & Networking segment is identified as the primary segment for revenue, indicating NVIDIA has this segment.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Customer A 14%\"", "rationale": "Direct customer with a 14% share of total revenue, indicating NVIDIA supplies to Customer A.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Customer B 11% *\"", "rationale": "Direct customer with a significant revenue share (11%), indicating NVIDIA supplies to Customer B.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer C", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Customer C 11% *\"", "rationale": "Direct customer with a significant revenue share (11%), indicating NVIDIA supplies to Customer C.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer D", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Customer D 10%\"", "rationale": "Direct customer with a 10% share of total revenue, indicating NVIDIA supplies to Customer D.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer E", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "\"Customer E 10%\"", "rationale": "Direct customer with a 10% share of total revenue (Six Months), indicating NVIDIA supplies to Customer E.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Reportable Segments Revenue by Reportable Segments Compute & Networking $116,193 million | $47,405 million | Up 145%", "rationale": "The segment is identified as a reportable segment of NVIDIA, indicating a formal internal division.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Graphics $14,304 million | $13,517 million | Up 6%", "rationale": "Graphics is listed as a reportable segment of NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "All Other", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "All Other $(6,507) million | $(4,890) million | Increased loss 33% Total", "rationale": "All Other is listed as a reportable segment of NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Hopper computing platform", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Revenue from Data Center computing grew 162% driven primarily by demand for our Hopper computing platform used for large language models, recommendation engines, and generative AI applications.", "rationale": "The text references the Hopper computing platform as a product associated with NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GeForce RTX 40 Series GPU", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Graphics revenue year over year increase was driven by sales of our GeForce RTX 40 Series GPUs.", "rationale": "The sentence attributes NVIDIA as the producer of GeForce RTX 40 Series GPUs.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Direct Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Direct Customers – Sales to direct customers which represented 10% or more of total revenue, all primarily attributable to Compute & Networking segment: Direct Customer A 12% | Less than 10% prior year", "rationale": "Direct Customer A is cited as a direct customer representing 10% or more of total revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Direct Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Direct Customer B 11% | 13% prior year", "rationale": "Direct Customer B is cited as a direct customer representing a significant share of revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Direct Customer C", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "Direct Customer C 11% | Less than 10% prior year", "rationale": "Direct Customer C is cited as a direct customer representing a significant share of revenue.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "The year over year increase was primarily due to strong Data Center revenue growth of 427%.", "rationale": "Data Center is referenced as a distinct revenue segment, implying NVIDIA Corporation has a Data Center segment.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "both of which were attributable to the Compute & Networking segment.", "rationale": "Compute & Networking is cited as a segment for which direct and indirect customer revenues are attributed, implying NVIDIA has this segment.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "Sales to one direct customer, Customer A, represented 13% of total revenue and sales to a second direct customer, Customer B, represented 11% of total revenue for the first quarter of fiscal year 2025,", "rationale": "Direct customer sales indicate NVIDIA supplies to Customer A.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "Sales to one direct customer, Customer A, represented 13% of total revenue and sales to a second direct customer, Customer B, represented 11% of total revenue for the first quarter of fiscal year 2025,", "rationale": "Direct customer sales indicate NVIDIA supplies to Customer B.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "Sales to one direct customer, Customer A, and sales to another direct customer, Customer B, represented 13% and 11% of total revenue, respectively, for the first quarter of fiscal year 2025.", "rationale": "The sentence states that NVIDIA sells to Customer A, indicating NVIDIA supplies to Customer A.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "Sales to one direct customer, Customer A, and sales to another direct customer, Customer B, represented 13% and 11% of total revenue, respectively, for the first quarter of fiscal year 2025.", "rationale": "The sentence states that NVIDIA sells to Customer B, indicating NVIDIA supplies to Customer B.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "Both were attributable to the Compute & Networking segment.", "rationale": "The sentence ties the revenues from these customers to the Compute & Networking segment, indicating the segment is a NVIDIA internal segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Sales to one customer, Customer A, represented 13% of total revenue for fiscal year 2024, which was attributable to the Compute & Networking segment.", "rationale": "Customer A is named as a purchaser of NVIDIA's products and accounted for a notable portion of revenue, indicating a supplier-to-customer relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "7", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "which was attributable to the Compute & Networking segment.", "rationale": "The Compute & Networking segment is referenced as a distinct segment within NVIDIA's revenue attribution, implying the existence of a company segment.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Compute & Networking revenue – The year over year increase in the first quarter of fiscal year 2026 was driven by demand for our accelerated computing platform used for large language models, recommendation engines, and generative and agentic AI applications.", "rationale": "Explicitly states Compute & Networking as a segment of NVIDIA.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Data Center computing", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Revenue from Data Center computing grew 76% year-on-year compared to the first quarter of fiscal year 2025, driven by demand for our Blackwell computing platform.", "rationale": "Explicitly states Data Center computing as a segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Data Center networking", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Revenue from Data Center networking grew 56% year-on-year compared to the first quarter of fiscal year 2025 driven by the growth of NVLink compute fabric in our GB200 systems and continued adoption of Ethernet for AI solutions at cloud service providers and consumer internet companies.", "rationale": "Explicitly states Data Center networking as a segment.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Graphics revenue – The year over year increase in the first quarter of fiscal year 2026 was driven by sales of our Blackwell architecture.", "rationale": "Explicitly states Graphics as a segment.", "strength_score": 0.95}}, {"source_id": "GB200", "target_id": "NVLink compute fabric", "type": "USES", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "growth of NVLink compute fabric in our GB200 systems", "rationale": "GB200 system uses NVLink compute fabric.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Direct Customers – Sales to one direct customer, Customer A, represented 16% of total revenue and sales to a second direct customer, Customer B, represented 14% of total revenue for the first quarter of fiscal year 2026, both of which were attributable to the Compute & Networking segment.", "rationale": "Direct sales to Customer A are explicitly described.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "Direct Customers – Sales to one direct customer, Customer A, represented 16% of total revenue and sales to a second direct customer, Customer B, represented 14% of total revenue for the first quarter of fiscal year 2026, both of which were attributable to the Compute & Networking segment.", "rationale": "Direct sales to Customer B are explicitly described.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Sales to direct Customers, A, B, C and D represented 14% | 11% | 11% | and 10% of total revenue, respectively, for the second quarter of fiscal year 2025, all of which were primarily attributable to the Compute & Networking segment.", "rationale": "NVIDIA sells to direct customers A, B, C and D, indicating a supplies-to relationship.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer B", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Sales to direct Customers, A, B, C and D represented 14% | 11% | 11% | and 10% of total revenue, respectively, for the second quarter of fiscal year 2025, all of which were primarily attributable to the Compute & Networking segment.", "rationale": "NVIDIA sells to direct customer B, indicating a supplies-to relationship.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer C", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Sales to direct Customers, A, B, C and D represented 14% | 11% | 11% | and 10% of total revenue, respectively, for the second quarter of fiscal year 2025, all of which were primarily attributable to the Compute & Networking segment.", "rationale": "NVIDIA sells to direct customer C, indicating a supplies-to relationship.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer D", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Sales to direct Customers, A, B, C and D represented 14% | 11% | 11% | and 10% of total revenue, respectively, for the second quarter of fiscal year 2025, all of which were primarily attributable to the Compute & Networking segment.", "rationale": "NVIDIA sells to direct customer D, indicating a supplies-to relationship.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer E", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "two indirect customers which primarily purchase our products through system integrators and distributors, including through Customer B and Customer E, are estimated to each represent 10% or more of total revenue, attributable to the Compute & Networking segment.", "rationale": "NVIDIA earns revenue from indirect customer E, indicating a supplies-to relationship.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "two indirect customers which primarily purchase our products through system integrators and distributors, including through Customer B and Customer E, are estimated to each represent 10% or more of total revenue, attributable to the Compute & Networking segment.", "rationale": "The revenue is attributed to the Compute & Networking segment, indicating a formal internal segment of the company.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Santa Clara", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Our headquarters is in Santa Clara, California.", "rationale": "The sentence states the headquarters location in Santa Clara, indicating the company is headquartered in that city.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "United States", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan..", "rationale": "The sentence mentions facilities throughout the U.S., indicating operating presence in the United States.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "China", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan..", "rationale": "The sentence mentions facilities primarily in China, indicating operating presence in China.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "India", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan..", "rationale": "The sentence mentions facilities primarily in India, indicating operating presence in India.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Israel", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan..", "rationale": "The sentence mentions facilities primarily in Israel, indicating operating presence in Israel.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Taiwan", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan..", "rationale": "The sentence mentions facilities primarily in Taiwan, indicating operating presence in Taiwan.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Santa Clara", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"Our headquarters is in Santa Clara, California.\"", "rationale": "Direct statement of headquarters location in Santa Clara.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "California", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"Our headquarters is in Santa Clara, California.\"", "rationale": "The state is specified as part of the headquarters location.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "United States", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan.\"", "rationale": "Mentions presence/facilities across the United States.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "China", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan.\"", "rationale": "Mentions China as a location of facilities.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "India", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan.\"", "rationale": "Mentions India as a location of facilities.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Israel", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan.\"", "rationale": "Mentions Israel as a location of facilities.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Taiwan", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"throughout the U.S. and in various international locations, primarily in China, India, Israel, and Taiwan.\"", "rationale": "Mentions Taiwan as a location of facilities.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Reportable segments: Compute & Networking and Graphics; 'All Other' category includes corporate expenses unallocated to segments.", "rationale": "Compute & Networking is listed as a reportable segment for NVIDIA, establishing a segment relationship.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Reportable segments: Compute & Networking and Graphics; 'All Other' category includes corporate expenses unallocated to segments.", "rationale": "Graphics is listed as a reportable segment for NVIDIA, establishing a segment relationship.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "All Other", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Reportable segments: Compute & Networking and Graphics; 'All Other' category includes corporate expenses unallocated to segments.", "rationale": "All Other is described as a category within NVIDIA's reportable segments, treated as a segment for structuring purposes.", "strength_score": 0.7}}, {"source_id": "Compute & Networking segment", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Compute & Networking includes data center platforms, AI solutions, networking, automotive, Jetson, and DGX Cloud.", "rationale": "Data Center is explicitly included under Compute & Networking as a market/segment.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Customer A", "type": "SUPPLIES_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part1item1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "Sales to direct customers that are 10% or more of total revenue: Customer A 14% for three and six months ended Jul 28, 2024", "rationale": "The text indicates NVIDIA has a direct customer relationship with Customer A and provides sales to them.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "We have not received licenses from the USG to ship restricted products to China.", "rationale": "The sentence explicitly states that NVIDIA relies on licenses from a government authority (USG), indicating regulatory oversight by the United States Government.", "strength_score": 0.85}}, {"source_id": "A100", "target_id": "Export licensing requirements", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Specific export license requirements affect key products including A100 and H100 chips.", "rationale": "The sentence states that export license requirements affect key products, specifically naming the A100 chip, implying the product is subject to those requirements.", "strength_score": 0.85}}, {"source_id": "H100", "target_id": "Export licensing requirements", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Specific export license requirements affect key products including A100 and H100 chips.", "rationale": "The sentence states that export license requirements affect key products, specifically naming the H100 chip, implying the product is subject to those requirements.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "Russia", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "In fiscal year 2023, we stopped direct sales to Russia and closed business operations in Russia.", "rationale": "The sentence explicitly states NVIDIA stopped direct sales to Russia and closed business operations in Russia, indicating NVIDIA had operations in Russia.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "the USG has changed and may again change the export control rules at any time and further subject a wider range of our products to export restrictions and licensing requirements", "rationale": "The sentence links US Government export controls to NVIDIA's products, implying NVIDIA's products are regulated by the US government.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "Hong Kong", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "which are warehoused in and distributed from Hong Kong", "rationale": "The phrase describes NVIDIA's products being warehoused in and distributed from Hong Kong, indicating operations in that region.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "Data Center", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1A", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "Sales to China decreased from 19% to 14% of Data Center revenue from fiscal 2023 to 2024; shipments to China are limited by USG licenses.", "rationale": "The text references Data Center revenue, indicating Data Center is a defined internal segment of NVIDIA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "MRVL", "type": "SOURCES_FROM", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "\"Micron, a supplier of ours.\"", "rationale": "The text explicitly states Micron is a supplier to NVIDIA, indicating NVIDIA sources from Micron.", "strength_score": 0.8}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "\"the USG has changed and may again change the export control rules at any time and further subject a wider range of our products to export restrictions and licensing requirements\"", "rationale": "The sentence ties US government actions to regulatory controls on NVIDIA's products.", "strength_score": 0.9}}, {"source_id": "Gaming GPU", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "\"The USG has already imposed export controls restricting certain gaming GPUs,\"", "rationale": "Direct statement that USG has imposed export controls on gaming GPUs, indicating regulation.", "strength_score": 0.9}}, {"source_id": "High-speed network interconnect", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "\"The USG may also impose export controls on our networking products, such as high-speed network interconnects, to limit the ability of downstream parties to create large clusters for frontier model training.\"", "rationale": "USG export controls on networking products described, implying regulatory relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "the USG’s export controls", "rationale": "NVIDIA is subject to export controls administered by the United States Government.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Hong Kong", "type": "OPERATES_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "warehoused in and distributed from Hong Kong", "rationale": "NVIDIA has warehousing and distribution activity in Hong Kong, indicating operations in the region.", "strength_score": 0.6}}, {"source_id": "NVDA", "target_id": "Patent", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on patents to protect its IP, indicating a uses relationship from the company to the patent.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Trademark", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on trademarks to protect its IP, indicating a uses relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Trade secret", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on trade secrets to protect its IP, indicating a uses relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Employee nondisclosure agreement", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on employee nondisclosure agreements to protect its IP, indicating a uses relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Third-party nondisclosure agreement", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on third-party nondisclosure agreements to protect its IP, indicating a uses relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Licensing arrangement", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2024-01-28", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000029/nvda-********.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The sentence states NVIDIA relies on licensing arrangements to protect its IP, indicating a uses relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "CUDA", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "our full-stack includes the foundational CUDA programming model that runs on all NVIDIA GPUs", "rationale": "The line states that CUDA is part of NVIDIA's stack, implying NVIDIA uses CUDA in its platform.", "strength_score": 0.8}}, {"source_id": "NVIDIA GPU", "target_id": "CUDA", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "our full-stack includes the foundational CUDA programming model that runs on all NVIDIA GPUs", "rationale": "CUDA runs on NVIDIA GPUs, indicating the GPUs use CUDA.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Patent", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The text states that the company relies on patents to protect its IP, indicating a use relationship with Patent.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Trademark", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The text states that the company relies on trademarks to protect its IP, indicating a use relationship with Trademark.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Trade Secret", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The text states that the company relies on trade secrets to protect its IP, indicating a use relationship with Trade Secret.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Nondisclosure Agreement", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The text references employee and third-party nondisclosure agreements as part of IP protection, indicating a use relationship with Nondisclosure Agreement.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "Licensing Arrangement", "type": "USES", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "We rely primarily on a combination of patents, trademarks, trade secrets, employee and third-party nondisclosure agreements, and licensing arrangements to protect our IP in the United States and internationally.", "rationale": "The text references licensing arrangements as part of IP protection, indicating a use relationship with Licensing Arrangement.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.\"", "rationale": "The text states NVIDIA is subject to US government laws and regulations.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "A100", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"our A100 and H100 integrated circuits\"", "rationale": "The text refers to A100 as a product NVIDIA develops.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "H100", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"our A100 and H100 integrated circuits\"", "rationale": "The text refers to H100 as a product NVIDIA develops.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA DGX system", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"NVIDIA DGX, HGX, and MGX systems\"", "rationale": "The text mentions NVIDIA DGX system as part of the product family.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA HGX system", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"NVIDIA DGX, HGX, and MGX systems\"", "rationale": "The text mentions NVIDIA HGX system as part of the product family.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVIDIA MGX system", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"NVIDIA DGX, HGX, and MGX systems\"", "rationale": "The text mentions NVIDIA MGX system as part of the product family.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "RTX 4090", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"RTX 4090\"", "rationale": "RTX 4090 is listed among NVIDIA products in export control text.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "RTX 6000 Ada", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"RTX 6000 Ada\"", "rationale": "RTX 6000 Ada is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "A800", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"A800\"", "rationale": "A800 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "H200", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"H200\"", "rationale": "H200 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "H800", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"H800\"", "rationale": "H800 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "B100", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"B100\"", "rationale": "B100 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "B200", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"B200\"", "rationale": "B200 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "GB200", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"GB200\"", "rationale": "GB200 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "L4", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"L4\"", "rationale": "L4 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "L40S", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"L40S\"", "rationale": "L40S is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVL 72", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"NVL 72\"", "rationale": "NVL 72 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "NVL 36", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"NVL 36\"", "rationale": "NVL 36 is listed among NVIDIA products.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Blackwell systems", "type": "DEVELOPS", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"Blackwell systems\"", "rationale": "The text references Blackwell systems.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Export licensing requirements", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-K", "period_of_report": "2025-01-26", "section": "1", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm"}, "evidence": "\"On October 23, 2023, the USG informed us that the licensing requirements were effective immediately for shipments of our A100, A800, H100, H800, and L40S products\"", "rationale": "The licensing requirements apply to NVIDIA products.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "United States Government", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "The United States has imposed unilateral controls restricting GPUs and associated products, and it is likely that additional unilateral or multilateral controls will be adopted.", "rationale": "Explicit government-imposed export controls described as restricting NVIDIA's GPUs and related products.", "strength_score": 0.92}}, {"source_id": "NVDA", "target_id": "Foreign Corrupt Practices Act", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "anti-corruption, including the Foreign Corrupt Practices Act;", "rationale": "The text states NVIDIA is subject to anti-corruption laws, explicitly including the Foreign Corrupt Practices Act.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "Foreign Corrupt Practices Act", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "anti-corruption, including the Foreign Corrupt Practices Act;", "rationale": "The text states the company is subject to laws including the Foreign Corrupt Practices Act, indicating the relationship.", "strength_score": 0.85}}, {"source_id": "NVDA", "target_id": "French Competition Authority", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item1a", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "The French Competition Authority collected information from us regarding our business and competition in the graphics card and cloud service provider market as part of an ongoing inquiry into competition in those markets.", "rationale": "The regulator collected information from NVIDIA as part of an ongoing inquiry, implying regulatory oversight of NVIDIA by that authority.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Section 4(a)(2) of the Securities Act", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part2item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "The above securities were issued in transactions not involving a public offering pursuant to an exemption from registration set forth in Section 4(a)(2) of the Securities Act (and Regulation D or Regulation S promulgated thereunder)..", "rationale": "The sentence ties the exemption to Section 4(a)(2) of the Securities Act, indicating the company's issuance relied on that provision.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Regulation D", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part2item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "The above securities were issued in transactions not involving a public offering pursuant to an exemption from registration set forth in Section 4(a)(2) of the Securities Act (and Regulation D or Regulation S promulgated thereunder)..", "rationale": "The sentence mentions Regulation D as part of the exemption, linking the company to Regulation D within the same sentence.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "Regulation S", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part2item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "The above securities were issued in transactions not involving a public offering pursuant to an exemption from registration set forth in Section 4(a)(2) of the Securities Act (and Regulation D or Regulation S promulgated thereunder)..", "rationale": "The sentence mentions Regulation S as part of the exemption, linking the company to Regulation S within the same sentence.", "strength_score": 0.75}}, {"source_id": "NVDA", "target_id": "UNRESOLVED_Acquired Company", "type": "ACQUIRED", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-07-28", "section": "part2item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm"}, "evidence": "to key employees of a company we acquired.", "rationale": "The sentence states that NVIDIA acquired a company, and the issued shares were to key employees of that acquired company, establishing an acquisition relationship.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Section 4(a)(2) of the Securities Act", "type": "IS_SUBJECT_TO", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-04-28", "section": "part2item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm"}, "evidence": "exemption from registration set forth in Section 4(a)(2) of the Securities Act.", "rationale": "The sentence states the transaction was exempt under a specific section of the Securities Act, indicating NVIDIA is subject to that law/regulation for this transaction.", "strength_score": 0.7}}, {"source_id": "NVDA", "target_id": "Santa Clara", "type": "IS_HEADQUARTERED_IN", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Headquartered in Santa Clara, California,", "rationale": "The text states the company is headquartered in Santa Clara, establishing the headquarter location to that geographic region.", "strength_score": 0.9}}, {"source_id": "NVDA", "target_id": "Compute & Networking segment", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics,\" as described in Note 14 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.", "rationale": "The sentence explicitly lists Compute & Networking as one of NVIDIA's operating segments.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Graphics", "type": "HAS_SEGMENT", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2024-10-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm"}, "evidence": "Our two operating segments are \"Compute & Networking\" and \"Graphics,\" as described in Note 14 of the Notes to Condensed Consolidated Financial Statements in Part I, Item 1 of this Quarterly Report on Form 10-Q.", "rationale": "The sentence explicitly lists Graphics as one of NVIDIA's operating segments.", "strength_score": 0.95}}, {"source_id": "NVDA", "target_id": "Securities and Exchange Commission", "type": "IS_REGULATED_BY", "properties": {"source": {"report_type": "10-Q", "period_of_report": "2025-04-27", "section": "part1item2", "source_url": "https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-********.htm"}, "evidence": "our other filings with the SEC.", "rationale": "The text states that NVIDIA's filings are with the SEC, indicating regulatory oversight.", "strength_score": 0.8}}]}