2025-08-17 13:58:22,105 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-58-22.log
2025-08-17 13:58:31,730 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:58:31,730 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:58:31,730 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:58:31,742 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:58:31,742 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:58:33,555 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:58:33,555 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:58:33,555 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:58:50,073 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:58:50,073 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:58:50,073 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:58:50,793 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:58:50,793 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 13:58:50,793 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: NVIDIA CORP ---
2025-08-17 13:58:50,793 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-17 13:59:35,697 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-17 13:59:35,701 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 46 for NVIDIA CORP (ID: a3a20ae7-aa9d-4cfd-9a91-a36d6e5c7562)
2025-08-17 13:59:35,703 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3582
