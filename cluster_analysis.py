import sys
import json
from pathlib import Path
import pandas as pd
import numpy as np
import umap
import hdbscan
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any

# --- Setup Paths and Logger ---
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from logger import log
import config
from src.data_retriever import DataRetriever

# --- Data Loading and Vectorization Functions (No Changes Here) ---

# -- new function for json file--

def load_relationships_from_json(file_path: Path) -> List[str]:
    """
    Load a single JSON file that contains a top-level "relationships" array and
    return a list of unique relationship_type strings.

    Example input shape (abbreviated):
    {
        "entities": [...],
        "relationships": [
            {"entity_1": "...", "entity_2": "...", "relationship_type": "...", ...},
            ...
        ]
    }
    """
    log.info(f"Loading relationship phrases from single JSON file: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            obj = json.load(f)
        rels = obj.get("relationships", []) if isinstance(obj, dict) else []
        phrases = {
            (rel.get("relationship_type") or "").strip()
            for rel in rels
            if isinstance(rel, dict) and rel.get("relationship_type")
        }
        unique_phrases = sorted(phrases)
        log.info(f"Found {len(unique_phrases)} unique relationship phrases in JSON.")
        return unique_phrases
    except FileNotFoundError:
        log.error(f"FATAL: The specified JSON file was not found: {file_path}")
        return []
    except json.JSONDecodeError as e:
        log.error(f"Error decoding JSON from file {file_path}. Error: {e}")
        return []

def load_relationships_from_file(file_path: Path) -> List[str]:
    # ... (code is identical to the previous version) ...
    log.info(f"Loading relationship phrases from: {file_path}")
    relationship_phrases = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line)
                for rel in data.get("relationships", []):
                    phrase = rel.get("relationship_type")
                    if phrase:
                        relationship_phrases.add(phrase.strip())
        
        unique_phrases = list(relationship_phrases)
        log.info(f"Found {len(unique_phrases)} unique relationship phrases.")
        return unique_phrases
    except FileNotFoundError:
        log.error(f"FATAL: The specified output file was not found: {file_path}")
        return []
    except json.JSONDecodeError as e:
        log.error(f"Error decoding JSON from file {file_path}. Error: {e}")
        return []

def vectorize_phrases(phrases: List[str], model) -> np.ndarray:
    # ... (code is identical to the previous version) ...
    if not phrases:
        log.warning("Phrase list is empty. Cannot vectorize.")
        return np.array([])
        
    log.info(f"Vectorizing {len(phrases)} phrases using the BGE model...")
    embeddings = model.embed_documents(phrases)
    log.info(f"Vectorization complete. Embedding matrix shape: {np.array(embeddings).shape}")
    return np.array(embeddings)

# --- NEW / RE-INTRODUCED UMAP FUNCTION ---
def reduce_dimensionality_with_umap(embeddings: np.ndarray, n_components: int) -> np.ndarray:
    """
    Reduces the dimensionality of embeddings using UMAP.

    Args:
        embeddings (np.ndarray): The high-dimensional embedding matrix.
        n_components (int): The target number of dimensions.

    Returns:
        np.ndarray: The low-dimensional embedding matrix.
    """
    if embeddings.shape[0] < 2:
        log.warning("Not enough data points for UMAP. Skipping.")
        return np.array([])
        
    log.info(f"Starting dimensionality reduction with UMAP from {embeddings.shape[1]} to {n_components} dimensions...")
    
    # UMAP configuration. These are good starting parameters.
    umap_reducer = umap.UMAP(
        n_neighbors=5,
        min_dist=0.1,
        n_components=n_components,
        metric='cosine',
        random_state=42
    )
    
    reduced_embeddings = umap_reducer.fit_transform(embeddings)
    log.info(f"UMAP reduction complete. New matrix shape: {reduced_embeddings.shape}")
    
    return reduced_embeddings

# --- HDBSCAN and Analysis Functions (No Changes Here) ---
def cluster_embeddings_with_hdbscan(embeddings: np.ndarray) -> np.ndarray:
    # ... (code is identical to the previous version) ...
    if embeddings.shape[0] < 2:
        log.warning("Not enough data points to perform clustering. Skipping.")
        return np.array([])

    log.info(f"Starting clustering with HDBSCAN...")
    
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=3,
        min_samples=3,
        metric='euclidean'
    )
    
    clusterer.fit(embeddings)
    labels = clusterer.labels_
    
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    n_noise = np.sum(labels == -1)
    
    log.info(f"HDBSCAN clustering complete.")
    log.info(f"Found {n_clusters} clusters and {n_noise} noise points.")
    
    return labels

def analyze_and_display_clusters(df: pd.DataFrame):
    # ... (code is identical, but now takes a DataFrame as input) ...
    log.info("\n" + "="*80)
    log.info("--- CLUSTER ANALYSIS RESULTS ---")
    log.info("="*80)

    for cluster_id in sorted(df['cluster_id'].unique()):
        if cluster_id == -1:
            print("\n--- Noise Points ---\n")
        else:
            print(f"\n--- Cluster {cluster_id} ---\n")
        
        phrases_in_cluster = df[df['cluster_id'] == cluster_id]['phrase'].tolist()
        for phrase in phrases_in_cluster:
            print(f"  - {phrase}")
            
    output_path = config.OUTPUT_DIR / "cluster_analysis_results.csv"
    df.to_csv(output_path, index=False)
    log.info(f"\nFull cluster analysis saved to: {output_path}")



# --- UPDATED MAIN FUNCTION ---
def main():
    """
    Main function to run the full UMAP -> HDBSCAN clustering pipeline.
    """
    log.info("--- Starting UMAP -> HDBSCAN Clustering Analysis ---")

    # --- Step 1: Load and Vectorize ---
    # output_files = sorted(config.OUTPUT_DIR.glob("*.jsonl"), reverse=True)
    # if not output_files:
    #     log.error(f"No .jsonl output files found in {config.OUTPUT_DIR}. Please run the main pipeline first.")
    #     return
    
    # latest_output_file = output_files[0]  
    latest_output_file = config.OUTPUT_DIR / "texas.json"
    phrases = load_relationships_from_json(latest_output_file)
    print(phrases)
    if not phrases: return

    try:
        retriever = DataRetriever(
            qdrant_url=config.QDRANT_URL, collection_name=config.QDRANT_COLLECTION_NAME,
            query_file_path=config.SEARCH_QUERIES_PATH
        )
        embeddings_768d = vectorize_phrases(phrases, retriever.embedding_model)
        print(f"First few numbers of embeddings: {embeddings_768d[:3]}")
        print(f"Size of the embedding matrix: {embeddings_768d.shape}")

    except Exception as e:
        log.error(f"Failed to initialize or use embedding model. Error: {e}")
        return

    # --- Step 2: Reduce Dimensionality for Clustering ---
    # We reduce to a moderate number of dimensions for clustering, not just 2.
    # This often provides a better representation for clustering than the full dimension.
    embeddings_for_clustering = reduce_dimensionality_with_umap(embeddings_768d, n_components=5)
    print(f"First few numbers of embeddings for clustering: {embeddings_for_clustering[:7]}")

    # --- Step 3: Cluster the Reduced Embeddings ---
    cluster_labels = cluster_embeddings_with_hdbscan(embeddings_for_clustering)

    
    # Create the final DataFrame
    results_df = pd.DataFrame({
        'phrase': phrases,
        'cluster_id': cluster_labels
    })
    
    # --- Step 5: Analyze and Visualize ---
    if not results_df.empty:
        analyze_and_display_clusters(results_df)
        

    log.info("--- UMAP -> HDBSCAN Clustering Analysis Finished ---")

if __name__ == "__main__":
    main()