2025-08-11 17:27:37,139 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-11_17-27-37.log
2025-08-11 17:27:37,184 - Finance_Knowledge_Graph - INFO - [data_retriever.py:131] - --- Running DataRetriever Test ---
2025-08-11 17:27:37,259 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-11 17:27:42,379 - Finance_Knowledge_Graph - INFO - [data_retriever.py:68] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-11 17:27:42,391 - Finance_Knowledge_Graph - INFO - [data_retriever.py:43] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-11 17:27:42,400 - Finance_Knowledge_Graph - INFO - [data_retriever.py:47] - Using device: cpu
2025-08-11 17:28:19,744 - Finance_Knowledge_Graph - INFO - [data_retriever.py:56] - Embedding model initialized successfully.
2025-08-11 17:28:19,744 - Finance_Knowledge_Graph - INFO - [data_retriever.py:37] - DataRetriever initialized successfully.
2025-08-11 17:28:19,744 - Finance_Knowledge_Graph - INFO - [data_retriever.py:85] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-11 17:28:29,223 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:120] - Qdrant search failed for query 'Describe the competitive landscape and market position for NVIDIA CORP.'. Error: timed out
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection.py", line 103, in handle_request
    return self._connection.handle_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 136, in handle_request
    raise exc
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 106, in handle_request
    ) = self._receive_response_headers(**kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 177, in _receive_response_headers
    event = self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 217, in _receive_event
    data = self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_backends\sync.py", line 126, in read
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 116, in send_inner
    response = self._client.send(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 100, in fetch_relevant_chunks
    hits = self.qdrant_client.search(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\qdrant_client.py", line 373, in search
    return self._client.search(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\qdrant_remote.py", line 520, in search
    search_result = self.http.search_api.search_points(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api\search_api.py", line 936, in search_points
    return self._build_for_search_points(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api\search_api.py", line 487, in _build_for_search_points
    return self.api_client.request(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 89, in request
    return self.send(request, type_)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 106, in send
    response = self.middleware(request, self.send_inner)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 215, in __call__
    return call_next(request)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 118, in send_inner
    raise ResponseHandlingException(e)
qdrant_client.http.exceptions.ResponseHandlingException: timed out
2025-08-11 17:28:34,520 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:120] - Qdrant search failed for query 'Who are the main competitors, rivals, and peers of NVIDIA CORP?'. Error: timed out
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\connection.py", line 103, in handle_request
    return self._connection.handle_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 136, in handle_request
    raise exc
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 106, in handle_request
    ) = self._receive_response_headers(**kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 177, in _receive_response_headers
    event = self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_sync\http11.py", line 217, in _receive_event
    data = self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_backends\sync.py", line 126, in read
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 116, in send_inner
    response = self._client.send(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 100, in fetch_relevant_chunks
    hits = self.qdrant_client.search(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\qdrant_client.py", line 373, in search
    return self._client.search(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\qdrant_remote.py", line 520, in search
    search_result = self.http.search_api.search_points(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api\search_api.py", line 936, in search_points
    return self._build_for_search_points(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api\search_api.py", line 487, in _build_for_search_points
    return self.api_client.request(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 89, in request
    return self.send(request, type_)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 106, in send
    response = self.middleware(request, self.send_inner)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 215, in __call__
    return call_next(request)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\qdrant_client\http\api_client.py", line 118, in send_inner
    raise ResponseHandlingException(e)
qdrant_client.http.exceptions.ResponseHandlingException: timed out
