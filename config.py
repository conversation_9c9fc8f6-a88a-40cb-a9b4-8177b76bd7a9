import os
from pathlib import Path

# --- API and Service Credentials ---
QDRANT_URL = "http://52.77.112.246:6333"
QDRANT_COLLECTION_NAME = "SEC_chunks_indxx"

OPENAI_API_KEY = "********************************************************************************************************************************************************************" 



# --- File and Directory Paths ---

PROJECT_ROOT = Path(__file__).resolve().parent

# Input data directory
DATA_DIR = PROJECT_ROOT / "data"

# Logs directory
LOGS_DIR = PROJECT_ROOT / "logs" 

# Path to the JSON file containing our semantic queries for Qdrant.
SEARCH_QUERIES_PATH = DATA_DIR / "search_queries.json" 

# Path to the JSON file that stores the state of our normalization maps.
CANONICAL_MAPS_PATH = DATA_DIR / "canonical_maps.json" 

# Output directory for processed data.
OUTPUT_DIR = DATA_DIR / "output" 

# Path to the final output file where clean relationships will be stored.
CLEAN_RELATIONSHIPS_PATH = OUTPUT_DIR / "clean_relationships.jsonl" 


# --- Operational Parameters ---

# The list of companies we want to process in a pipeline run.
COMPANIES_TO_PROCESS = [
    "NVIDIA CORP",
    "INTEL CORP",
    "Broadcom Inc.",
    "QUALCOMM INC/DE",
    "TEXAS INSTRUMENTS INC"
]

FUZZY_MATCH_THRESHOLD = 80
QDRANT_SEARCH_LIMIT = 10

# --- Neo4j Database Configuration ---
NEO4J_URI = "neo4j+s://aab151a2.databases.neo4j.io" 
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "3SHaGDYhKDe7VV3cFw9QaWx-5u8gOVyCva-_Hq0bbbw"

# --- Normalization Service Configuration ---

# The predefined set of official entity labels our system will use.
CANONICAL_LABELS = {
"Company",
"BusinessSegment",
"Product",
"Technology",
"GeographicRegion",
"StockExchange",
"GovernmentAgency",
"LawOrRegulation",
"IndustrySector",
"Person"
}

RELATIONSHIP_LABELS = {
"HAS_SUBSIDIARY",
"ACQUIRED",
"IS_HEADQUARTERED_IN",
"OPERATES_IN",
"SUPPLIES_TO",
"SOURCES_FROM",
"PARTNERS_WITH",
"TERMINATED_AGREEMENT_WITH",
"HAS_OFFICER",
"AUDITED_BY",
"DEVELOPS",
"USES",
"COMPETES_WITH",
"BELONGS_TO",
"HAS_SEGMENT",
"IS_TRADED_ON",
"IS_REGULATED_BY",
"IS_SUBJECT_TO"
}

# --- Directory Creation ---
def ensure_directories():
    """Creates the data and output directories if they don't already exist."""
    DATA_DIR.mkdir(exist_ok=True)
    OUTPUT_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)

if __name__ == "__main__":
    print("Ensuring all necessary directories exist...")
    ensure_directories()
    print("Directories are ready.")