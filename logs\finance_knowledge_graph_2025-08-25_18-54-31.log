2025-08-25 18:54:31,673 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-25_18-54-31.log
2025-08-25 18:54:35,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:181] - ========================================================
2025-08-25 18:54:35,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:182] - = Starting Neo4j Ingestion Pipeline                    =
2025-08-25 18:54:35,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:183] - ========================================================
2025-08-25 18:54:35,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:191] - Selected latest graph file for ingestion: cleaned_llm_output_2025-08-24_16-57-42.json
2025-08-25 18:54:35,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:23] - Connecting to Neo4j database at neo4j+s://aab151a2.databases.neo4j.io...
2025-08-25 18:54:37,190 - Finance_Knowledge_Graph - INFO - [ingestion.py:27] - Neo4j connection successful.
2025-08-25 18:54:37,190 - Finance_Knowledge_Graph - INFO - [ingestion.py:53] - Setting up database constraints...
2025-08-25 18:54:37,272 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'IndustrySector' is set.
2025-08-25 18:54:37,332 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'BusinessSegment' is set.
2025-08-25 18:54:37,392 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Company' is set.
2025-08-25 18:54:37,445 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'LawOrRegulation' is set.
2025-08-25 18:54:37,512 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Product' is set.
2025-08-25 18:54:37,572 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'StockExchange' is set.
2025-08-25 18:54:37,662 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Person' is set.
2025-08-25 18:54:37,724 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GeographicRegion' is set.
2025-08-25 18:54:37,783 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Technology' is set.
2025-08-25 18:54:37,837 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GovernmentAgency' is set.
2025-08-25 18:54:37,837 - Finance_Knowledge_Graph - INFO - [ingestion.py:162] - --- Starting Neo4j Ingestion Process ---
2025-08-25 18:54:37,840 - Finance_Knowledge_Graph - INFO - [ingestion.py:74] - Ingesting 9 nodes in batches...
2025-08-25 18:54:38,122 - Finance_Knowledge_Graph - INFO - [ingestion.py:94] - Ingested batch of 9 nodes.
2025-08-25 18:54:38,122 - Finance_Knowledge_Graph - INFO - [ingestion.py:96] - Node ingestion complete.
2025-08-25 18:54:38,122 - Finance_Knowledge_Graph - INFO - [ingestion.py:111] - Ingesting 10 relationships in batches...
2025-08-25 18:54:38,250 - Finance_Knowledge_Graph - ERROR - [ingestion.py:204] - The ingestion pipeline failed with a critical error.
neo4j.exceptions.GqlError: {gql_status: 42I13} {gql_status_description: error: syntax error or access rule violation - invalid number of procedure or function arguments. The procedure or function call does not provide the required number of arguments; expected 1 but got 2. The procedure or function apoc.nodes.get() has the signature: apoc.nodes.get(nodes :: ANY) :: node :: NODE.} {message: 42I13: The procedure or function call does not provide the required number of arguments; expected 1 but got 2. The procedure or function apoc.nodes.get() has the signature: apoc.nodes.get(nodes :: ANY) :: node :: NODE.} {diagnostic_record: {'_classification': 'CLIENT_ERROR', '_position': {'line': 6, 'column': 9, 'offset': 198}, 'OPERATION': '', 'OPERATION_CODE': '0', 'CURRENT_SCHEMA': '/'}} {raw_classification: CLIENT_ERROR}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\ingestion.py", line 201, in main
    ingestor.run(latest_graph_file)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\ingestion.py", line 175, in run
    self.ingest_relationships(relationships, nodes)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\ingestion.py", line 153, in ingest_relationships
    session.run(query, relationships=batch)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\work\session.py", line 330, in run
    self._auto_result._run(
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\io\_common.py", line 193, in inner
    func(*args, **kwargs)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 863, in fetch_message
    res = self._process_message(tag, fields)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 1208, in _process_message
    response.on_failure(summary_metadata or {})
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\neo4j\_sync\io\_common.py", line 263, in on_failure
    raise self._hydrate_error(metadata)
neo4j.exceptions.CypherSyntaxError: {code: Neo.ClientError.Statement.SyntaxError} {message: Procedure call provides too many arguments: got 2 expected no more than 1.

Procedure apoc.nodes.get has signature: apoc.nodes.get(nodes :: ANY) :: node :: NODE
meaning that it expects at least 1 argument of type ANY
Description: Returns all `NODE` values with the given ids. (line 6, column 9 (offset: 198))
"        CALL apoc.nodes.get([rel_data.source_label], {id: rel_data.source_id}) YIELD node AS source"
         ^}
2025-08-25 18:54:38,273 - Finance_Knowledge_Graph - INFO - [ingestion.py:38] - Closing Neo4j connection.
2025-08-25 18:54:38,276 - Finance_Knowledge_Graph - INFO - [ingestion.py:209] - Ingestion pipeline completed.
