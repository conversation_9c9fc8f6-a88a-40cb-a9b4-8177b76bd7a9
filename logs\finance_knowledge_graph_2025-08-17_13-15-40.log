2025-08-17 13:15:40,676 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-15-40.log
2025-08-17 13:15:50,873 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:15:50,873 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:15:50,873 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:15:50,876 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:15:50,877 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:15:53,399 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:15:53,403 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:15:53,419 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:16:19,373 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:16:19,373 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:16:19,373 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:16:20,090 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:16:20,092 - Finance_Knowledge_Graph - ERROR - [main.py:24] - A fatal error occurred in the main pipeline execution.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\main.py", line 21, in main
    pipeline.run(companies=["INTEL CORP"])
    ^^^^^^^^^^^^
AttributeError: 'KnowledgeGraphPipeline' object has no attribute 'run'
2025-08-17 13:16:20,093 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-17 13:16:20,093 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-17 13:16:20,093 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
