2025-08-14 13:48:41,069 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_13-48-41.log
2025-08-14 13:48:41,069 - Finance_Knowledge_Graph - INFO - [entity_processor.py:95] - --- Running EntityProcessor Test with NEW LOGIC ---
2025-08-14 13:48:41,069 - Finance_Knowledge_Graph - INFO - [entity_processor.py:22] - Initializing EntityProcessor...
2025-08-14 13:48:41,137 - Finance_Knowledge_Graph - INFO - [entity_processor.py:24] - EntityProcessor initialized with 86557 terms from lexicon.
2025-08-14 13:48:41,137 - Finance_Knowledge_Graph - INFO - [entity_processor.py:78] - Snapped 'our entire lineup of next-generation graphics processing units' to longest exact match: 'processing'.
2025-08-14 13:48:41,137 - Finance_Knowledge_Graph - INFO - [entity_processor.py:78] - Snapped 'the company's primary data center operations' to longest exact match: 'data center'.
2025-08-14 13:48:41,137 - Finance_Knowledge_Graph - INFO - [entity_processor.py:78] - Snapped 'machine learning algorithms' to longest exact match: 'machine learning'.
2025-08-14 13:48:41,145 - Finance_Knowledge_Graph - INFO - [entity_processor.py:84] - No exact match found in lexicon for 'NVIDIA'. Returning original name.
2025-08-14 13:48:41,146 - Finance_Knowledge_Graph - INFO - [entity_processor.py:78] - Snapped 'a new credit facility' to longest exact match: 'facility'.
