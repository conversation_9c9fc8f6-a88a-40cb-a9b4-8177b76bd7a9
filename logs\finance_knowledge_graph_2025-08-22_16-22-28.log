2025-08-22 16:22:28,977 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-22_16-22-28.log
2025-08-22 16:22:44,634 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-22 16:22:44,634 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-22 16:22:44,634 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-22 16:22:44,634 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-22 16:22:44,644 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-22 16:22:46,594 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-22 16:22:46,594 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-22 16:22:46,594 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-22 16:23:13,557 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-22 16:23:13,557 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-22 16:23:13,557 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-22 16:23:14,376 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-22 16:23:14,379 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-22 16:23:14,379 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: NVIDIA CORP ---
2025-08-22 16:23:14,379 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-22 16:24:01,067 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-22 16:24:01,067 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 1 of 3 for NVIDIA CORP (ID: a0626742-c13b-493b-93d4-0bcd36c69416)
2025-08-22 16:24:01,082 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6464
2025-08-22 16:25:14,269 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 29 entities and 7 relationships.
2025-08-22 16:25:14,269 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 2 of 3 for NVIDIA CORP (ID: bccfc3e1-99f1-436f-9356-1d5a3a7b93d8)
2025-08-22 16:25:14,269 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1625
2025-08-22 16:25:41,420 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 4 entities and 3 relationships.
2025-08-22 16:25:41,420 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 3 of 3 for NVIDIA CORP (ID: 088338c0-46ac-4c26-8343-af6a389e37f1)
2025-08-22 16:25:41,420 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 2173
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 2 entities and 0 relationships.
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [pipeline.py:107] - --- Pipeline Run Finished ---
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [pipeline.py:108] - Total entities extracted: 12
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [pipeline.py:109] - Total relationships extracted: 10
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [main.py:30] - =====================================================
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [main.py:31] -       PIPELINE EXECUTION FINISHED                 
2025-08-22 16:26:01,818 - Finance_Knowledge_Graph - INFO - [main.py:32] - =====================================================
