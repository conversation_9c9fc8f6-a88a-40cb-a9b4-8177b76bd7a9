2025-08-14 14:36:33,006 - Finance_Knowledge_Graph - INFO - [logger.py:61] - <PERSON>gger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_14-36-33.log
2025-08-14 14:36:33,006 - Finance_Knowledge_Graph - INFO - [entity_processor.py:95] - --- Running EntityProcessor Test with NEW LOGIC ---
2025-08-14 14:36:33,006 - Finance_Knowledge_Graph - INFO - [entity_processor.py:22] - Initializing EntityProcessor...
2025-08-14 14:36:33,078 - Finance_Knowledge_Graph - INFO - [entity_processor.py:24] - EntityProcessor initialized with 86557 terms from lexicon.
2025-08-14 14:36:33,084 - Finance_Knowledge_Graph - INFO - [entity_processor.py:78] - Snapped 'CPU design and development' to longest exact match: 'development'.
