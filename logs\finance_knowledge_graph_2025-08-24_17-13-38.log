2025-08-24 17:13:38,871 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-24_17-13-38.log
2025-08-24 17:13:43,773 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:203] - ========================================================
2025-08-24 17:13:43,773 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:204] - = Starting Unified Entity Resolution & Cleaning Pipeline =
2025-08-24 17:13:43,789 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:205] - ========================================================
2025-08-24 17:13:43,789 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:214] - Selected latest raw file for processing: record.jsonl
2025-08-24 17:13:43,789 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:27] - Initializing Entity Resolution Pipeline...
2025-08-24 17:13:43,789 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:45] - Collecting all unique entities and all relationships from record.jsonl...
2025-08-24 17:13:43,794 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:59] - Collected 43 unique entities and loaded 46 relationships.
2025-08-24 17:13:43,794 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:34] - Entity Resolution Pipeline initialized.
2025-08-24 17:13:43,794 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:70] - --- Starting Pass 1: The Learning Pass ---
2025-08-24 17:13:43,794 - Finance_Knowledge_Graph - INFO - [other_norm.py:434] - 
--- Starting Normalization Pass for 43 total entities ---
2025-08-24 17:13:43,796 - Finance_Knowledge_Graph - INFO - [other_norm.py:439] - Separated entities: 19 Companies, 24 Others.
2025-08-24 17:13:43,796 - Finance_Knowledge_Graph - INFO - [other_norm.py:443] - --- Processing Company Entities ---
2025-08-24 17:13:43,797 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:18] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-24 17:13:43,797 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:42] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-24 17:13:43,838 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:57] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-24 17:13:43,838 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - CompanyNormalizer initialized with 4 canonical companies.
2025-08-24 17:13:43,838 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'NVIDIA Corporation' -> 'NVDA'
2025-08-24 17:13:43,855 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Advanced Micro Devices, Inc.' found in local map: 'AMD' (Score: 98.00)
2025-08-24 17:13:43,855 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Advanced Micro Devices, Inc.' -> 'AMD'
2025-08-24 17:13:44,056 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Huawei Technologies Co., Ltd.' found in SEC master list: 'Meihua International Medical Technologies Co., Ltd.' (Score: 82.70)
2025-08-24 17:13:44,056 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'MHUA'
2025-08-24 17:13:44,056 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Huawei Technologies Co., Ltd.' -> 'MHUA'
2025-08-24 17:13:44,240 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Deutsche Telekom AG' found in SEC master list: 'DEUTSCHE TELEKOM AG' (Score: 100.00)
2025-08-24 17:13:44,240 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'DTEGY'
2025-08-24 17:13:44,240 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Deutsche Telekom AG' -> 'DTEGY'
2025-08-24 17:13:44,433 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Brookfield Asset Management Inc.' found in SEC master list: 'Brookfield Asset Management Ltd.' (Score: 92.60)
2025-08-24 17:13:44,433 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'BAM'
2025-08-24 17:13:44,433 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Brookfield Asset Management Inc.' -> 'BAM'
2025-08-24 17:13:44,433 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:146] - Fuzzy match for 'Microsoft Corporation' found in local map: 'INTC' (Score: 80.40)
2025-08-24 17:13:44,433 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Microsoft Corporation' -> 'INTC'
2025-08-24 17:13:44,625 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Meta Platforms, Inc.' found in SEC master list: 'Meta Platforms, Inc.' (Score: 100.00)
2025-08-24 17:13:44,625 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'META'
2025-08-24 17:13:44,625 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Meta Platforms, Inc.' -> 'META'
2025-08-24 17:13:44,807 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'OpenAI, L.L.C.'. No match in local map or SEC list.
2025-08-24 17:13:44,807 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'OpenAI, L.L.C.' -> 'UNRESOLVED_COMPANY_OPENAI,_LLC'
2025-08-24 17:13:44,967 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'OpenAI'. No match in local map or SEC list.
2025-08-24 17:13:44,967 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'OpenAI' -> 'UNRESOLVED_COMPANY_OPENAI'
2025-08-24 17:13:45,174 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Nscale Global Holdings' found in SEC master list: 'GCL Global Holdings Ltd' (Score: 82.90)
2025-08-24 17:13:45,174 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'GCL'
2025-08-24 17:13:45,174 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Nscale Global Holdings' -> 'GCL'
2025-08-24 17:13:45,324 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Aker ASA'. No match in local map or SEC list.
2025-08-24 17:13:45,324 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Aker ASA' -> 'UNRESOLVED_COMPANY_AKER_ASA'
2025-08-24 17:13:45,539 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Taiwan Semiconductor Manufacturing Company' found in SEC master list: 'TAIWAN SEMICONDUCTOR MANUFACTURING CO LTD' (Score: 91.80)
2025-08-24 17:13:45,539 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'TSM'
2025-08-24 17:13:45,539 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Taiwan Semiconductor Manufacturing Company' -> 'TSM'
2025-08-24 17:13:45,753 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Chinese distributors'. No match in local map or SEC list.
2025-08-24 17:13:45,753 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Chinese distributors' -> 'UNRESOLVED_COMPANY_CHINESE_DISTRIBUTORS'
2025-08-24 17:13:46,033 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'data center suppliers'. No match in local map or SEC list.
2025-08-24 17:13:46,033 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'data center suppliers' -> 'UNRESOLVED_COMPANY_DATA_CENTER_SUPPLIERS'
2025-08-24 17:13:46,206 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Perplexity AI'. No match in local map or SEC list.
2025-08-24 17:13:46,206 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Perplexity AI' -> 'UNRESOLVED_COMPANY_PERPLEXITY_AI'
2025-08-24 17:13:46,373 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Google LLC'. No match in local map or SEC list.
2025-08-24 17:13:46,373 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Google LLC' -> 'UNRESOLVED_COMPANY_GOOGLE_LLC'
2025-08-24 17:13:46,533 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Apple Inc.' found in SEC master list: 'Apple Inc.' (Score: 100.00)
2025-08-24 17:13:46,533 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'AAPL'
2025-08-24 17:13:46,533 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Apple Inc.' -> 'AAPL'
2025-08-24 17:13:46,738 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:175] - Fuzzy title match for 'Alibaba Group Holding Limited' found in SEC master list: 'Alibaba Group Holding Ltd' (Score: 93.90)
2025-08-24 17:13:46,738 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:216] - Creating new entry in company map for 'BABA'
2025-08-24 17:13:46,738 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Alibaba Group Holding Limited' -> 'BABA'
2025-08-24 17:13:46,920 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Tencent Holdings Limited'. No match in local map or SEC list.
2025-08-24 17:13:46,920 - Finance_Knowledge_Graph - INFO - [other_norm.py:455] - Resolved Company 'Tencent Holdings Limited' -> 'UNRESOLVED_COMPANY_TENCENT_HOLDINGS_LIMITED'
2025-08-24 17:13:46,936 - Finance_Knowledge_Graph - INFO - [other_norm.py:461] - Company resolution review map saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\review_company_nodes.json
2025-08-24 17:13:46,936 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:84] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-24 17:13:46,936 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:96] - Successfully saved 12 companies to the map.
2025-08-24 17:13:46,936 - Finance_Knowledge_Graph - INFO - [other_norm.py:467] - --- Finished Processing Company Entities ---
2025-08-24 17:13:46,936 - Finance_Knowledge_Graph - INFO - [other_norm.py:471] - --- Processing Other Entity Types ---
2025-08-24 17:13:47,960 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:81] - The Learning Pass failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 474, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 359, in _Update_alias
    llm_result = call_openai_api(
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 84, in call_openai_api
    response = client.chat.completions.parse(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Completions' object has no attribute 'parse'
2025-08-24 17:13:48,022 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:248] - The entity resolution pipeline failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 232, in main
    pipeline.run_learning_pass()
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 474, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 359, in _Update_alias
    llm_result = call_openai_api(
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 84, in call_openai_api
    response = client.chat.completions.parse(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Completions' object has no attribute 'parse'
