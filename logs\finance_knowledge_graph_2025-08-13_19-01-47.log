2025-08-13 19:01:47,958 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-13_19-01-47.log
2025-08-13 19:01:58,017 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-13 19:01:58,018 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-13 19:01:58,018 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-13 19:01:58,023 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-13 19:01:58,023 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-13 19:01:59,812 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-13 19:01:59,815 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-13 19:01:59,820 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-13 19:02:16,236 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-13 19:02:16,236 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-13 19:02:16,237 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-13 19:02:17,114 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-13 19:02:17,115 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 19:02:17,116 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-13 19:02:17,116 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-13 19:02:17,117 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-13 19:02:17,117 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-13 19:02:17,118 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-13 19:03:00,576 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-13 19:03:00,577 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-13 19:03:58,745 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:91] - Successfully extracted 7 relationships from the provided context.
2025-08-13 19:03:58,746 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 7 raw relationships for INTEL CORP.
2025-08-13 19:03:58,746 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,748 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'DCAI' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,749 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'NEX' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,751 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Total Intel Products revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,751 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,753 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Total Intel Products revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,755 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,756 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Total Intel Products revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,757 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,759 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'CPU design and development' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 19:03:58,760 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-13 19:03:58,765 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 7
2025-08-13 19:03:58,766 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-13 19:03:58,770 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 19:03:58,773 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-13 19:03:58,773 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-13 19:03:58,774 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-13 19:03:58,775 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
