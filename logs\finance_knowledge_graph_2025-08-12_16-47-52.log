2025-08-12 16:47:52,978 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-47-52.log
2025-08-12 16:48:02,642 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-12 16:48:02,642 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-12 16:48:02,642 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-12 16:48:02,651 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-12 16:48:02,651 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 16:48:04,343 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-12 16:48:04,349 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 16:48:04,349 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 16:48:19,004 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 16:48:19,004 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 16:48:19,004 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 16:48:19,718 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 16:48:19,718 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:48:19,721 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 16:48:19,721 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-12 16:48:19,721 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-12 16:48:19,721 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: NVIDIA CORP ---
2025-08-12 16:48:19,721 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 16:49:02,087 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-12 16:49:02,087 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 5 chunks to OpenAI for extraction. Total context length: 22005
2025-08-12 16:49:22,392 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:92] - Successfully extracted 13 relationships from the provided context.
2025-08-12 16:49:22,392 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 13 raw relationships for NVIDIA CORP.
2025-08-12 16:49:22,400 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Baidu, Inc.' of type 'Company' with ID 'company_baidu,_inc'.
2025-08-12 16:49:22,400 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Ambarella, Inc.' of type 'Company' with ID 'company_ambarella,_inc'.
2025-08-12 16:49:22,400 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Tesla, Inc.' of type 'Company' with ID 'company_tesla,_inc'.
2025-08-12 16:49:22,405 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'automotive OEMs' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:49:22,407 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'tier-1 suppliers' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:49:22,411 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'start-ups' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:49:22,413 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-12 16:49:22,415 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 13
2025-08-12 16:49:22,418 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-12 16:49:22,418 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:49:22,423 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-12 16:49:22,423 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-12 16:49:22,425 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-12 16:49:22,426 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
