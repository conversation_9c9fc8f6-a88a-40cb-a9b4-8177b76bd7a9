2025-08-17 21:25:17,787 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_21-25-17.log
2025-08-17 21:25:29,847 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:177] - --- Starting UMAP -> HDBSCAN Clustering Analysis ---
2025-08-17 21:25:29,848 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:23] - Loading relationship phrases from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships.jsonl
2025-08-17 21:25:29,850 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:35] - Found 8 unique relationship phrases.
2025-08-17 21:25:29,851 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 21:25:31,975 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 21:25:31,981 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 21:25:31,982 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 21:25:47,515 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 21:25:47,515 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 21:25:47,516 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:50] - Vectorizing 8 phrases using the BGE model...
2025-08-17 21:25:48,235 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:52] - Vectorization complete. Embedding matrix shape: (8, 768)
2025-08-17 21:25:48,236 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:71] - Starting dimensionality reduction with UMAP from 768 to 10 dimensions...
