{"cells": [{"cell_type": "code", "execution_count": null, "id": "7af2a703", "metadata": {}, "outputs": [], "source": ["NODE_DEFINITIONS = \"\"\"\n", "- Company: A legal business entity (e.g., \"NVIDIA Corporation\", \"Intel\").\n", "- BusinessSegment: An internal, reportable division of a company (e.g., \"Data Center\", \"Client Computing Group\").\n", "- Product: A specific hardware or software offering from a company (e.g., \"GeForce RTX 4090\").\n", "- Technology: A specific technology, standard, or platform (e.g., \"CUDA\", \"5G\").\n", "- GeographicRegion: A country, state, or city (e.g., \"Taiwan\", \"United States\", \"California\").\n", "- StockExchange: A specific stock market where companies are traded (e.g., \"NASDAQ\").\n", "- GovernmentAgency: A formal government body (e.g., \"U.S. Securities and Exchange Commission\").\n", "- LawOrRegulation: A specific law, act, or regulation (e.g., \"CHIPS and Science Act\").\n", "- IndustrySector: A broad industry category (e.g., \"Semiconductors\", \"Automotive\").\n", "- Person: A named individual, typically an executive or board member.\n", "\"\"\"\n", "\n", "RELATIONSHIP_DEFINITIONS = \"\"\"\n", "- HAS_SUBSIDIARY: Parent company owns or controls a smaller company.\n", "- ACQUIRED: One company has bought another company.\n", "- IS_HEADQUARTERED_IN: A company's main office is located in a specific region.\n", "- OPERATES_IN: A company has significant business operations (manufacturing, sales) in a region.\n", "- SUPPLIES_TO: One entity provides goods or services to another.\n", "- SOURCES_FROM: One entity receives goods or services from another.\n", "- PARTNERS_WITH: Two or more entities are formally collaborating on a project or technology.\n", "- TERMINATED_AGREEMENT_WITH: A previously existing partnership or agreement has ended.\n", "- HAS_OFFICER: A company has a person in a key executive role.\n", "- AUDITED_BY: An accounting firm is the official auditor for a company.\n", "- DEVELOPS: A company is the primary creator of a product or technology.\n", "- USES: A company integrates or utilizes a specific technology in its products or operations.\n", "- COMPETES_WITH: Two entities are direct rivals in the same market.\n", "- BELONGS_TO: An entity is a member of a broader category (e.g., Company -> IndustrySector).\n", "- HAS_SEGMENT: A company is internally organized into a specific business division.\n", "- IS_TRADED_ON: A company's stock is listed on a specific stock exchange.\n", "- IS_REGULATED_BY: A company is subject to the oversight of a government agency.\n", "- IS_SUBJECT_TO: A company's operations are directly affected by a specific law or regulation.\n", "\"\"\"\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "181910d8", "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = f\"\"\"\n", "You are an expert financial analyst building a structured knowledge graph. Your task is to act as a precision-focused information extraction engine. You must deconstruct sentences into clean, distinct entities and map their interactions to a predefined set of relationship types.\n", "\n", "Guiding Principles for a Scalable Graph:\n", "\n", "1.  Entity Disentanglement: You MUST separate compound entities. A possessive or descriptive phrase often hides two entities and a relationship.\n", "    -   Example Text: \"NVIDIA's Data Center segment saw growth...\"\n", "    -  INCORRECT Extraction: One entity named \"NVIDIA's Data Center\"\n", "    -   CORRECT Extraction: Two separate entities: `{{ \"name\": \"NVIDIA\", \"type\": \"Company\" }}` and `{{ \"name\": \"Data Center\", \"type\": \"BusinessSegment\" }}`, linked by a `HAS_SEGMENT` relationship.\n", "\n", "2.  Canonical Naming: You MUST use an entity's full, official name if available, not an abbreviation.\n", "    -   Example Text: \"The Client Computing Group (CCG) is our largest segment.\"\n", "    -   CORRECT `name`: \"Client Computing Group\"\n", "    -   Incorrect `name`: \"CCG\"\n", "\n", "3.  Strictly Factual & Explicit: You MUST only extract relationships that are stated as facts in the past or present tense. Do not extract hypothetical, forward-looking, or negated relationships (e.g., \"may acquire\", \"plans to partner\", \"did not supply\").\n", "\n", "4.  Pronoun Resolution: You MUST resolve pronouns (it, they) to the specific entity they refer to.\n", "\n", "Core Task: Entity and Relationship Classification:\n", "\n", "Your task is to identify entities and relationships and classify them according to the following strict definitions.\n", "\n", "1. Allowed Entity Types and Their Definitions:\n", "{NODE_DEFINITIONS}\n", "You must choose the appropriate entity type from this list.\n", "\n", "2. Allowed Relationship Types and Their Definitions:\n", "{RELATIONSHIP_DEFINITIONS}\n", "You MUST choose the most appropriate relationship from this list.\n", "\n", "A\n", "3. Strength Score:\n", "   - Assign a numeric strength_score between 0.0 and 1.0 representing the intensity or importance of the relationship as indicated in the text.\n", "   - Base this on language cues (e.g., “exclusive, long-term agreement” → high score; “minor collaboration” → lower score).\n", "\n", "4. Evidence\n", "   - evidence must be a direct quotation (verbatim) from the provided text that supports the relationship.\n", "   - If multiple sentences mention the relationship, choose the one most directly describing it.\n", "\n", "5. <PERSON> Met<PERSON><PERSON>\n", "   - Fill in the `source` object with the provided metadata:\n", "       - report_type — filing type (e.g., \"10-K\", \"10-Q\")\n", "       - period_of_report — YYYY-MM-DD format\n", "       - section — section of the filing\n", "       - source_url — exact provided URL\n", "\n", "Final Output Format:\n", "- You will be given a text chunk and must return a JSON object with two keys: `entities` and `relationships`.\n", "- The `entities` list must contain every unique, disentangled entity identified.\n", "- The `relationships` list must describe the connections between those entities.\n", "- The entire output MUST be a single, valid JSON object that strictly adheres to the provided schema.\n", "- If no relationships matching the predefined types can be factually extracted, you MUST return an empty list for `extracted_relationships`.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "id": "0af66ef9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are an expert financial analyst building a structured knowledge graph. Your task is to act as a precision-focused information extraction engine. You must deconstruct sentences into clean, distinct entities and map their interactions to a predefined set of relationship types.\n", "\n", "--- Guiding Principles for a Scalable Graph ---\n", "\n", "1.  **Entity Disentanglement (Most Important Rule):** You MUST separate compound entities. A possessive or descriptive phrase often hides two entities and a relationship.\n", "    -   **Example Text:** \"NVIDIA's Data Center segment saw growth...\"\n", "    -   **CORRECT Extraction:** Two separate entities: `{ \"name\": \"NVIDIA\", \"type\": \"Company\" }` and `{ \"name\": \"Data Center\", \"type\": \"BusinessSegment\" }`, linked by a `HAS_SEGMENT` relationship.\n", "\n", "2.  **Canonical Naming:** You MUST use an entity's full, official name if available, not an abbreviation.\n", "    -   **Example Text:** \"The Client Computing Group (CCG) is our largest segment.\"\n", "    -   **CORRECT `name`:** \"Client Computing Group\"\n", "\n", "3.  **Strictly Factual & Explicit:** You MUST only extract relationships that are stated as facts in the past or present tense. Do not extract hypothetical, forward-looking, or negated relationships.\n", "\n", "4.  **Pronoun Resolution:** You MUST resolve pronouns (it, they) to the specific entity they refer to.\n", "\n", "--- Core Task: Entity and Relationship Classification ---\n", "\n", "Your task is to identify entities and relationships and classify them according to the following strict definitions.\n", "\n", "**1. Allowed Entity Types and Their Definitions:**\n", "\n", "- **Company**: A legal business entity (e.g., \"NVIDIA Corporation\", \"Intel\").\n", "- **BusinessSegment**: An internal, reportable division of a company (e.g., \"Data Center\", \"Client Computing Group\").\n", "- **Product**: A specific hardware or software offering from a company (e.g., \"GeForce RTX 4090\").\n", "- **Technology**: A specific technology, standard, or platform (e.g., \"CUDA\", \"5G\").\n", "- **GeographicRegion**: A country, state, or city (e.g., \"Taiwan\", \"United States\", \"California\").\n", "- **StockExchange**: A specific stock market where companies are traded (e.g., \"NASDAQ\").\n", "- **GovernmentAgency**: A formal government body (e.g., \"U.S. Securities and Exchange Commission\").\n", "- **LawOrRegulation**: A specific law, act, or regulation (e.g., \"CHIPS and Science Act\").\n", "- **IndustrySector**: A broad industry category (e.g., \"Semiconductors\", \"Automotive\").\n", "- **Person**: A named individual, typically an executive or board member.\n", "\n", "If an entity does not fit any category, you MUST use the type \"Other\".\n", "\n", "**2. Allowed Relationship Types and Their Definitions:**\n", "\n", "- **HAS_SUBSIDIARY**: Parent company owns or controls a smaller company.\n", "- **ACQUIRED**: One company has bought another company.\n", "- **IS_HEADQUARTERED_IN**: A company's main office is located in a specific region.\n", "- **OPERATES_IN**: A company has significant business operations (manufacturing, sales) in a region.\n", "- **SUPPLIES_TO**: One entity provides goods or services to another.\n", "- **SOURCES_FROM**: One entity receives goods or services from another.\n", "- **PARTNERS_WITH**: Two or more entities are formally collaborating on a project or technology.\n", "- **TERMINATED_AGREEMENT_WITH**: A previously existing partnership or agreement has ended.\n", "- **HAS_OFFICER**: A company has a person in a key executive role.\n", "- **AUDITED_BY**: An accounting firm is the official auditor for a company.\n", "- **DEVELOPS**: A company is the primary creator of a product or technology.\n", "- **USES**: A company integrates or utilizes a specific technology in its products or operations.\n", "- **COMPETES_WITH**: Two entities are direct rivals in the same market.\n", "- **BELONGS_TO**: An entity is a member of a broader category (e.g., Company -> IndustrySector).\n", "- **HAS_SEGMENT**: A company is internally organized into a specific business division.\n", "- **IS_TRADED_ON**: A company's stock is listed on a specific stock exchange.\n", "- **IS_REGULATED_BY**: A company is subject to the oversight of a government agency.\n", "- **IS_SUBJECT_TO**: A company's operations are directly affected by a specific law or regulation.\n", "\n", "You MUST choose the most appropriate relationship from this list.\n", "\n", "--- Final Output Format ---\n", "- You will be given a text chunk and must return a JSON object with two keys: `entities` and `relationships`.\n", "- The `entities` list must contain every unique, disentangled entity identified.\n", "- The `relationships` list must describe the connections between those entities.\n", "- The entire output MUST be a single, valid JSON object that strictly adheres to the provided schema.\n", "- If no relationships matching the predefined types can be factually extracted, you MUST return an empty list for `extracted_relationships`.\n", "\n"]}], "source": ["print(SYSTEM_PROMPT)"]}, {"cell_type": "code", "execution_count": 1, "id": "f33dbb04", "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Dict, Any, List\n", "\n", "def load_sec_data(file_path: str) -> List[Dict[str, Any]]:\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "        # Convert the SEC list from { \"0\": {...}, \"1\": {...} } to a more useful\n", "        # dictionary keyed by the CIK string for potential future use.\n", "        # For now, we'll just return the values as a list of dicts.\n", "        return list(data.values())\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9659e729", "metadata": {}, "outputs": [], "source": ["data = load_sec_data('../tickers.json')"]}, {"cell_type": "code", "execution_count": 3, "id": "d7199e47", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'cik_str': 789019, 'ticker': 'MSFT', 'title': 'MICROSOFT CORP'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data[1]"]}, {"cell_type": "code", "execution_count": 4, "id": "a168873d", "metadata": {}, "outputs": [], "source": ["sec_ticker_lookup = {\n", "            entry.get(\"ticker\", \"\").upper(): entry for entry in data if entry.get(\"ticker\")\n", "        }"]}, {"cell_type": "code", "execution_count": 7, "id": "d542b5c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'NVDA': {'cik_str': 1045810, 'ticker': 'NVDA', 'title': 'NVIDIA CORP'},\n", " 'MSFT': {'cik_str': 789019, 'ticker': 'MSFT', 'title': 'MICROSOFT CORP'},\n", " 'AAPL': {'cik_str': 320193, 'ticker': 'AAPL', 'title': 'Apple Inc.'},\n", " 'AMZN': {'cik_str': 1018724, 'ticker': 'AMZN', 'title': 'AMAZON COM INC'},\n", " 'GOOGL': {'cik_str': 1652044, 'ticker': 'GOOGL', 'title': 'Alphabet Inc.'},\n", " 'META': {'cik_str': 1326801,\n", "  'ticker': 'META',\n", "  'title': 'Meta Platforms, Inc.'},\n", " 'AVGO': {'cik_str': 1730168, 'ticker': 'AVGO', 'title': 'Broadcom Inc.'},\n", " 'TSLA': {'cik_str': 1318605, 'ticker': 'TSLA', 'title': 'Tesla, Inc.'},\n", " 'BRK-B': {'cik_str': 1067983,\n", "  'ticker': 'BRK-B',\n", "  'title': 'BERKSHIRE HATHAWAY INC'},\n", " 'JPM': {'cik_str': 19617, 'ticker': 'JPM', 'title': 'JPMORGAN CHASE & CO'},\n", " 'WMT': {'cik_str': 104169, 'ticker': 'WMT', 'title': 'Walmart Inc.'},\n", " 'ORCL': {'cik_str': 1341439, 'ticker': 'ORCL', 'title': 'ORACLE CORP'},\n", " 'V': {'cik_str': 1403161, 'ticker': 'V', 'title': 'VISA INC.'},\n", " 'LLY': {'cik_str': 59478, 'ticker': 'LLY', 'title': 'ELI LILLY & Co'},\n", " 'SPY': {'cik_str': 884394,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'SPDR S&P 500 ETF TRUST'},\n", " 'MA': {'cik_str': 1141391, 'ticker': 'MA', 'title': 'Mastercard Inc'},\n", " 'NFLX': {'cik_str': 1065280, 'ticker': 'NFLX', 'title': 'NETFLIX INC'},\n", " 'XOM': {'cik_str': 34088, 'ticker': 'XOM', 'title': 'EXXON MOBIL CORP'},\n", " 'COST': {'cik_str': 909832,\n", "  'ticker': 'COST',\n", "  'title': 'COSTCO WHOLESALE CORP /NEW'},\n", " 'PLTR': {'cik_str': 1321655,\n", "  'ticker': 'PLTR',\n", "  'title': 'Palantir Technologies Inc.'},\n", " 'JNJ': {'cik_str': 200406, 'ticker': '<PERSON>N<PERSON>', 'title': 'JOHNSON & JOHNSON'},\n", " 'HD': {'cik_str': 354950, 'ticker': 'HD', 'title': 'HOME DEPOT, INC.'},\n", " 'ABBV': {'cik_str': 1551152, 'ticker': 'ABBV', 'title': 'AbbVie Inc.'},\n", " 'PG': {'cik_str': 80424, 'ticker': 'PG', 'title': 'PROCTER & GAMBLE Co'},\n", " 'BAC': {'cik_str': 70858,\n", "  'ticker': 'BAC',\n", "  'title': 'BANK OF AMERICA CORP /DE/'},\n", " 'SAP': {'cik_str': 1000184, 'ticker': 'SAP', 'title': 'SAP SE'},\n", " 'CVX': {'cik_str': 93410, 'ticker': 'CVX', 'title': 'CHEVRON CORP'},\n", " 'KO': {'cik_str': 21344, 'ticker': 'KO', 'title': 'COCA COLA CO'},\n", " 'ASML': {'cik_str': 937966, 'ticker': 'ASML', 'title': 'ASML HOLDING NV'},\n", " 'BABA': {'cik_str': 1577552,\n", "  'ticker': 'BABA',\n", "  'title': 'Alibaba Group Holding Ltd'},\n", " 'AMD': {'cik_str': 2488,\n", "  'ticker': 'AMD',\n", "  'title': 'ADVANCED MICRO DEVICES INC'},\n", " 'GE': {'cik_str': 40545, 'ticker': 'GE', 'title': 'GENERAL ELECTRIC CO'},\n", " 'TMUS': {'cik_str': 1283699, 'ticker': 'TMUS', 'title': 'T-Mobile US, Inc.'},\n", " 'CSCO': {'cik_str': 858877, 'ticker': 'CSCO', 'title': 'CISCO SYSTEMS, INC.'},\n", " 'PM': {'cik_str': 1413329,\n", "  'ticker': 'PM',\n", "  'title': 'Philip Morris International Inc.'},\n", " 'WFC': {'cik_str': 72971,\n", "  'ticker': 'WFC',\n", "  'title': 'WELLS FARGO & COMPANY/MN'},\n", " 'TM': {'cik_str': 1094517, 'ticker': 'TM', 'title': 'TOYOTA MOTOR CORP/'},\n", " 'UNH': {'cik_str': 731766,\n", "  'ticker': 'UNH',\n", "  'title': 'UNITEDHEALTH GROUP INC'},\n", " 'AZN': {'cik_str': 901832, 'ticker': 'AZN', 'title': 'ASTRAZENECA PLC'},\n", " 'MS': {'cik_str': 895421, 'ticker': 'MS', 'title': 'MORGAN STANLEY'},\n", " 'NVS': {'cik_str': 1114448, 'ticker': 'NVS', 'title': 'NOVARTIS AG'},\n", " 'QQQ': {'cik_str': 1067839,\n", "  'ticker': 'QQQ',\n", "  'title': 'INVESCO QQQ TRUST, SERIES 1'},\n", " 'HSBC': {'cik_str': 1089113, 'ticker': 'HSBC', 'title': 'HSBC HOLDINGS PLC'},\n", " 'NVO': {'cik_str': 353278, 'ticker': 'NVO', 'title': 'NOVO NORDISK A S'},\n", " 'ABT': {'cik_str': 1800, 'ticker': 'ABT', 'title': 'ABBOTT LABORATORIES'},\n", " 'GS': {'cik_str': 886982, 'ticker': 'GS', 'title': 'GOLDMAN SACHS GROUP INC'},\n", " 'LIN': {'cik_str': 1707925, 'ticker': 'LIN', 'title': 'LINDE PLC'},\n", " 'CRM': {'cik_str': 1108524, 'ticker': 'CRM', 'title': 'Salesforce, Inc.'},\n", " 'IBM': {'cik_str': 51143,\n", "  'ticker': 'IBM',\n", "  'title': 'INTERNATIONAL BUSINESS MACHINES CORP'},\n", " 'MCD': {'cik_str': 63908, 'ticker': 'MCD', 'title': 'MCDONALDS CORP'},\n", " 'BX': {'cik_str': 1393818, 'ticker': 'BX', 'title': 'Blackstone Inc.'},\n", " 'AXP': {'cik_str': 4962, 'ticker': 'AXP', 'title': 'AMERICAN EXPRESS CO'},\n", " 'SHEL': {'cik_str': 1306965, 'ticker': 'SHEL', 'title': 'Shell plc'},\n", " 'DIS': {'cik_str': 1744489, 'ticker': 'DIS', 'title': 'Walt Disney Co'},\n", " 'RTX': {'cik_str': 101829, 'ticker': 'RTX', 'title': 'RTX Corp'},\n", " 'MRK': {'cik_str': 310158, 'ticker': 'MRK', 'title': 'Merck & Co., Inc.'},\n", " 'T': {'cik_str': 732717, 'ticker': 'T', 'title': 'AT&T INC.'},\n", " 'PEP': {'cik_str': 77476, 'ticker': 'PEP', 'title': 'PEPSICO INC'},\n", " 'INTU': {'cik_str': 896878, 'ticker': 'INTU', 'title': 'INTUIT INC.'},\n", " 'CAT': {'cik_str': 18230, 'ticker': 'CAT', 'title': 'CATERPILLAR INC'},\n", " 'RY': {'cik_str': 1000275, 'ticker': 'RY', 'title': 'ROYAL BANK OF CANADA'},\n", " 'UBER': {'cik_str': 1543151,\n", "  'ticker': 'UBER',\n", "  'title': 'Uber Technologies, Inc'},\n", " 'SHOP': {'cik_str': 1594805, 'ticker': 'SHOP', 'title': 'SHOPIFY INC.'},\n", " 'HDB': {'cik_str': 1144967, 'ticker': 'HDB', 'title': 'HDFC BANK LTD'},\n", " 'VZ': {'cik_str': 732712,\n", "  'ticker': 'VZ',\n", "  'title': 'VERIZON COMMUNICATIONS INC'},\n", " 'TMO': {'cik_str': 97745,\n", "  'ticker': 'TMO',\n", "  'title': 'THERMO FISHER SCIENTIFIC INC.'},\n", " 'BLK': {'cik_str': 2012383, 'ticker': 'BLK', 'title': 'BlackRock, Inc.'},\n", " 'BKNG': {'cik_str': 1075531,\n", "  'ticker': 'BKNG',\n", "  'title': 'Booking Holdings Inc.'},\n", " 'SCHW': {'cik_str': 316709, 'ticker': 'SCHW', 'title': 'SCHWAB CHARLES CORP'},\n", " 'CYATY': {'cik_str': 2070829,\n", "  'ticker': 'CYAT<PERSON>',\n", "  'title': 'Contemporary Amperex Technology Co., Limited/ADR'},\n", " 'NOW': {'cik_str': 1373715, 'ticker': 'NOW', 'title': 'ServiceNow, Inc.'},\n", " 'BA': {'cik_str': 12927, 'ticker': 'BA', 'title': 'BOEING CO'},\n", " 'TXN': {'cik_str': 97476, 'ticker': 'TXN', 'title': 'TEXAS INSTRUMENTS INC'},\n", " 'DTEGY': {'cik_str': 946770,\n", "  'ticker': 'DTEGY',\n", "  'title': 'DEUTSCHE TELEKOM AG'},\n", " 'C': {'cik_str': 831001, 'ticker': 'C', 'title': 'CITIGROUP INC'},\n", " 'MUFG': {'cik_str': 67088,\n", "  'ticker': 'MUFG',\n", "  'title': 'MITSUBISHI UFJ FINANCIAL GROUP INC'},\n", " 'SPGI': {'cik_str': 64040, 'ticker': 'SPGI', 'title': 'S&P Global Inc.'},\n", " 'ANET': {'cik_str': 1596532,\n", "  'ticker': 'ANET',\n", "  'title': 'Arista Networks, Inc.'},\n", " 'FMX': {'cik_str': 1061736,\n", "  'ticker': 'FMX',\n", "  'title': 'MEXICAN ECONOMIC DEVELOPMENT INC'},\n", " 'ISRG': {'cik_str': 1035267,\n", "  'ticker': 'ISRG',\n", "  'title': 'INTUITIVE SURGICAL INC'},\n", " 'QCOM': {'cik_str': 804328, 'ticker': 'QCOM', 'title': 'QUALCOMM INC/DE'},\n", " 'GEV': {'cik_str': 1996810, 'ticker': 'GEV', 'title': 'GE Vernova Inc.'},\n", " 'SONY': {'cik_str': 313838, 'ticker': 'SONY', 'title': 'Sony Group Corp'},\n", " 'PDD': {'cik_str': 1737806, 'ticker': 'PDD', 'title': 'PDD Holdings Inc.'},\n", " 'AMGN': {'cik_str': 318154, 'ticker': 'AMGN', 'title': 'AMGEN INC'},\n", " 'ACN': {'cik_str': 1467373, 'ticker': 'ACN', 'title': 'Accenture plc'},\n", " 'BSX': {'cik_str': 885725,\n", "  'ticker': 'BSX',\n", "  'title': 'BOSTON SCIENTIFIC CORP'},\n", " 'AMAT': {'cik_str': 6951,\n", "  'ticker': 'AMAT',\n", "  'title': 'APPLIED MATERIALS INC /DE'},\n", " 'UL': {'cik_str': 217410, 'ticker': 'UL', 'title': 'UNILEVER PLC'},\n", " 'DHR': {'cik_str': 313616, 'ticker': 'DHR', 'title': 'DANAHER CORP /DE/'},\n", " 'TJX': {'cik_str': 109198,\n", "  'ticker': 'TJX',\n", "  'title': 'TJX COMPANIES INC /DE/'},\n", " 'ARM': {'cik_str': 1973239, 'ticker': 'ARM', 'title': 'ARM HOLDINGS PLC /UK'},\n", " 'ADBE': {'cik_str': 796343, 'ticker': 'ADBE', 'title': 'ADOBE INC.'},\n", " 'NEE': {'cik_str': 753308, 'ticker': 'NEE', 'title': 'NEXTERA ENERGY INC'},\n", " 'GILD': {'cik_str': 882095,\n", "  'ticker': 'GIL<PERSON>',\n", "  'title': 'GILEAD SCIENCES, INC.'},\n", " 'PGR': {'cik_str': 80661, 'ticker': 'PGR', 'title': 'PROGRESSIVE CORP/OH/'},\n", " 'APP': {'cik_str': 1751008, 'ticker': 'APP', 'title': 'AppLovin Corp'},\n", " 'SPOT': {'cik_str': 1639920,\n", "  'ticker': 'SPOT',\n", "  'title': 'Spotify Technology S.A.'},\n", " 'SYK': {'cik_str': 310764, 'ticker': 'SYK', 'title': 'STRYKER CORP'},\n", " 'PFE': {'cik_str': 78003, 'ticker': 'PFE', 'title': 'PFIZER INC'},\n", " 'SAN': {'cik_str': 891478, 'ticker': 'SAN', 'title': '<PERSON><PERSON>, S.A.'},\n", " 'LOW': {'cik_str': 60667, 'ticker': 'LOW', 'title': 'LOWES COMPANIES INC'},\n", " 'COF': {'cik_str': 927628,\n", "  'ticker': 'COF',\n", "  'title': 'CAPITAL ONE FINANCIAL CORP'},\n", " 'MU': {'cik_str': 723125, 'ticker': 'MU', 'title': 'MICRON TECHNOLOGY INC'},\n", " 'ETN': {'cik_str': 1551182, 'ticker': 'ETN', 'title': 'Eaton Corp plc'},\n", " 'HON': {'cik_str': 773840,\n", "  'ticker': 'H<PERSON>',\n", "  'title': 'HONEYWELL INTERNATIONAL INC'},\n", " 'BHP': {'cik_str': 811809, 'ticker': 'BHP', 'title': 'BHP Group Ltd'},\n", " 'LRCX': {'cik_str': 707549, 'ticker': 'LRCX', 'title': 'LAM RESEARCH CORP'},\n", " 'KKR': {'cik_str': 1404912, 'ticker': 'KKR', 'title': 'KKR & Co. Inc.'},\n", " 'TTE': {'cik_str': 879764, 'ticker': 'TTE', 'title': 'TotalEnergies SE'},\n", " 'APH': {'cik_str': 820313, 'ticker': 'APH', 'title': 'AMPHENOL CORP /DE/'},\n", " 'HTHIY': {'cik_str': 47710, 'ticker': 'HTHIY', 'title': 'HITACHI LTD'},\n", " 'UNP': {'cik_str': 100885, 'ticker': 'UNP', 'title': 'UNION PACIFIC CORP'},\n", " 'DE': {'cik_str': 315189, 'ticker': 'DE', 'title': 'DEERE & CO'},\n", " 'TD': {'cik_str': 947263, 'ticker': 'TD', 'title': 'TORONTO DOMINION BANK'},\n", " 'BTI': {'cik_str': 1303523,\n", "  'ticker': 'BTI',\n", "  'title': 'British American Tobacco p.l.c.'},\n", " 'UBS': {'cik_str': 1610520, 'ticker': 'UBS', 'title': 'UBS Group AG'},\n", " 'KLAC': {'cik_str': 319201, 'ticker': 'KLAC', 'title': 'KLA CORP'},\n", " 'RTNTF': {'cik_str': 887028, 'ticker': 'RTNTF', 'title': 'RIO TINTO LTD'},\n", " 'ADP': {'cik_str': 8670,\n", "  'ticker': 'ADP',\n", "  'title': 'AUTOMATIC DATA PROCESSING INC'},\n", " 'CMCSA': {'cik_str': 1166691, 'ticker': 'CMCSA', 'title': 'COMCAST CORP'},\n", " 'SNY': {'cik_str': 1121404, 'ticker': 'SNY', 'title': 'Sanofi'},\n", " 'COP': {'cik_str': 1163165, 'ticker': 'COP', 'title': 'CONOCOPHILLIPS'},\n", " 'BUD': {'cik_str': 1668717,\n", "  'ticker': 'BUD',\n", "  'title': 'Anheuser-Busch InBev SA/NV'},\n", " 'MELI': {'cik_str': 1099590, 'ticker': 'MELI', 'title': 'MERCADOLIBRE INC'},\n", " 'MDT': {'cik_str': 1613103, 'ticker': 'MDT', 'title': 'Medtronic plc'},\n", " 'PANW': {'cik_str': 1327567,\n", "  'ticker': 'PANW',\n", "  'title': 'Palo Alto Networks Inc'},\n", " 'ADI': {'cik_str': 6281, 'ticker': 'ADI', 'title': 'ANALOG DEVICES INC'},\n", " 'IBN': {'cik_str': 1103838, 'ticker': 'IBN', 'title': 'ICICI BANK LTD'},\n", " 'SNPS': {'cik_str': 883241, 'ticker': 'SNPS', 'title': 'SYNOPSYS INC'},\n", " 'NKE': {'cik_str': 320187, 'ticker': 'NKE', 'title': 'NIKE, Inc.'},\n", " 'MO': {'cik_str': 764180, 'ticker': 'MO', 'title': 'ALTRIA GROUP, INC.'},\n", " 'CB': {'cik_str': 896159, 'ticker': 'CB', 'title': 'Chubb Ltd'},\n", " 'BBVA': {'cik_str': 842180,\n", "  'ticker': 'BB<PERSON>',\n", "  'title': 'BANCO BILBAO VIZCAYA ARGENTARIA, S.A.'},\n", " 'IBKR': {'cik_str': 1381197,\n", "  'ticker': 'IBKR',\n", "  'title': 'Interactive Brokers Group, Inc.'},\n", " 'WELL': {'cik_str': 766704, 'ticker': 'WELL', 'title': 'WELLTOWER INC.'},\n", " 'SMFG': {'cik_str': 1022837,\n", "  'ticker': 'SMFG',\n", "  'title': '<PERSON><PERSON><PERSON><PERSON><PERSON> MITSUI FINANCIAL GROUP, INC.'},\n", " 'DASH': {'cik_str': 1792789, 'ticker': 'DASH', 'title': 'DoorDash, Inc.'},\n", " 'SBUX': {'cik_str': 829224, 'ticker': 'SBUX', 'title': 'STARBUCKS CORP'},\n", " 'MSTR': {'cik_str': 1050446, 'ticker': 'MSTR', 'title': 'Strategy Inc'},\n", " 'CRWD': {'cik_str': 1535527,\n", "  'ticker': 'CRWD',\n", "  'title': 'CrowdStrike Holdings, Inc.'},\n", " 'ENB': {'cik_str': 895728, 'ticker': 'ENB', 'title': 'ENBRIDGE INC'},\n", " 'ICE': {'cik_str': 1571949,\n", "  'ticker': 'ICE',\n", "  'title': 'Intercontinental Exchange, Inc.'},\n", " 'SE': {'cik_str': 1703399, 'ticker': 'SE', 'title': 'Sea Ltd'},\n", " 'SO': {'cik_str': 92122, 'ticker': 'SO', 'title': 'SOUTHERN CO'},\n", " 'CEG': {'cik_str': 1868275,\n", "  'ticker': 'CEG',\n", "  'title': 'Constellation Energy Corp'},\n", " 'LMT': {'cik_str': 936468, 'ticker': 'LMT', 'title': 'LOCKHEED MARTIN CORP'},\n", " 'MMC': {'cik_str': 62709,\n", "  'ticker': 'MMC',\n", "  'title': 'MARSH & MCLENNAN COMPANIES, INC.'},\n", " 'BAM': {'cik_str': 1937926,\n", "  'ticker': 'BAM',\n", "  'title': 'Brookfield Asset Management Ltd.'},\n", " 'VRTX': {'cik_str': 875320,\n", "  'ticker': 'VRTX',\n", "  'title': 'VERTEX PHARMACEUTICALS INC / MA'},\n", " 'RIO': {'cik_str': 863064, 'ticker': 'RIO', 'title': 'RIO TINTO PLC'},\n", " 'CME': {'cik_str': 1156375, 'ticker': 'CME', 'title': 'CME GROUP INC.'},\n", " 'INTC': {'cik_str': 50863, 'ticker': 'INTC', 'title': 'INTEL CORP'},\n", " 'PLD': {'cik_str': 1045609, 'ticker': 'PLD', 'title': 'Prologis, Inc.'},\n", " 'BN': {'cik_str': 1001085, 'ticker': 'BN', 'title': 'BROOKFIELD Corp /ON/'},\n", " 'HOOD': {'cik_str': 1783879,\n", "  'ticker': 'HOOD',\n", "  'title': 'Robinhood Markets, Inc.'},\n", " 'BMY': {'cik_str': 14272,\n", "  'ticker': 'BM<PERSON>',\n", "  'title': 'BRISTOL MYERS SQUIBB CO'},\n", " 'DUK': {'cik_str': 1326160, 'ticker': 'DUK', 'title': 'Duke Energy CORP'},\n", " 'CFRUY': {'cik_str': 948401,\n", "  'ticker': 'CFRUY',\n", "  'title': 'COMPAGNI<PERSON> FINANCIERE RICHEMONT AG /FI'},\n", " 'TT': {'cik_str': 1466258, 'ticker': 'TT', 'title': 'Trane Technologies plc'},\n", " 'PH': {'cik_str': 76334, 'ticker': 'PH', 'title': 'Parker-Hannifin Corp'},\n", " 'AMT': {'cik_str': 1053507,\n", "  'ticker': 'AMT',\n", "  'title': 'AMERICAN TOWER CORP /MA/'},\n", " 'CDNS': {'cik_str': 813672,\n", "  'ticker': 'CDNS',\n", "  'title': 'CADENCE DESIGN SYSTEMS INC'},\n", " 'DELL': {'cik_str': 1571996,\n", "  'ticker': 'DELL',\n", "  'title': 'Dell Technologies Inc.'},\n", " 'MCO': {'cik_str': 1059556, 'ticker': 'MCO', 'title': 'MOODYS CORP /DE/'},\n", " 'HCA': {'cik_str': 860730, 'ticker': 'HCA', 'title': 'HCA Healthcare, Inc.'},\n", " 'WM': {'cik_str': 823768, 'ticker': 'WM', 'title': 'WASTE MANAGEMENT INC'},\n", " 'SHW': {'cik_str': 89800, 'ticker': 'SHW', 'title': 'SHERWIN WILLIAMS CO'},\n", " 'CTAS': {'cik_str': 723254, 'ticker': 'CTAS', 'title': 'CINTAS CORP'},\n", " 'BP': {'cik_str': 313807, 'ticker': 'BP', 'title': 'BP PLC'},\n", " 'RBLX': {'cik_str': 1315098, 'ticker': 'RBLX', 'title': 'Roblox Corp'},\n", " 'RELX': {'cik_str': 929869, 'ticker': 'RELX', 'title': 'RELX PLC'},\n", " 'ORLY': {'cik_str': 898173,\n", "  'ticker': '<PERSON>L<PERSON>',\n", "  'title': 'O REILLY AUTOMOTIVE INC'},\n", " 'RCL': {'cik_str': 884887,\n", "  'ticker': 'RCL',\n", "  'title': 'ROYAL CARIBBEAN CRUISES LTD'},\n", " 'GD': {'cik_str': 40533, 'ticker': 'GD', 'title': 'GENERAL DYNAMICS CORP'},\n", " 'CVS': {'cik_str': 64803, 'ticker': 'CVS', 'title': 'CVS HEALTH Corp'},\n", " 'NTES': {'cik_str': 1110646, 'ticker': 'NTES', 'title': 'NetEase, Inc.'},\n", " 'MMM': {'cik_str': 66740, 'ticker': 'MMM', 'title': '3M CO'},\n", " 'MCK': {'cik_str': 927653, 'ticker': 'MCK', 'title': 'MCKESSON CORP'},\n", " 'NOC': {'cik_str': 1133421,\n", "  'ticker': 'NOC',\n", "  'title': 'NORTHROP GRUMMAN CORP /DE/'},\n", " 'COIN': {'cik_str': 1679788,\n", "  'ticker': 'COIN',\n", "  'title': 'Coinbase Global, Inc.'},\n", " 'RACE': {'cik_str': 1648416, 'ticker': 'RACE', 'title': 'Ferrari N.V.'},\n", " 'BMO': {'cik_str': 927971,\n", "  'ticker': 'B<PERSON>',\n", "  'title': 'BANK OF MONTREAL /CAN/'},\n", " 'MFG': {'cik_str': 1335730,\n", "  'ticker': 'MFG',\n", "  'title': 'MIZUHO FINANCIAL GROUP INC'},\n", " 'APO': {'cik_str': 1858681,\n", "  'ticker': 'APO',\n", "  'title': 'Apollo Global Management, Inc.'},\n", " 'GLD': {'cik_str': 1222333, 'ticker': 'GLD', 'title': 'SPDR GOLD TRUST'},\n", " 'TDG': {'cik_str': 1260221, 'ticker': 'TDG', 'title': 'TransDigm Group INC'},\n", " 'MDLZ': {'cik_str': 1103982,\n", "  'ticker': 'MDLZ',\n", "  'title': 'Mondelez International, Inc.'},\n", " 'ECL': {'cik_str': 31462, 'ticker': 'ECL', 'title': 'ECOLAB INC.'},\n", " 'AON': {'cik_str': 315293, 'ticker': 'AON', 'title': 'Aon plc'},\n", " 'GSK': {'cik_str': 1131399, 'ticker': 'GSK', 'title': 'GSK plc'},\n", " 'SCCO': {'cik_str': 1001838,\n", "  'ticker': 'SCCO',\n", "  'title': 'SOUTHERN COPPER CORP/'},\n", " 'CI': {'cik_str': 1739940, 'ticker': 'CI', 'title': 'Cigna Group'},\n", " 'MSI': {'cik_str': 68505,\n", "  'ticker': 'MSI',\n", "  'title': 'Motorola Solutions, Inc.'},\n", " 'ITW': {'cik_str': 49826,\n", "  'ticker': 'ITW',\n", "  'title': 'ILLINOIS TOOL WORKS INC'},\n", " 'ABNB': {'cik_str': 1559720, 'ticker': 'ABNB', 'title': 'Airbnb, Inc.'},\n", " 'PNC': {'cik_str': 713676,\n", "  'ticker': 'P<PERSON>',\n", "  'title': 'PNC FINANCIAL SERVICES GROUP, INC.'},\n", " 'MGCLY': {'cik_str': 2039784,\n", "  'ticker': 'MGCL<PERSON>',\n", "  'title': 'Midea Group Co., Ltd./ADR'},\n", " 'TRI': {'cik_str': 1075124,\n", "  'ticker': 'TRI',\n", "  'title': 'THOMSON REUTERS CORP /CAN/'},\n", " 'EMR': {'cik_str': 32604, 'ticker': 'EMR', 'title': 'EMERSON ELECTRIC CO'},\n", " 'EQIX': {'cik_str': 1101239, 'ticker': 'EQIX', 'title': 'EQUINIX INC'},\n", " 'PBR': {'cik_str': 1119639,\n", "  'ticker': 'PBR',\n", "  'title': 'PETROBRAS - PETROLEO BRASILEIRO SA'},\n", " 'CRH': {'cik_str': 849395, 'ticker': 'CRH', 'title': 'CRH PUBLIC LTD CO'},\n", " 'UPS': {'cik_str': 1090727,\n", "  'ticker': 'UPS',\n", "  'title': 'UNITED PARCEL SERVICE INC'},\n", " 'NEM': {'cik_str': 1164727, 'ticker': 'NEM', 'title': 'NEWMONT Corp /DE/'},\n", " 'AJG': {'cik_str': 354190,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'Arthur J. Gallagher & Co.'},\n", " 'ING': {'cik_str': 1039765, 'ticker': 'ING', 'title': 'ING GROEP NV'},\n", " 'BK': {'cik_str': 1390777,\n", "  'ticker': 'B<PERSON>',\n", "  'title': 'Bank of New York Mellon Corp'},\n", " 'FI': {'cik_str': 798354, 'ticker': 'FI', 'title': 'FISERV INC'},\n", " 'RSG': {'cik_str': 1060391,\n", "  'ticker': 'RSG',\n", "  'title': 'REPUBLIC SERVICES, INC.'},\n", " 'USB': {'cik_str': 36104, 'ticker': 'USB', 'title': 'US BANCORP DE'},\n", " 'MAR': {'cik_str': 1048286,\n", "  'ticker': 'MAR',\n", "  'title': 'MARRIOTT INTERNATIONAL INC /MD/'},\n", " 'BCS': {'cik_str': 312069, 'ticker': 'BCS', 'title': 'BARCLAYS PLC'},\n", " 'HWM': {'cik_str': 4281, 'ticker': 'HWM', 'title': 'Howmet Aerospace Inc.'},\n", " 'BAESY': {'cik_str': 895564,\n", "  'ticker': 'BAESY',\n", "  'title': 'BAE SYSTEMS PLC /FI/'},\n", " 'NGG': {'cik_str': 1004315, 'ticker': 'NGG', 'title': 'NATIONAL GRID PLC'},\n", " 'ITUB': {'cik_str': 1132597,\n", "  'ticker': 'ITUB',\n", "  'title': 'Itau Unibanco Holding S.A.'},\n", " 'DB': {'cik_str': 1159508,\n", "  'ticker': '<PERSON>',\n", "  'title': '<PERSON><PERSON><PERSON><PERSON><PERSON> BANK AKTIENGESELLSCHAFT'},\n", " 'BNS': {'cik_str': 9631, 'ticker': 'BNS', 'title': 'BANK OF NOVA SCOTIA'},\n", " 'WMB': {'cik_str': 107263,\n", "  'ticker': 'WMB',\n", "  'title': 'WILLIAMS COMPANIES, INC.'},\n", " 'JCI': {'cik_str': 833444,\n", "  'ticker': 'JCI',\n", "  'title': 'Johnson Controls International plc'},\n", " 'EPD': {'cik_str': 1061219,\n", "  'ticker': 'EPD',\n", "  'title': 'ENTERPRISE PRODUCTS PARTNERS L.P.'},\n", " 'VST': {'cik_str': 1692819, 'ticker': 'VST', 'title': 'Vistra Corp.'},\n", " 'CM': {'cik_str': 1045520,\n", "  'ticker': 'CM',\n", "  'title': 'CANADIAN IMPERIAL BANK OF COMMERCE /CAN/'},\n", " 'CL': {'cik_str': 21665, 'ticker': 'CL', 'title': 'COLGATE PALMOLIVE CO'},\n", " 'MRVL': {'cik_str': 1835632,\n", "  'ticker': 'MRVL',\n", "  'title': 'Marvell Technology, Inc.'},\n", " 'LYG': {'cik_str': 1160106,\n", "  'ticker': 'LYG',\n", "  'title': 'Lloyds Banking Group plc'},\n", " 'NET': {'cik_str': 1477333, 'ticker': 'NET', 'title': 'Cloudflare, Inc.'},\n", " 'INFY': {'cik_str': 1067491, 'ticker': 'INFY', 'title': 'Infosys Ltd'},\n", " 'CP': {'cik_str': 16875,\n", "  'ticker': 'CP',\n", "  'title': 'CANADIAN PACIFIC KANSAS CITY LTD/CN'},\n", " 'CSX': {'cik_str': 277948, 'ticker': 'CSX', 'title': 'CSX CORP'},\n", " 'ZTS': {'cik_str': 1555280, 'ticker': 'ZTS', 'title': 'Zoetis Inc.'},\n", " 'AZO': {'cik_str': 866787, 'ticker': 'AZO', 'title': 'AUTOZONE INC'},\n", " 'ELV': {'cik_str': 1156039,\n", "  'ticker': 'ELV',\n", "  'title': 'Elevance Health, Inc.'},\n", " 'AEM': {'cik_str': 2809, 'ticker': 'AEM', 'title': 'AGNICO EAGLE MINES LTD'},\n", " 'PYPL': {'cik_str': 1633917,\n", "  'ticker': 'PYPL',\n", "  'title': 'PayPal Holdings, Inc.'},\n", " 'EOG': {'cik_str': 821189, 'ticker': 'EOG', 'title': 'EOG RESOURCES INC'},\n", " 'SNOW': {'cik_str': 1640147, 'ticker': 'SNOW', 'title': 'Snowflake Inc.'},\n", " 'SPG': {'cik_str': 1063761,\n", "  'ticker': 'SPG',\n", "  'title': 'SIMON PROPERTY GROUP INC /DE/'},\n", " 'APD': {'cik_str': 2969,\n", "  'ticker': 'APD',\n", "  'title': 'Air Products & Chemicals, Inc.'},\n", " 'GBTC': {'cik_str': 1588489,\n", "  'ticker': 'GBTC',\n", "  'title': 'Grayscale Bitcoin Trust ETF'},\n", " 'HLT': {'cik_str': 1585689,\n", "  'ticker': 'HLT',\n", "  'title': 'Hilton Worldwide Holdings Inc.'},\n", " 'NSC': {'cik_str': 702165, 'ticker': 'NSC', 'title': 'NORFOLK SOUTHERN CORP'},\n", " 'MNST': {'cik_str': 865752,\n", "  'ticker': 'MNST',\n", "  'title': 'Monster Beverage Corp'},\n", " 'ARES': {'cik_str': 1176948,\n", "  'ticker': 'ARES',\n", "  'title': 'Ares Management Corp'},\n", " 'EQNR': {'cik_str': 1140625, 'ticker': 'EQNR', 'title': 'EQUINOR ASA'},\n", " 'CNQ': {'cik_str': 1017413,\n", "  'ticker': 'CNQ',\n", "  'title': 'CANADIAN NATURAL RESOURCES LTD'},\n", " 'BMWKY': {'cik_str': 1446250,\n", "  'ticker': 'BMWKY',\n", "  'title': 'Bayerische Motoren Werke AG/ADR'},\n", " 'DEO': {'cik_str': 835403, 'ticker': 'DEO', 'title': 'DIAGEO PLC'},\n", " 'TEL': {'cik_str': 1385157, 'ticker': 'TEL', 'title': 'TE Connectivity plc'},\n", " 'IAU': {'cik_str': 1278680, 'ticker': 'IAU', 'title': 'ISHARES GOLD TRUST'},\n", " 'NWG': {'cik_str': 844150, 'ticker': 'NWG', 'title': 'NatWest Group plc'},\n", " 'FCX': {'cik_str': 831259, 'ticker': 'FCX', 'title': 'FREEPORT-MCMORAN INC'},\n", " 'ADSK': {'cik_str': 769397, 'ticker': 'ADSK', 'title': 'Autodesk, Inc.'},\n", " 'TRV': {'cik_str': 86312,\n", "  'ticker': 'TRV',\n", "  'title': 'TRAVELERS COMPANIES, INC.'},\n", " 'AEP': {'cik_str': 4904,\n", "  'ticker': 'AEP',\n", "  'title': 'AMERICAN ELECTRIC POWER CO INC'},\n", " 'REGN': {'cik_str': 872589,\n", "  'ticker': 'REGN',\n", "  'title': 'REGEN<PERSON>ON PHARMACEUTICALS, INC.'},\n", " 'ET': {'cik_str': 1276187, 'ticker': 'ET', 'title': 'Energy Transfer LP'},\n", " 'KMI': {'cik_str': 1506307, 'ticker': 'KMI', 'title': 'KINDER MORGAN, INC.'},\n", " 'FTNT': {'cik_str': 1262039, 'ticker': 'FTNT', 'title': 'Fortinet, Inc.'},\n", " 'URI': {'cik_str': 1067701, 'ticker': 'URI', 'title': 'UNITED RENTALS, INC.'},\n", " 'WDAY': {'cik_str': 1327811, 'ticker': 'WDAY', 'title': 'Workday, Inc.'},\n", " 'AXON': {'cik_str': 1069183,\n", "  'ticker': 'AXON',\n", "  'title': 'AXON ENTERPRISE, INC.'},\n", " 'TFC': {'cik_str': 92230, 'ticker': 'TFC', 'title': 'TRUIST FINANCIAL CORP'},\n", " 'ALNY': {'cik_str': 1178670,\n", "  'ticker': 'ALN<PERSON>',\n", "  'title': 'ALNYLAM PHARMACEUTICALS, INC.'},\n", " 'NXPI': {'cik_str': 1413447,\n", "  'ticker': 'NXPI',\n", "  'title': 'NXP Semiconductors N.V.'},\n", " 'DLR': {'cik_str': 1297996,\n", "  'ticker': 'DLR',\n", "  'title': 'DIGITAL REALTY TRUST, INC.'},\n", " 'NU': {'cik_str': 1691493, 'ticker': 'NU', 'title': 'Nu Holdings Ltd.'},\n", " 'CNI': {'cik_str': 16868,\n", "  'ticker': 'C<PERSON>',\n", "  'title': 'CANADIAN NATIONAL RAILWAY CO'},\n", " 'CMG': {'cik_str': 1058090,\n", "  'ticker': 'C<PERSON>',\n", "  'title': 'CHIPOTLE MEXICAN GRILL INC'},\n", " 'COR': {'cik_str': 1140859, 'ticker': 'COR', 'title': 'Cencora, Inc.'},\n", " 'AMX': {'cik_str': 1129137,\n", "  'ticker': 'AMX',\n", "  'title': 'AMERICA MOVIL SAB DE CV/'},\n", " 'ROP': {'cik_str': 882835,\n", "  'ticker': 'ROP',\n", "  'title': 'ROPER TECHNOLOGIES INC'},\n", " 'GLW': {'cik_str': 24741, 'ticker': 'GLW', 'title': 'CORNING INC /NY'},\n", " 'PWR': {'cik_str': 1050915,\n", "  'ticker': 'P<PERSON>',\n", "  'title': 'QUANTA SERVICES, INC.'},\n", " 'AFL': {'cik_str': 4977, 'ticker': 'AFL', 'title': 'AFLAC INC'},\n", " 'ATEYY': {'cik_str': 1158838, 'ticker': 'ATEYY', 'title': 'ADVANTEST CORP'},\n", " 'FAST': {'cik_str': 815556, 'ticker': 'FAST', 'title': 'FASTENAL CO'},\n", " 'CARR': {'cik_str': 1783180,\n", "  'ticker': 'CARR',\n", "  'title': 'CARRIER GLOBAL Corp'},\n", " 'BDX': {'cik_str': 10795, 'ticker': 'BDX', 'title': 'BECTON DICKINSON & CO'},\n", " 'CMI': {'cik_str': 26172, 'ticker': 'CMI', 'title': 'CUMMINS INC'},\n", " 'IFNNY': {'cik_str': 1107457,\n", "  'ticker': 'IFNNY',\n", "  'title': 'INFINEON TECHNOLOGIES AG'},\n", " 'FDX': {'cik_str': 1048911, 'ticker': 'FDX', 'title': 'FEDEX CORP'},\n", " 'ALL': {'cik_str': 899051, 'ticker': 'ALL', 'title': 'ALLSTATE CORP'},\n", " 'NDAQ': {'cik_str': 1120193, 'ticker': 'NDAQ', 'title': 'NASDAQ, INC.'},\n", " 'SRE': {'cik_str': 1032208, 'ticker': 'SRE', 'title': 'SEMPRA'},\n", " 'GM': {'cik_str': 1467858, 'ticker': 'GM', 'title': 'General Motors Co'},\n", " 'TRP': {'cik_str': 1232384, 'ticker': 'TRP', 'title': 'TC ENERGY CORP'},\n", " 'E': {'cik_str': 1002242, 'ticker': 'E', 'title': 'ENI SPA'},\n", " 'O': {'cik_str': 726728, 'ticker': 'O', 'title': 'REALTY INCOME CORP'},\n", " 'CPNG': {'cik_str': 1834584, 'ticker': 'CPNG', 'title': 'Coupang, Inc.'},\n", " 'D': {'cik_str': 715957, 'ticker': 'D', 'title': 'DOMINION ENERGY, INC'},\n", " 'PCAR': {'cik_str': 75362, 'ticker': 'PCAR', 'title': 'PACCAR INC'},\n", " 'IDXX': {'cik_str': 874716,\n", "  'ticker': 'IDXX',\n", "  'title': 'IDEXX LABORATORIES INC /DE'},\n", " 'MFC': {'cik_str': 1086888,\n", "  'ticker': 'MFC',\n", "  'title': 'MANULIFE FINANCIAL CORP'},\n", " 'MET': {'cik_str': 1099219, 'ticker': 'MET', 'title': 'METLIFE INC'},\n", " 'LNG': {'cik_str': 3570, 'ticker': 'LNG', 'title': 'Cheniere Energy, Inc.'},\n", " 'FLUT': {'cik_str': 1635327,\n", "  'ticker': 'FLUT',\n", "  'title': 'Flutter Entertainment plc'},\n", " 'LHX': {'cik_str': 202058,\n", "  'ticker': 'LHX',\n", "  'title': 'L3HARRIS TECHNOLOGIES, INC. /DE/'},\n", " 'MPLX': {'cik_str': 1552000, 'ticker': 'MPLX', 'title': 'MPLX LP'},\n", " 'VRT': {'cik_str': 1674101, 'ticker': 'VRT', 'title': 'Vertiv Holdings Co'},\n", " 'PAYX': {'cik_str': 723531, 'ticker': 'PAYX', 'title': 'PAYCHEX INC'},\n", " 'CRWV': {'cik_str': 1769628, 'ticker': 'CRWV', 'title': 'CoreWeave, Inc.'},\n", " 'PSX': {'cik_str': 1534701, 'ticker': 'PSX', 'title': '<PERSON> 66'},\n", " 'PSA': {'cik_str': 1393311, 'ticker': 'PSA', 'title': 'Public Storage'},\n", " 'MPC': {'cik_str': 1510295,\n", "  'ticker': 'MPC',\n", "  'title': 'Marathon Petroleum Corp'},\n", " 'SLB': {'cik_str': 87347,\n", "  'ticker': 'SLB',\n", "  'title': 'SCHLUMBERGER LIMITED/NV'},\n", " 'CTVA': {'cik_str': 1755672, 'ticker': 'CTVA', 'title': 'Corteva, Inc.'},\n", " 'DHI': {'cik_str': 882184, 'ticker': 'DHI', 'title': 'HORTON D R INC /DE/'},\n", " 'PTCAY': {'cik_str': 1547873,\n", "  'ticker': 'PTCAY',\n", "  'title': 'PT Chandra Asri Petrochemical Tbk/ADR'},\n", " 'AMP': {'cik_str': 820027,\n", "  'ticker': 'AMP',\n", "  'title': 'AMERIPRISE FINANCIAL INC'},\n", " 'ROST': {'cik_str': 745732, 'ticker': 'ROST', 'title': 'ROSS STORES, INC.'},\n", " 'WCN': {'cik_str': 1318220,\n", "  'ticker': 'WCN',\n", "  'title': 'Waste Connections, Inc.'},\n", " 'SU': {'cik_str': 311337, 'ticker': 'SU', 'title': 'SUNCOR ENERGY INC'},\n", " 'KDP': {'cik_str': 1418135,\n", "  'ticker': 'KDP',\n", "  'title': 'Keurig Dr Pepper Inc.'},\n", " 'CBRE': {'cik_str': 1138118, 'ticker': 'CBRE', 'title': 'CBRE GROUP, INC.'},\n", " 'TGT': {'cik_str': 27419, 'ticker': 'TGT', 'title': 'TARGET CORP'},\n", " 'OKE': {'cik_str': 1039684, 'ticker': 'OKE', 'title': 'ONEOK INC /NEW/'},\n", " 'TAK': {'cik_str': 1395064,\n", "  'ticker': 'TAK',\n", "  'title': 'TAKEDA PHARMACEUTICAL CO LTD'},\n", " 'GWW': {'cik_str': 277135, 'ticker': 'GWW', 'title': '<PERSON><PERSON><PERSON><PERSON>, INC.'},\n", " 'XYZ': {'cik_str': 1512673, 'ticker': 'XYZ', 'title': 'Block, Inc.'},\n", " 'JD': {'cik_str': 1549802, 'ticker': 'JD', 'title': 'JD.com, Inc.'},\n", " 'FERG': {'cik_str': 2011641,\n", "  'ticker': 'FERG',\n", "  'title': 'Ferguson Enterprises Inc. /DE/'},\n", " 'KR': {'cik_str': 56873, 'ticker': 'KR', 'title': 'KROGER CO'},\n", " 'EW': {'cik_str': 1099800,\n", "  'ticker': 'EW',\n", "  'title': 'Edwards Lifesciences Corp'},\n", " 'CPRT': {'cik_str': 900075, 'ticker': 'CPRT', 'title': 'COPART INC'},\n", " 'GRMN': {'cik_str': 1121788, 'ticker': 'GRMN', 'title': 'GARMIN LTD'},\n", " 'EBAY': {'cik_str': 1065088, 'ticker': 'EBAY', 'title': 'EBAY INC'},\n", " 'F': {'cik_str': 37996, 'ticker': 'F', 'title': 'FORD MOTOR CO'},\n", " 'EXC': {'cik_str': 1109357, 'ticker': 'EXC', 'title': 'EXELON CORP'},\n", " 'RDDT': {'cik_str': 1713445, 'ticker': 'RDDT', 'title': 'Reddit, Inc.'},\n", " 'VEEV': {'cik_str': 1393052, 'ticker': 'VEEV', 'title': 'VEEVA SYSTEMS INC'},\n", " 'EA': {'cik_str': 712515, 'ticker': 'EA', 'title': 'ELECTRONIC ARTS INC.'},\n", " 'AIG': {'cik_str': 5272,\n", "  'ticker': 'AIG',\n", "  'title': 'AMERICAN INTERNATIONAL GROUP, INC.'},\n", " 'KMB': {'cik_str': 55785, 'ticker': 'KMB', 'title': 'KIMBERLY CLARK CORP'},\n", " 'HMC': {'cik_str': 715153, 'ticker': 'HMC', 'title': 'HONDA MOTOR CO LTD'},\n", " 'OXY': {'cik_str': 797468,\n", "  'ticker': 'OXY',\n", "  'title': 'OCCIDENTAL PETROLEUM CORP /DE/'},\n", " 'MSCI': {'cik_str': 1408198, 'ticker': 'MSCI', 'title': 'MSCI Inc.'},\n", " 'DDOG': {'cik_str': 1561550, 'ticker': 'DDOG', 'title': 'Datadog, Inc.'},\n", " 'CCI': {'cik_str': 1051470, 'ticker': 'CCI', 'title': 'CROWN CASTLE INC.'},\n", " 'HLN': {'cik_str': 1900304, 'ticker': 'HLN', 'title': 'Haleon plc'},\n", " 'PEG': {'cik_str': 788784,\n", "  'ticker': 'PEG',\n", "  'title': 'PUBL<PERSON> SERVICE ENTERPRISE GROUP INC'},\n", " 'TTWO': {'cik_str': 946581,\n", "  'ticker': 'TTWO',\n", "  'title': 'TAKE TWO INTERACTIVE SOFTWARE INC'},\n", " 'VALE': {'cik_str': 917851, 'ticker': 'VALE', 'title': 'Vale S.A.'},\n", " 'TEAM': {'cik_str': 1650372, 'ticker': 'TEAM', 'title': 'Atlassian Corp'},\n", " 'XEL': {'cik_str': 72903, 'ticker': 'XEL', 'title': 'XCEL ENERGY INC'},\n", " 'ALC': {'cik_str': 1167379, 'ticker': 'ALC', 'title': 'ALCON INC'},\n", " 'AME': {'cik_str': 1037868, 'ticker': 'AME', 'title': 'AMETEK INC/'},\n", " 'WPM': {'cik_str': 1323404,\n", "  'ticker': 'WPM',\n", "  'title': 'Wheaton Precious Metals Corp.'},\n", " 'BKR': {'cik_str': 1701605, 'ticker': 'BKR', 'title': 'Baker Hughes Co'},\n", " 'IMO': {'cik_str': 49938, 'ticker': 'IMO', 'title': 'IMPERIAL OIL LTD'},\n", " 'VLO': {'cik_str': 1035002,\n", "  'ticker': 'VLO',\n", "  'title': 'VALERO ENERGY CORP/TX'},\n", " 'ZS': {'cik_str': 1713683, 'ticker': 'ZS', 'title': 'Zscaler, Inc.'},\n", " 'CCEP': {'cik_str': 1650107,\n", "  'ticker': 'CCEP',\n", "  'title': 'COCA-COLA EUROPACIFIC PARTNERS plc'},\n", " 'RMD': {'cik_str': 943819, 'ticker': 'RMD', 'title': 'RESMED INC'},\n", " 'CCL': {'cik_str': 815097, 'ticker': 'CCL', 'title': 'CARNIVAL CORP'},\n", " 'KVUE': {'cik_str': 1944048, 'ticker': 'KVUE', 'title': 'Kenvue Inc.'},\n", " 'YUM': {'cik_str': 1041061, 'ticker': 'YUM', 'title': 'YUM BRANDS INC'},\n", " 'FANG': {'cik_str': 1539838,\n", "  'ticker': 'FANG',\n", "  'title': 'Diamondback Energy, Inc.'},\n", " 'ETR': {'cik_str': 65984, 'ticker': 'ETR', 'title': 'ENTERGY CORP /DE/'},\n", " 'MPWR': {'cik_str': 1280452,\n", "  'ticker': 'MPWR',\n", "  'title': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POWER SYSTEMS INC'},\n", " 'TME': {'cik_str': 1744676,\n", "  'ticker': 'TME',\n", "  'title': 'Tencent Music Entertainment Group'},\n", " 'B': {'cik_str': 756894, 'ticker': 'B', 'title': 'BARRICK MINING CORP'},\n", " 'CVNA': {'cik_str': 1690820, 'ticker': 'CVNA', 'title': 'CARVANA CO.'},\n", " 'RKT': {'cik_str': 1805284,\n", "  'ticker': 'RKT',\n", "  'title': 'Rocket Companies, Inc.'},\n", " 'ARGX': {'cik_str': 1697862, 'ticker': 'ARGX', 'title': 'ARGENX SE'},\n", " 'TCOM': {'cik_str': 1269238, 'ticker': 'TCOM', 'title': 'Trip.com Group Ltd'},\n", " 'FER': {'cik_str': 1468522, 'ticker': 'FER', 'title': 'Ferrovial SE'},\n", " 'ROK': {'cik_str': 1024478,\n", "  'ticker': 'R<PERSON>',\n", "  'title': 'ROCKWELL AUTOMATION, INC'},\n", " 'SYY': {'cik_str': 96021, 'ticker': 'SYY', 'title': 'SYSCO CORP'},\n", " 'VMC': {'cik_str': 1396009, 'ticker': 'VMC', 'title': 'Vulcan Materials CO'},\n", " 'DAL': {'cik_str': 27904, 'ticker': 'DAL', 'title': 'DELTA AIR LINES, INC.'},\n", " 'FRFHF': {'cik_str': 915191,\n", "  'ticker': 'FRFHF',\n", "  'title': 'FAIRFAX FINANCIAL HOLDINGS LTD/ CAN'},\n", " 'GALDY': {'cik_str': 2021390,\n", "  'ticker': 'GALDY',\n", "  'title': 'Galderma Group AG/ADR'},\n", " 'FIG': {'cik_str': 1579878, 'ticker': 'FIG', 'title': 'Figma, Inc.'},\n", " 'HEI': {'cik_str': 46619, 'ticker': 'HEI', 'title': 'HEICO CORP'},\n", " 'PRU': {'cik_str': 1137774,\n", "  'ticker': 'PRU',\n", "  'title': 'PRUDENTIAL FINANCIAL INC'},\n", " 'LYV': {'cik_str': 1335258,\n", "  'ticker': 'LYV',\n", "  'title': 'Live Nation Entertainment, Inc.'},\n", " 'FIS': {'cik_str': 1136893,\n", "  'ticker': 'FIS',\n", "  'title': 'Fidelity National Information Services, Inc.'},\n", " 'VRSK': {'cik_str': 1442145,\n", "  'ticker': 'VRSK',\n", "  'title': 'Verisk Analytics, Inc.'},\n", " 'BSBR': {'cik_str': 1471055,\n", "  'ticker': 'BSBR',\n", "  'title': '<PERSON><PERSON> (Brasil) S.A.'},\n", " 'CSGP': {'cik_str': 1057352, 'ticker': 'CSGP', 'title': 'COSTAR GROUP, INC.'},\n", " 'ED': {'cik_str': 1047862,\n", "  'ticker': 'ED',\n", "  'title': 'CONSOLIDATED EDISON INC'},\n", " 'LVS': {'cik_str': 1300514, 'ticker': 'LVS', 'title': 'LAS VEGAS SANDS CORP'},\n", " 'HIG': {'cik_str': 874766,\n", "  'ticker': 'HIG',\n", "  'title': 'HARTFORD INSURANCE GROUP, INC.'},\n", " 'MLM': {'cik_str': 916076,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'MARTIN MARIETTA MATERIALS INC'},\n", " 'HSY': {'cik_str': 47111, 'ticker': 'HSY', 'title': 'HERSHEY CO'},\n", " 'ONC': {'cik_str': 1651308, 'ticker': 'ONC', 'title': 'BeOne Medicines Ltd.'},\n", " 'DIA': {'cik_str': 1041130,\n", "  'ticker': 'DIA',\n", "  'title': 'SPDR DOW JONES INDUSTRIAL AVERAGE ETF TRUST'},\n", " 'GWLIF': {'cik_str': 850918,\n", "  'ticker': 'GWLIF',\n", "  'title': 'GREAT-WEST LIFECO INC.'},\n", " 'CAH': {'cik_str': 721371, 'ticker': 'CAH', 'title': 'CARDINAL HEALTH INC'},\n", " 'CHTR': {'cik_str': 1091667,\n", "  'ticker': 'CHTR',\n", "  'title': 'CHARTER COMMUNICATIONS, INC. /MO/'},\n", " 'TRGP': {'cik_str': 1389170,\n", "  'ticker': 'TRGP',\n", "  'title': 'Targa Resources Corp.'},\n", " 'CRCL': {'cik_str': 1876042,\n", "  'ticker': 'CRCL',\n", "  'title': 'Circle Internet Group, Inc.'},\n", " 'MCHP': {'cik_str': 827054,\n", "  'ticker': 'MC<PERSON>',\n", "  'title': 'MICROCHIP TECHNOLOGY INC'},\n", " 'CUK': {'cik_str': 1125259, 'ticker': 'CUK', 'title': 'CARNIVAL PLC'},\n", " 'RYAAY': {'cik_str': 1038683,\n", "  'ticker': 'RYAA<PERSON>',\n", "  'title': 'RYANAIR HOLDINGS PLC'},\n", " 'CHT': {'cik_str': 1132924,\n", "  'ticker': 'CHT',\n", "  'title': 'CHUNGHWA TELECOM CO LTD'},\n", " 'VICI': {'cik_str': 1705696,\n", "  'ticker': 'VICI',\n", "  'title': 'VICI PROPERTIES INC.'},\n", " 'ABEV': {'cik_str': 1565025, 'ticker': 'ABEV', 'title': 'AMBEV S.A.'},\n", " 'WEC': {'cik_str': 783325,\n", "  'ticker': 'WEC',\n", "  'title': 'WEC ENERGY GROUP, INC.'},\n", " 'XYL': {'cik_str': 1524472, 'ticker': 'XYL', 'title': 'Xylem Inc.'},\n", " 'OTIS': {'cik_str': 1781335,\n", "  'ticker': 'OTIS',\n", "  'title': 'Otis Worldwide Corp'},\n", " 'PUK': {'cik_str': 1116578, 'ticker': 'PUK', 'title': 'PRUDENTIAL PLC'},\n", " 'FNV': {'cik_str': 1456346, 'ticker': 'FNV', 'title': 'FRANCO NEVADA Corp'},\n", " 'ACGL': {'cik_str': 947484,\n", "  'ticker': 'ACGL',\n", "  'title': 'ARCH CAPITAL GROUP LTD.'},\n", " 'HUM': {'cik_str': 49071, 'ticker': 'HUM', 'title': 'HUMANA INC'},\n", " 'CTSH': {'cik_str': 1058290,\n", "  'ticker': 'CTSH',\n", "  'title': 'COGNIZANT TECH<PERSON><PERSON>OGY SOLUTIONS CORP'},\n", " 'VG': {'cik_str': 2007855, 'ticker': 'VG', 'title': 'Venture Global, Inc.'},\n", " 'PCG': {'cik_str': 1004980, 'ticker': 'PCG', 'title': 'PG&E Corp'},\n", " 'A': {'cik_str': 1090872,\n", "  'ticker': 'A',\n", "  'title': 'AGILENT TECHNOLOGIES, INC.'},\n", " 'GEHC': {'cik_str': 1932393,\n", "  'ticker': 'GEHC',\n", "  'title': 'GE HealthCare Technologies Inc.'},\n", " 'LEN': {'cik_str': 920760, 'ticker': 'LEN', 'title': 'LENNAR CORP /NEW/'},\n", " 'WDS': {'cik_str': 844551,\n", "  'ticker': 'WDS',\n", "  'title': 'WOODSIDE ENERGY GROUP LTD'},\n", " 'STX': {'cik_str': 1137789,\n", "  'ticker': 'STX',\n", "  'title': 'Seagate Technology Holdings plc'},\n", " 'NUE': {'cik_str': 73309, 'ticker': 'NUE', 'title': 'NUCOR CORP'},\n", " 'CHLSY': {'cik_str': 1885110,\n", "  'ticker': 'CHLS<PERSON>',\n", "  'title': 'Chocoladefabriken Lindt & Spruengli AG/ADR'},\n", " 'KHC': {'cik_str': 1637459, 'ticker': 'KHC', 'title': 'Kraft Heinz Co'},\n", " 'CCJ': {'cik_str': 1009001, 'ticker': 'CCJ', 'title': 'CAMECO CORP'},\n", " 'RJF': {'cik_str': 720005,\n", "  'ticker': 'RJ<PERSON>',\n", "  'title': 'RAYMOND JAMES FINANCIAL INC'},\n", " 'WAB': {'cik_str': 943452,\n", "  'ticker': 'WAB',\n", "  'title': 'WESTINGHOUSE AIR BRAKE TECHNOLOGIES CORP'},\n", " 'SLF': {'cik_str': 1097362,\n", "  'ticker': 'SLF',\n", "  'title': 'SUN LIFE FINANCIAL INC'},\n", " 'MDY': {'cik_str': 936958,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'SPDR S&P MIDCAP 400 ETF TRUST'},\n", " 'WTW': {'cik_str': 1140536,\n", "  'ticker': 'WTW',\n", "  'title': 'WILLIS TOWERS WATSON PLC'},\n", " 'EQT': {'cik_str': 33213, 'ticker': 'EQT', 'title': 'EQT Corp'},\n", " 'STT': {'cik_str': 93751, 'ticker': 'STT', 'title': 'STATE STREET CORP'},\n", " 'EL': {'cik_str': 1001250,\n", "  'ticker': 'EL',\n", "  'title': 'ESTEE LAUDER COMPANIES INC'},\n", " 'IQV': {'cik_str': 1478242, 'ticker': 'IQV', 'title': 'IQVIA HOLDINGS INC.'},\n", " 'UAL': {'cik_str': 100517,\n", "  'ticker': 'UAL',\n", "  'title': 'United Airlines Holdings, Inc.'},\n", " 'IR': {'cik_str': 1699150, 'ticker': 'IR', 'title': 'Ingersoll Rand Inc.'},\n", " 'FICO': {'cik_str': 814547, 'ticker': 'FICO', 'title': 'FAIR ISAAC CORP'},\n", " 'ODFL': {'cik_str': 878927,\n", "  'ticker': 'ODFL',\n", "  'title': 'OLD DOMINION FREIGHT LINE, INC.'},\n", " 'TSCO': {'cik_str': 916365,\n", "  'ticker': 'TSCO',\n", "  'title': 'TRACTOR SUPPLY CO /DE/'},\n", " 'TEF': {'cik_str': 814052, 'ticker': 'TEF', 'title': 'TELEFONICA S A'},\n", " 'BRO': {'cik_str': 79282, 'ticker': 'BR<PERSON>', 'title': 'BROWN & BROWN, INC.'},\n", " 'ALAB': {'cik_str': 1736297, 'ticker': 'ALAB', 'title': 'Astera Labs, Inc.'},\n", " 'DXCM': {'cik_str': 1093557, 'ticker': 'DXCM', 'title': 'DEXCOM INC'},\n", " 'WTKWY': {'cik_str': 861967,\n", "  'ticker': 'WTKWY',\n", "  'title': 'WOLTERS KLUWER N V /FI'},\n", " 'SYM': {'cik_str': 1837240, 'ticker': 'SYM', 'title': 'Symbotic Inc.'},\n", " 'DD': {'cik_str': 1666700,\n", "  'ticker': 'DD',\n", "  'title': 'DuPont de Nemours, Inc.'},\n", " 'VTR': {'cik_str': 740260, 'ticker': 'VTR', 'title': 'Ventas, Inc.'},\n", " 'EFX': {'cik_str': 33185, 'ticker': 'EFX', 'title': 'EQUIFAX INC'},\n", " 'JBS': {'cik_str': 1791942, 'ticker': 'JBS', 'title': 'JBS N.V.'},\n", " 'BR': {'cik_str': 1383312,\n", "  'ticker': 'BR',\n", "  'title': 'BROADRIDGE FINANCIAL SOLUTIONS, INC.'},\n", " 'OWL': {'cik_str': 1823945,\n", "  'ticker': 'OW<PERSON>',\n", "  'title': 'BLUE OWL CAPITAL INC.'},\n", " 'EXR': {'cik_str': 1289490,\n", "  'ticker': 'EXR',\n", "  'title': 'Extra Space Storage Inc.'},\n", " 'MTB': {'cik_str': 36270, 'ticker': 'MTB', 'title': 'M&T BANK CORP'},\n", " 'BIDU': {'cik_str': 1329099, 'ticker': 'BIDU', 'title': 'Baidu, Inc.'},\n", " 'NRG': {'cik_str': 1013871, 'ticker': 'NRG', 'title': 'NRG ENERGY, INC.'},\n", " 'STZ': {'cik_str': 16918,\n", "  'ticker': 'STZ',\n", "  'title': 'CONSTELLATION BRANDS, INC.'},\n", " 'KB': {'cik_str': 1445930,\n", "  'ticker': 'K<PERSON>',\n", "  'title': 'KB Financial Group Inc.'},\n", " 'QSR': {'cik_str': 1618756,\n", "  'ticker': 'QSR',\n", "  'title': 'Restaurant Brands International Inc.'},\n", " 'WBD': {'cik_str': 1437107,\n", "  'ticker': 'WBD',\n", "  'title': 'Warner Bros. Discovery, Inc.'},\n", " 'TYHOY': {'cik_str': 2018139,\n", "  'ticker': 'TYHOY',\n", "  'title': 'Toyota Tsusho Corporation/ADR'},\n", " 'DTE': {'cik_str': 936340, 'ticker': 'DTE', 'title': 'DTE ENERGY CO'},\n", " 'FUJIY': {'cik_str': 800365,\n", "  'ticker': 'FUJIY',\n", "  'title': 'FUJI PHOTO FILM CO LTD /FI'},\n", " 'BBD': {'cik_str': 1160330, 'ticker': 'BBD', 'title': 'BANK BRADESCO'},\n", " 'IX': {'cik_str': 1070304, 'ticker': 'IX', 'title': 'ORIX CORP'},\n", " 'AMRZ': {'cik_str': 2035989, 'ticker': 'AMRZ', 'title': 'Amrize Ltd'},\n", " 'WIT': {'cik_str': 1123799, 'ticker': 'WIT', 'title': 'WIPRO LTD'},\n", " 'TW': {'cik_str': 1758730, 'ticker': 'TW', 'title': 'Tradeweb Markets Inc.'},\n", " 'FITB': {'cik_str': 35527, 'ticker': 'FITB', 'title': 'FIFTH THIRD BANCORP'},\n", " 'ADM': {'cik_str': 7084,\n", "  'ticker': 'ADM',\n", "  'title': 'Archer-Daniels-Midland Co'},\n", " 'LPLA': {'cik_str': 1397911,\n", "  'ticker': 'LPLA',\n", "  'title': 'LPL Financial Holdings Inc.'},\n", " 'KEYS': {'cik_str': 1601046,\n", "  'ticker': 'KEYS',\n", "  'title': 'Keysight Technologies, Inc.'},\n", " 'VOD': {'cik_str': 839923,\n", "  'ticker': 'VOD',\n", "  'title': 'VODAFONE GROUP PUBLIC LTD CO'},\n", " 'STLA': {'cik_str': 1605484, 'ticker': 'STLA', 'title': 'Stellantis N.V.'},\n", " 'HPE': {'cik_str': 1645590,\n", "  'ticker': 'HPE',\n", "  'title': 'Hewlett Packard Enterprise Co'},\n", " 'SOFI': {'cik_str': 1818874,\n", "  'ticker': 'SOFI',\n", "  'title': 'SoFi Technologies, Inc.'},\n", " 'AWK': {'cik_str': 1410636,\n", "  'ticker': 'AW<PERSON>',\n", "  'title': 'American Water Works Company, Inc.'},\n", " 'ROL': {'cik_str': 84839, 'ticker': 'ROL', 'title': 'ROLLINS INC'},\n", " 'K': {'cik_str': 55067, 'ticker': 'K', 'title': 'KELLANOVA'},\n", " 'AU': {'cik_str': 1973832, 'ticker': 'AU', 'title': 'AngloGold Ashanti PLC'},\n", " 'NTR': {'cik_str': 1725964, 'ticker': 'NTR', 'title': 'Nutrien Ltd.'},\n", " 'AEE': {'cik_str': 1002910, 'ticker': 'AEE', 'title': 'AMEREN CORP'},\n", " 'EME': {'cik_str': 105634, 'ticker': 'EME', 'title': 'EMCOR Group, Inc.'},\n", " 'PPL': {'cik_str': 922224, 'ticker': 'PPL', 'title': 'PPL Corp'},\n", " 'GFI': {'cik_str': 1172724, 'ticker': 'GFI', 'title': 'GOLD FIELDS LTD'},\n", " 'CVE': {'cik_str': 1475260, 'ticker': 'CVE', 'title': 'CENOVUS ENERGY INC.'},\n", " 'SYF': {'cik_str': 1601712, 'ticker': 'SYF', 'title': 'Synchrony Financial'},\n", " 'WRB': {'cik_str': 11544, 'ticker': 'WRB', 'title': 'BERKLEY W R CORP'},\n", " 'SMCI': {'cik_str': 1375365,\n", "  'ticker': 'SMCI',\n", "  'title': 'Super Micro Computer, Inc.'},\n", " 'IRM': {'cik_str': 1020569, 'ticker': 'IRM', 'title': 'IRON MOUNTAIN INC'},\n", " 'BNTX': {'cik_str': 1776985, 'ticker': 'BNTX', 'title': 'BioNTech SE'},\n", " 'AVB': {'cik_str': 915912,\n", "  'ticker': 'AVB',\n", "  'title': 'AVALONBAY COMMUNITIES INC'},\n", " 'MTD': {'cik_str': 1037646,\n", "  'ticker': 'MTD',\n", "  'title': 'METTLER TOLEDO INTERNATIONAL INC/'},\n", " 'INSM': {'cik_str': 1104506, 'ticker': 'INSM', 'title': 'INSMED Inc'},\n", " 'VLTO': {'cik_str': 1967680, 'ticker': 'VLTO', 'title': 'Veralto Corp'},\n", " 'WDC': {'cik_str': 106040, 'ticker': 'WDC', 'title': 'WESTERN DIGITAL CORP'},\n", " 'ATO': {'cik_str': 731802, 'ticker': 'ATO', 'title': 'ATMOS ENERGY CORP'},\n", " 'VIK': {'cik_str': 1745201, 'ticker': 'VIK', 'title': 'Viking Holdings Ltd'},\n", " 'GIS': {'cik_str': 40704, 'ticker': 'GIS', 'title': 'GENERAL MILLS INC'},\n", " 'CQP': {'cik_str': 1383650,\n", "  'ticker': 'CQP',\n", "  'title': 'Cheniere Energy Partners, L.P.'},\n", " 'UI': {'cik_str': 1511737, 'ticker': 'UI', 'title': 'Ubiquiti Inc.'},\n", " 'PHG': {'cik_str': 313216,\n", "  'ticker': 'PHG',\n", "  'title': 'K<PERSON><PERSON>KLIJKE PHILIPS NV'},\n", " 'EXPE': {'cik_str': 1324424,\n", "  'ticker': 'EXPE',\n", "  'title': 'Expedia Group, Inc.'},\n", " 'MT': {'cik_str': 1243429, 'ticker': 'MT', 'title': 'ArcelorMittal'},\n", " 'KBGGY': {'cik_str': 1535759,\n", "  'ticker': 'KBGGY',\n", "  'title': 'Kongsberg Gruppen ASA/ADR'},\n", " 'FTS': {'cik_str': 1666175, 'ticker': 'FTS', 'title': 'Fortis Inc.'},\n", " 'CBOE': {'cik_str': 1374310,\n", "  'ticker': 'CBOE',\n", "  'title': 'Cboe Global Markets, Inc.'},\n", " 'DIDIY': {'cik_str': 1764757, 'ticker': 'DIDIY', 'title': 'DiDi Global Inc.'},\n", " 'TDY': {'cik_str': 1094285,\n", "  'ticker': 'TDY',\n", "  'title': 'TELEDYNE TECHNOLOGIES INC'},\n", " 'FOXA': {'cik_str': 1754301, 'ticker': 'FOXA', 'title': 'Fox Corp'},\n", " 'WSM': {'cik_str': 719955, 'ticker': 'WSM', 'title': 'WILLIAMS SONOMA INC'},\n", " 'IP': {'cik_str': 51434,\n", "  'ticker': 'IP',\n", "  'title': 'INTERNATIONAL PAPER CO /NEW/'},\n", " 'HPQ': {'cik_str': 47217, 'ticker': 'HPQ', 'title': 'HP INC'},\n", " 'PHM': {'cik_str': 822416, 'ticker': 'PHM', 'title': 'PULTEGROUP INC/MI/'},\n", " 'ERIC': {'cik_str': 717826,\n", "  'ticker': 'ER<PERSON>',\n", "  'title': 'ERICSSON LM TELEPHONE CO'},\n", " 'SDZNY': {'cik_str': 1992829, 'ticker': 'SDZNY', 'title': 'SANDOZ GROUP AG'},\n", " 'FE': {'cik_str': 1031296, 'ticker': 'FE', 'title': 'FIRSTENERGY CORP'},\n", " 'PPG': {'cik_str': 79879, 'ticker': 'PPG', 'title': 'PPG INDUSTRIES INC'},\n", " 'CNP': {'cik_str': 1130310,\n", "  'ticker': 'CNP',\n", "  'title': 'CENTERPOINT ENERGY INC'},\n", " 'EQR': {'cik_str': 906107, 'ticker': 'EQR', 'title': 'EQUITY RESIDENTIAL'},\n", " 'DG': {'cik_str': 29534, 'ticker': 'DG', 'title': 'DOLLAR GENERAL CORP'},\n", " 'TOST': {'cik_str': 1650164, 'ticker': 'TOST', 'title': 'Toast, Inc.'},\n", " 'PTC': {'cik_str': 857005, 'ticker': 'PTC', 'title': 'PTC INC.'},\n", " 'VRSN': {'cik_str': 1014473, 'ticker': 'VRSN', 'title': 'VERISIGN INC/CA'},\n", " 'DSFIY': {'cik_str': 1989183,\n", "  'ticker': 'DSFI<PERSON>',\n", "  'title': 'DSM-Firmenich AG/ADR'},\n", " 'TYL': {'cik_str': 860731,\n", "  'ticker': 'TYL',\n", "  'title': 'TYLER TECHNOLOGIES INC'},\n", " 'TU': {'cik_str': 868675, 'ticker': 'TU', 'title': 'TELUS CORP'},\n", " 'MKL': {'cik_str': 1096343, 'ticker': 'MKL', 'title': 'MARKEL GROUP INC.'},\n", " 'TTD': {'cik_str': 1671933, 'ticker': 'TTD', 'title': 'Trade Desk, Inc.'},\n", " 'AFRM': {'cik_str': 1820953,\n", "  'ticker': 'AFRM',\n", "  'title': 'Affirm Holdings, Inc.'},\n", " 'DOV': {'cik_str': 29905, 'ticker': 'DOV', 'title': 'DOVER Corp'},\n", " 'FCNCA': {'cik_str': 798941,\n", "  'ticker': 'FCNCA',\n", "  'title': 'FIRST CITIZENS BANCSHARES INC /DE/'},\n", " 'PINS': {'cik_str': 1506293, 'ticker': 'PINS', 'title': 'PINTEREST, INC.'},\n", " 'FIX': {'cik_str': 1035983,\n", "  'ticker': 'FIX',\n", "  'title': 'COMFORT SYSTEMS USA INC'},\n", " 'HBAN': {'cik_str': 49196,\n", "  'ticker': 'HBAN',\n", "  'title': 'HUN<PERSON>NGTO<PERSON> BANCSHARES INC /MD/'},\n", " 'FUTU': {'cik_str': 1754581, 'ticker': 'FUTU', 'title': 'Futu Holdings Ltd'},\n", " 'NTRS': {'cik_str': 73124, 'ticker': 'NTRS', 'title': 'NORTHERN TRUST CORP'},\n", " 'ES': {'cik_str': 72741, 'ticker': 'ES', 'title': 'EVERSOURCE ENERGY'},\n", " 'TPG': {'cik_str': 1880661, 'ticker': 'TPG', 'title': 'TPG Inc.'},\n", " 'SHG': {'cik_str': 1263043,\n", "  'ticker': 'SHG',\n", "  'title': 'SHINHAN FINANCIAL GROUP CO LTD'},\n", " 'STE': {'cik_str': 1757898, 'ticker': 'STE', 'title': 'STERIS plc'},\n", " 'CINF': {'cik_str': 20286,\n", "  'ticker': 'CINF',\n", "  'title': 'CINCINNATI FINANCIAL CORP'},\n", " 'DRI': {'cik_str': 940944,\n", "  'ticker': 'DRI',\n", "  'title': 'DARDEN RESTAURANTS INC'},\n", " 'TROW': {'cik_str': 1113169,\n", "  'ticker': 'TROW',\n", "  'title': 'PRICE T ROWE GROUP INC'},\n", " 'LI': {'cik_str': 1791706, 'ticker': 'LI', 'title': 'Li Auto Inc.'},\n", " 'ULTA': {'cik_str': 1403568, 'ticker': 'ULTA', 'title': 'Ulta Beauty, Inc.'},\n", " 'DLTR': {'cik_str': 935703, 'ticker': 'DLTR', 'title': 'DOLLAR TREE, INC.'},\n", " 'BCE': {'cik_str': 718940, 'ticker': 'BCE', 'title': 'BCE INC'},\n", " 'CG': {'cik_str': 1527166, 'ticker': 'CG', 'title': 'Carlyle Group Inc.'},\n", " 'LULU': {'cik_str': 1397187,\n", "  'ticker': 'LULU',\n", "  'title': 'lululemon athletica inc.'},\n", " 'JBL': {'cik_str': 898293, 'ticker': 'JBL', 'title': 'JABIL INC'},\n", " 'HUBB': {'cik_str': 48898, 'ticker': 'HUBB', 'title': 'HUBBELL INC'},\n", " 'SBAC': {'cik_str': 1034054,\n", "  'ticker': 'SBAC',\n", "  'title': 'SBA COMMUNICATIONS CORP'},\n", " 'YAHOY': {'cik_str': 1446437,\n", "  'ticker': 'YAHOY',\n", "  'title': 'Yahoo! Japan Corp'},\n", " 'RF': {'cik_str': 1281761, 'ticker': 'RF', 'title': 'REGIONS FINANCIAL CORP'},\n", " 'KGC': {'cik_str': 701818, 'ticker': 'KGC', 'title': 'KINROSS GOLD CORP'},\n", " 'STM': {'cik_str': 932787,\n", "  'ticker': 'STM',\n", "  'title': 'STMicroelectronics N.V.'},\n", " 'LDOS': {'cik_str': 1336920,\n", "  'ticker': 'LDOS',\n", "  'title': 'Leidos Holdings, Inc.'},\n", " 'SW': {'cik_str': 2005951, 'ticker': 'SW', 'title': 'Smurfit Westrock plc'},\n", " 'PHYS': {'cik_str': 1477049,\n", "  'ticker': 'PHYS',\n", "  'title': 'S<PERSON>rott Physical Gold Trust'},\n", " 'NJDCY': {'cik_str': 1158967, 'ticker': 'NJDCY', 'title': 'NIDEC CORP'},\n", " 'PUBGY': {'cik_str': 1050952,\n", "  'ticker': 'PUBGY',\n", "  'title': 'PUBLICIS GROUPE SA'},\n", " 'NVR': {'cik_str': 906163, 'ticker': 'NVR', 'title': 'NVR INC'},\n", " 'EXE': {'cik_str': 895126, 'ticker': 'EXE', 'title': 'EXPAND ENERGY Corp'},\n", " 'CHD': {'cik_str': 313927,\n", "  'ticker': 'CHD',\n", "  'title': 'CHURCH & DWIGHT CO INC /DE/'},\n", " 'LH': {'cik_str': 920148, 'ticker': 'LH', 'title': 'LABCORP HOLDINGS INC.'},\n", " 'HUBS': {'cik_str': 1404655, 'ticker': 'HUBS', 'title': 'HUBSPOT INC'},\n", " 'CLS': {'cik_str': 1030894, 'ticker': 'CLS', 'title': 'CELESTICA INC'},\n", " 'CPAY': {'cik_str': 1175454, 'ticker': 'CPAY', 'title': 'CORPAY, INC.'},\n", " 'RSHGY': {'cik_str': 1447391,\n", "  'ticker': 'RSHGY',\n", "  'title': 'RESONA HOLDINGS INC'},\n", " 'NOK': {'cik_str': 924613, 'ticker': 'NOK', 'title': 'NOKIA CORP'},\n", " 'PODD': {'cik_str': 1145197, 'ticker': 'PODD', 'title': 'INSULET CORP'},\n", " 'TELNY': {'cik_str': 1126113, 'ticker': 'TELNY', 'title': 'TELENOR ASA'},\n", " 'NTAP': {'cik_str': 1002047, 'ticker': 'NTAP', 'title': 'NetApp, Inc.'},\n", " 'DKNG': {'cik_str': 1883685, 'ticker': 'DKNG', 'title': 'DraftKings Inc.'},\n", " 'BEKE': {'cik_str': 1809587, 'ticker': 'BEKE', 'title': 'KE Holdings Inc.'},\n", " 'ZM': {'cik_str': 1585521,\n", "  'ticker': 'ZM',\n", "  'title': 'Zoom Communications, Inc.'},\n", " 'RBA': {'cik_str': 1046102, 'ticker': 'RBA', 'title': 'RB GLOBAL INC.'},\n", " 'CMS': {'cik_str': 811156, 'ticker': 'CMS', 'title': 'CMS ENERGY CORP'},\n", " 'CDW': {'cik_str': 1402057, 'ticker': 'CDW', 'title': 'CDW Corp'},\n", " 'ASX': {'cik_str': 1122411,\n", "  'ticker': 'ASX',\n", "  'title': 'ASE Technology Holding Co., Ltd.'},\n", " 'EIX': {'cik_str': 827052, 'ticker': 'EIX', 'title': 'EDISON INTERNATIONAL'},\n", " 'NTRA': {'cik_str': 1604821, 'ticker': 'NTRA', 'title': 'Natera, Inc.'},\n", " 'SSNC': {'cik_str': 1402436,\n", "  'ticker': 'SSNC',\n", "  'title': 'SS&C Technologies Holdings Inc'},\n", " 'DVN': {'cik_str': 1090012, 'ticker': 'DVN', 'title': 'DEVON ENERGY CORP/DE'},\n", " 'ESLT': {'cik_str': 1027664, 'ticker': 'ESLT', 'title': 'ELBIT SYSTEMS LTD'},\n", " 'NMR': {'cik_str': 1163653, 'ticker': 'NMR', 'title': 'NOMURA HOLDINGS INC'},\n", " 'RPRX': {'cik_str': 1802768, 'ticker': 'RPRX', 'title': 'Royalty Pharma plc'},\n", " 'PBA': {'cik_str': 1546066,\n", "  'ticker': 'PBA',\n", "  'title': 'PEMBINA PIPELINE CORP'},\n", " 'GPN': {'cik_str': 1123360, 'ticker': 'GPN', 'title': 'GLOBAL PAYMENTS INC'},\n", " 'CFG': {'cik_str': 759944,\n", "  'ticker': 'CFG',\n", "  'title': 'CITIZENS FINANCIAL GROUP INC/RI'},\n", " 'LII': {'cik_str': 1069202,\n", "  'ticker': 'LII',\n", "  'title': 'LENNOX INTERNATIONAL INC'},\n", " 'AS': {'cik_str': 1988894, 'ticker': 'AS', 'title': 'Amer Sports, Inc.'},\n", " 'CYBR': {'cik_str': 1598110,\n", "  'ticker': 'CYBR',\n", "  'title': 'CyberArk Software Ltd.'},\n", " 'TPL': {'cik_str': 1811074,\n", "  'ticker': 'TPL',\n", "  'title': 'Texas Pacific Land Corp'},\n", " 'ON': {'cik_str': 1097864, 'ticker': 'ON', 'title': 'ON SEMICONDUCTOR CORP'},\n", " 'GIB': {'cik_str': 1061574, 'ticker': 'GIB', 'title': 'CGI INC'},\n", " 'GRAB': {'cik_str': 1855612, 'ticker': 'GRAB', 'title': 'Grab Holdings Ltd'},\n", " 'CKHGF': {'cik_str': 1469059,\n", "  'ticker': 'CKHGF',\n", "  'title': 'Capitec Bank Holdings Ltd / ADR'},\n", " 'RKLB': {'cik_str': 1819994, 'ticker': 'RKLB', 'title': 'Rocket Lab Corp'},\n", " 'TLK': {'cik_str': 1001807,\n", "  'ticker': 'TLK',\n", "  'title': 'PERUSAHAAN PERSEROAN PERSERO PT TELEKOMUNIKASI INDONESIA TBK'},\n", " 'ZG': {'cik_str': 1617640, 'ticker': 'ZG', 'title': 'ZILLOW GROUP, INC.'},\n", " 'PMDIY': {'cik_str': 2013003,\n", "  'ticker': 'PM<PERSON><PERSON>',\n", "  'title': 'Pro Medicus Ltd./ADR'},\n", " 'TPR': {'cik_str': 1116132, 'ticker': 'TPR', 'title': 'TAPESTRY, INC.'},\n", " 'TEVA': {'cik_str': 818686,\n", "  'ticker': 'TEVA',\n", "  'title': 'TE<PERSON> PHARMACEUTICAL INDUSTRIES LTD'},\n", " 'ZBH': {'cik_str': 1136869,\n", "  'ticker': 'ZBH',\n", "  'title': 'ZIMMER BIOMET HOLDINGS, INC.'},\n", " 'CRDO': {'cik_str': 1807794,\n", "  'ticker': 'CRDO',\n", "  'title': 'Credo Technology Group Holding Ltd'},\n", " 'KEY': {'cik_str': 91576, 'ticker': 'KEY', 'title': 'KEYCORP /NEW/'},\n", " 'AMCR': {'cik_str': 1748790, 'ticker': 'AMCR', 'title': 'Amcor plc'},\n", " 'AER': {'cik_str': 1378789, 'ticker': 'AER', 'title': 'AerCap Holdings N.V.'},\n", " 'NI': {'cik_str': 1111711, 'ticker': 'NI', 'title': 'NISOURCE INC.'},\n", " 'DGX': {'cik_str': 1022079,\n", "  'ticker': 'DGX',\n", "  'title': 'QUEST DIAGNOSTICS INC'},\n", " 'TSN': {'cik_str': 100493, 'ticker': 'TSN', 'title': 'TYSON FOODS, INC.'},\n", " 'TRMB': {'cik_str': 864749, 'ticker': 'TRMB', 'title': 'TRIMBLE INC.'},\n", " 'GDDY': {'cik_str': 1609711, 'ticker': 'GDDY', 'title': 'GoDaddy Inc.'},\n", " 'CHKP': {'cik_str': 1015922,\n", "  'ticker': 'CHKP',\n", "  'title': 'CHEC<PERSON> POINT SOFTWARE TECHNOLOGIES LTD'},\n", " 'VIV': {'cik_str': 1066119,\n", "  'ticker': 'VIV',\n", "  'title': 'TELEFONICA BRASIL S.A.'},\n", " 'L': {'cik_str': 60086, 'ticker': 'L', 'title': 'LOEWS CORP'},\n", " 'SMMT': {'cik_str': 1599298,\n", "  'ticker': 'SMMT',\n", "  'title': 'Summit Therapeutics Inc.'},\n", " 'BIIB': {'cik_str': 875045, 'ticker': 'BIIB', 'title': 'BIOGEN INC.'},\n", " 'BAP': {'cik_str': 1001290, 'ticker': 'BAP', 'title': 'CREDICORP LTD'},\n", " 'HKHHY': {'cik_str': 1142231,\n", "  'ticker': 'HKHHY',\n", "  'title': 'HEINEKEN HOLDING N V'},\n", " 'GEN': {'cik_str': 849399, 'ticker': 'GEN', 'title': 'Gen Digital Inc.'},\n", " 'FSLR': {'cik_str': 1274494, 'ticker': 'FSLR', 'title': 'FIRST SOLAR, INC.'},\n", " 'ERIE': {'cik_str': 922621, 'ticker': 'ERIE', 'title': 'ERIE INDEMNITY CO'},\n", " 'RCI': {'cik_str': 733099,\n", "  'ticker': 'RCI',\n", "  'title': 'ROGERS COMMUNICATIONS INC'},\n", " 'PSTG': {'cik_str': 1474432, 'ticker': 'PSTG', 'title': 'Pure Storage, Inc.'},\n", " 'GPC': {'cik_str': 40987, 'ticker': 'GPC', 'title': 'GENUINE PARTS CO'},\n", " 'WY': {'cik_str': 106535, 'ticker': 'WY', 'title': 'WEYERHAEUSER CO'},\n", " 'CASY': {'cik_str': 726958,\n", "  'ticker': 'CASY',\n", "  'title': 'CASEYS GENERAL STORES INC'},\n", " 'MKC': {'cik_str': 63754, 'ticker': 'MKC', 'title': 'MCCORMICK & CO INC'},\n", " 'TS': {'cik_str': 1190723, 'ticker': 'TS', 'title': 'TENARIS SA'},\n", " 'STLD': {'cik_str': 1022671, 'ticker': 'STLD', 'title': 'STEEL DYNAMICS INC'},\n", " 'EBR': {'cik_str': 1439124,\n", "  'ticker': 'EBR',\n", "  'title': 'BRAZILIAN ELECTRIC POWER CO'},\n", " 'CW': {'cik_str': 26324, 'ticker': 'CW', 'title': 'CURTISS WRIGHT CORP'},\n", " 'INVH': {'cik_str': 1687229,\n", "  'ticker': 'INVH',\n", "  'title': 'Invitation Homes Inc.'},\n", " 'BNT': {'cik_str': 1837429,\n", "  'ticker': 'BNT',\n", "  'title': 'Brookfield Wealth Solutions Ltd.'},\n", " 'FLEX': {'cik_str': 866374, 'ticker': 'FLEX', 'title': 'FLEX LTD.'},\n", " 'XPEV': {'cik_str': 1810997, 'ticker': 'XPEV', 'title': 'XPENG INC.'},\n", " 'IT': {'cik_str': 749251, 'ticker': 'IT', 'title': 'GARTNER INC'},\n", " 'NTNX': {'cik_str': 1618732, 'ticker': 'NTNX', 'title': 'Nutanix, Inc.'},\n", " 'GFS': {'cik_str': 1709048, 'ticker': 'G<PERSON>', 'title': 'GLOBALFOUNDRIES Inc.'},\n", " 'IOT': {'cik_str': 1642896, 'ticker': 'IOT', 'title': 'Samsara Inc.'},\n", " 'IHG': {'cik_str': 858446,\n", "  'ticker': 'IHG',\n", "  'title': 'INTERCONTINENTAL HOTELS GROUP PLC /NEW/'},\n", " 'KSPI': {'cik_str': 1985487,\n", "  'ticker': 'KSPI',\n", "  'title': 'Joint Stock Co Kaspi.kz'},\n", " 'CRBG': {'cik_str': 1889539,\n", "  'ticker': 'CRBG',\n", "  'title': 'Corebridge Financial, Inc.'},\n", " 'CTRA': {'cik_str': 858470, 'ticker': 'CTRA', 'title': 'Coterra Energy Inc.'},\n", " 'FFIV': {'cik_str': 1048695, 'ticker': 'FFIV', 'title': 'F5, INC.'},\n", " 'GFL': {'cik_str': 1780232,\n", "  'ticker': 'GFL',\n", "  'title': 'GFL Environmental Inc.'},\n", " 'KOF': {'cik_str': 910631,\n", "  'ticker': 'KOF',\n", "  'title': 'COCA COLA FEMSA SAB DE CV'},\n", " 'HAL': {'cik_str': 45012, 'ticker': 'HAL', 'title': 'HALLIBURTON CO'},\n", " 'DKS': {'cik_str': 1089063,\n", "  'ticker': 'D<PERSON>',\n", "  'title': \"DICK'S SPORTING GOODS, INC.\"},\n", " 'EDPFY': {'cik_str': 1039610,\n", "  'ticker': 'EDPFY',\n", "  'title': 'EDP ENERGIAS DE PORTUGAL SA'},\n", " 'EC': {'cik_str': 1444406, 'ticker': 'EC', 'title': 'ECOPETROL S.A.'},\n", " 'BURL': {'cik_str': 1579298,\n", "  'ticker': 'BURL',\n", "  'title': 'Burlington Stores, Inc.'},\n", " 'TER': {'cik_str': 97210, 'ticker': 'TER', 'title': 'TERADYNE, INC'},\n", " 'PKG': {'cik_str': 75677,\n", "  'ticker': 'PKG',\n", "  'title': 'PACKAGING CORP OF AMERICA'},\n", " 'KEP': {'cik_str': 887225,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'KOREA ELECTRIC POWER CORP'},\n", " 'RL': {'cik_str': 1037038, 'ticker': 'RL', 'title': 'RALPH LAUREN CORP'},\n", " 'J': {'cik_str': 52988, 'ticker': 'J', 'title': 'JACOBS SOLUTIONS INC.'},\n", " 'OMVKY': {'cik_str': 882602,\n", "  'ticker': 'OMVKY',\n", "  'title': 'OMV AKTIENGESELLSCHAFT /FI'},\n", " 'ESS': {'cik_str': 920522,\n", "  'ticker': 'ESS',\n", "  'title': 'ESSEX PROPERTY TRUST, INC.'},\n", " 'WST': {'cik_str': 105770,\n", "  'ticker': 'WST',\n", "  'title': 'WEST PHARMACEUTICAL SERVICES INC'},\n", " 'NWSA': {'cik_str': 1564708, 'ticker': 'NWSA', 'title': 'NEWS CORP'},\n", " 'TRU': {'cik_str': 1552033, 'ticker': 'TRU', 'title': 'TransUnion'},\n", " 'GWRE': {'cik_str': 1528396,\n", "  'ticker': 'GWRE',\n", "  'title': 'Guidewire Software, Inc.'},\n", " 'WAT': {'cik_str': 1000697, 'ticker': 'WAT', 'title': 'WATERS CORP /DE/'},\n", " 'PNR': {'cik_str': 77360, 'ticker': 'PNR', 'title': 'PENTAIR plc'},\n", " 'TLN': {'cik_str': 1622536, 'ticker': 'TLN', 'title': 'Talen Energy Corp'},\n", " 'USFD': {'cik_str': 1665918,\n", "  'ticker': 'USFD',\n", "  'title': 'US Foods Holding Corp.'},\n", " 'PFG': {'cik_str': 1126328,\n", "  'ticker': 'PFG',\n", "  'title': 'PRINCIPAL FINANCIAL GROUP INC'},\n", " 'ASTS': {'cik_str': 1780312,\n", "  'ticker': 'ASTS',\n", "  'title': 'AST SpaceMobile, Inc.'},\n", " 'UMC': {'cik_str': 1033767,\n", "  'ticker': 'UMC',\n", "  'title': 'UNITED MICROELECTRONICS CORP'},\n", " 'WSO': {'cik_str': 105016, 'ticker': 'WSO', 'title': 'WATSCO INC'},\n", " 'LYB': {'cik_str': 1489393,\n", "  'ticker': 'LYB',\n", "  'title': 'LyondellBasell Industries N.V.'},\n", " 'SNA': {'cik_str': 91440, 'ticker': 'SNA', 'title': 'Snap-on Inc'},\n", " 'JHX': {'cik_str': 1159152,\n", "  'ticker': 'JHX',\n", "  'title': 'James Hardie Industries plc'},\n", " 'SN': {'cik_str': 1957132, 'ticker': 'SN', 'title': 'SharkNinja, Inc.'},\n", " 'WMG': {'cik_str': 1319161,\n", "  'ticker': 'WMG',\n", "  'title': 'Warner Music Group Corp.'},\n", " 'INCY': {'cik_str': 879169, 'ticker': 'INCY', 'title': 'INCYTE CORP'},\n", " 'MDB': {'cik_str': 1441816, 'ticker': 'MD<PERSON>', 'title': 'MongoDB, Inc.'},\n", " 'IFF': {'cik_str': 51253,\n", "  'ticker': 'IFF',\n", "  'title': 'INTERNATIONAL FLAVORS & FRAGRANCES INC'},\n", " 'MAA': {'cik_str': 912595,\n", "  'ticker': 'MAA',\n", "  'title': 'MID AMERICA APARTMENT COMMUNITIES INC.'},\n", " 'EVRG': {'cik_str': 1711269, 'ticker': 'EVRG', 'title': 'Evergy, Inc.'},\n", " 'CSL': {'cik_str': 790051,\n", "  'ticker': 'CSL',\n", "  'title': 'CARLISLE COMPANIES INC'},\n", " 'LNT': {'cik_str': 352541, 'ticker': 'LNT', 'title': 'ALLIANT ENERGY CORP'},\n", " 'PKX': {'cik_str': 889132, 'ticker': 'PKX', 'title': 'POSCO HOLDINGS INC.'},\n", " 'ZBRA': {'cik_str': 877212,\n", "  'ticker': 'ZBRA',\n", "  'title': 'ZEBRA TECHNOLOGIES CORP'},\n", " 'SUI': {'cik_str': 912593, 'ticker': 'SUI', 'title': 'SUN COMMUNITIES INC'},\n", " 'DOW': {'cik_str': 1751788, 'ticker': 'DOW', 'title': 'DOW INC.'},\n", " 'SGI': {'cik_str': 1206264,\n", "  'ticker': '<PERSON><PERSON>',\n", "  'title': 'SOMNIGROUP INTERNATIONAL INC.'},\n", " 'RBRK': {'cik_str': 1943896, 'ticker': 'RBRK', 'title': 'Rubrik, Inc.'},\n", " 'YUMC': {'cik_str': 1673358,\n", "  'ticker': 'YUMC',\n", "  'title': 'Yum China Holdings, Inc.'},\n", " 'NBIS': {'cik_str': 1513845, 'ticker': 'NBIS', 'title': 'Nebius Group N.V.'},\n", " 'FNF': {'cik_str': 1331875,\n", "  'ticker': 'FNF',\n", "  'title': 'Fidelity National Financial, Inc.'},\n", " 'EXPD': {'cik_str': 746515,\n", "  'ticker': 'EXPD',\n", "  'title': 'EXPEDITORS INTERNATIONAL OF WASHINGTON INC'},\n", " 'EQH': {'cik_str': 1333986,\n", "  'ticker': 'EQH',\n", "  'title': 'Equitable Holdings, Inc.'},\n", " 'NPXYY': {'cik_str': 2019040,\n", "  'ticker': 'NPXYY',\n", "  'title': 'Nippon Sanso Holdings Corporation/ADR'},\n", " 'BG': {'cik_str': 1996862, 'ticker': 'BG', 'title': 'Bunge Global SA'},\n", " 'FTV': {'cik_str': 1659166, 'ticker': 'FTV', 'title': 'Fortive Corp'},\n", " 'CHWY': {'cik_str': 1766502, 'ticker': 'CHWY', 'title': 'Chewy, Inc.'},\n", " 'APTV': {'cik_str': 1521332, 'ticker': 'APTV', 'title': 'Aptiv PLC'},\n", " 'LUV': {'cik_str': 92380, 'ticker': 'LUV', 'title': 'SOUTHWEST AIRLINES CO'},\n", " 'U': {'cik_str': 1810806, 'ticker': 'U', 'title': 'Unity Software Inc.'},\n", " 'ARCC': {'cik_str': 1287750, 'ticker': 'ARCC', 'title': 'ARES CAPITAL CORP'},\n", " 'SNN': {'cik_str': 845982, 'ticker': 'SNN', 'title': 'SMITH & NEPHEW PLC'},\n", " 'BEP': {'cik_str': 1533232,\n", "  'ticker': 'BEP',\n", "  'title': 'Brookfield Renewable Partners L.P.'},\n", " 'RPM': {'cik_str': 110621,\n", "  'ticker': 'RPM',\n", "  'title': 'RPM INTERNATIONAL INC/DE/'},\n", " 'ZTO': {'cik_str': 1677250,\n", "  'ticker': 'Z<PERSON>',\n", "  'title': 'ZTO Express (Cayman) Inc.'},\n", " 'ACM': {'cik_str': 868857, 'ticker': 'ACM', 'title': 'AECOM'},\n", " 'BWXT': {'cik_str': 1486957,\n", "  'ticker': 'BW<PERSON>',\n", "  'title': 'BWX Technologies, Inc.'},\n", " 'RYAN': {'cik_str': 1849253,\n", "  'ticker': '<PERSON><PERSON><PERSON>',\n", "  'title': 'RYAN SPECIALTY HOLDINGS, INC.'},\n", " 'ILMN': {'cik_str': 1110803, 'ticker': 'ILMN', 'title': 'ILLUMINA, INC.'},\n", " 'TECK': {'cik_str': 886986, 'ticker': 'TECK', 'title': 'TECK RESOURCES LTD'},\n", " 'VDMCY': {'cik_str': 1468608,\n", "  'ticker': 'VDMCY',\n", "  'title': 'Vodacom Group Ltd / ADR'},\n", " 'TKO': {'cik_str': 1973266,\n", "  'ticker': 'TKO',\n", "  'title': 'TKO Group Holdings, Inc.'},\n", " 'PFGC': {'cik_str': 1618673,\n", "  'ticker': 'PFGC',\n", "  'title': 'Performance Food Group Co'},\n", " 'BSY': {'cik_str': 1031308, 'ticker': 'BSY', 'title': 'BENTLEY SYSTEMS INC'},\n", " 'HRL': {'cik_str': 48465, 'ticker': 'HRL', 'title': 'HORMEL FOODS CORP /DE/'},\n", " 'OKTA': {'cik_str': 1660134, 'ticker': 'OKTA', 'title': 'Okta, Inc.'},\n", " 'TWLO': {'cik_str': 1447669, 'ticker': 'TWLO', 'title': 'TWILIO INC'},\n", " 'DUOL': {'cik_str': 1562088, 'ticker': 'DUOL', 'title': 'Duolingo, Inc.'},\n", " 'MAS': {'cik_str': 62996, 'ticker': 'MAS', 'title': 'MASCO CORP /DE/'},\n", " 'DECK': {'cik_str': 910521,\n", "  'ticker': 'DECK',\n", "  'title': 'DECKERS OUTDOOR CORP'},\n", " 'BLDR': {'cik_str': 1316835,\n", "  'ticker': 'BLDR',\n", "  'title': 'Builders FirstSource, Inc.'},\n", " 'BBY': {'cik_str': 764478, 'ticker': 'BBY', 'title': 'BEST BUY CO INC'},\n", " 'DPZ': {'cik_str': 1286681, 'ticker': 'DPZ', 'title': 'DOMINOS PIZZA INC'},\n", " 'RS': {'cik_str': 861884, 'ticker': 'RS', 'title': 'RELIANCE, INC.'},\n", " 'THC': {'cik_str': 70318, 'ticker': 'THC', 'title': 'TENET HEALTHCARE CORP'},\n", " 'CNH': {'cik_str': 1567094, 'ticker': 'CNH', 'title': 'CNH Industrial N.V.'},\n", " 'HOLX': {'cik_str': 859737, 'ticker': 'HOLX', 'title': 'HOLOGIC INC'},\n", " 'SBS': {'cik_str': 1170858,\n", "  'ticker': 'SBS',\n", "  'title': 'COMPANHIA DE SANEAMENTO BASICO DO ESTADO DE SAO PAULO-SABESP'},\n", " 'XPO': {'cik_str': 1166003, 'ticker': 'XPO', 'title': 'XPO, Inc.'},\n", " 'CLX': {'cik_str': 21076, 'ticker': 'CLX', 'title': 'CLOROX CO /DE/'},\n", " 'JOBY': {'cik_str': 1819848,\n", "  'ticker': 'JOB<PERSON>',\n", "  'title': 'Joby Aviation, Inc.'},\n", " 'ARNNY': {'cik_str': 1719117,\n", "  'ticker': 'ARNNY',\n", "  'title': 'ASR Nederland N.V./ADR'},\n", " 'FTAI': {'cik_str': 1590364, 'ticker': 'FTAI', 'title': 'FTAI Aviation Ltd.'},\n", " 'BCH': {'cik_str': 1161125, 'ticker': 'BCH', 'title': 'BANK OF CHILE'},\n", " 'RIVN': {'cik_str': 1874178,\n", "  'ticker': 'RIVN',\n", "  'title': 'Rivian Automotive, Inc. / DE'},\n", " 'WWD': {'cik_str': 108312, 'ticker': 'WWD', 'title': 'Woodward, Inc.'},\n", " 'WES': {'cik_str': 1423902,\n", "  'ticker': 'WES',\n", "  'title': 'Western Midstream Partners, LP'},\n", " 'OMC': {'cik_str': 29989, 'ticker': 'OMC', 'title': 'OMNICOM GROUP INC.'},\n", " 'AMH': {'cik_str': 1562401,\n", "  'ticker': 'AMH',\n", "  'title': 'American Homes 4 Rent'},\n", " 'APG': {'cik_str': 1796209, 'ticker': 'APG', 'title': 'APi Group Corp'},\n", " 'COO': {'cik_str': 711404,\n", "  'ticker': 'COO',\n", "  'title': 'COOPER COMPANIES, INC.'},\n", " 'BALL': {'cik_str': 9389, 'ticker': 'BALL', 'title': 'BALL Corp'},\n", " 'FMS': {'cik_str': 1333141,\n", "  'ticker': 'FMS',\n", "  'title': 'Fresenius Medical Care AG'},\n", " 'FTI': {'cik_str': 1681459, 'ticker': 'FTI', 'title': 'TechnipFMC plc'},\n", " 'CELH': {'cik_str': 1341766,\n", "  'ticker': 'CELH',\n", "  'title': 'Celsius Holdings, Inc.'},\n", " 'ONON': {'cik_str': 1858985, 'ticker': 'ONON', 'title': 'On Holding AG'},\n", " 'LOGI': {'cik_str': 1032975,\n", "  'ticker': 'LO<PERSON>',\n", "  'title': 'LOGITECH INTERNATIONAL S.A.'},\n", " 'NVT': {'cik_str': 1720635, 'ticker': 'NVT', 'title': 'nVent Electric plc'},\n", " 'ALLE': {'cik_str': 1579241, 'ticker': 'ALLE', 'title': 'Allegion plc'},\n", " 'UDR': {'cik_str': 74208, 'ticker': 'UDR', 'title': 'UDR, Inc.'},\n", " 'CHRW': {'cik_str': 1043277,\n", "  'ticker': 'CHRW',\n", "  'title': '<PERSON><PERSON> <PERSON><PERSON>ON WORLDWIDE, INC.'},\n", " 'KIM': {'cik_str': 879101, 'ticker': 'KIM', 'title': 'KIMCO REALTY CORP'},\n", " 'TXT': {'cik_str': 217346, 'ticker': 'TXT', 'title': 'TEXTRON INC'},\n", " 'BF-A': {'cik_str': 14693, 'ticker': 'BF-A', 'title': 'BROWN FORMAN CORP'},\n", " 'WPC': {'cik_str': 1025378, 'ticker': 'WPC', 'title': 'W. P. Carey Inc.'},\n", " 'DT': {'cik_str': 1773383, 'ticker': 'DT', 'title': 'Dynatrace, Inc.'},\n", " 'EMA': {'cik_str': 1127248, 'ticker': 'EMA', 'title': 'EMERA INC'},\n", " 'FDS': {'cik_str': 1013237,\n", "  'ticker': 'FDS',\n", "  'title': 'FACTSET RESEARCH SYSTEMS INC'},\n", " 'EWBC': {'cik_str': 1069157,\n", "  'ticker': 'EWBC',\n", "  'title': 'EAST WEST BANCORP INC'},\n", " 'MTZ': {'cik_str': 15615, 'ticker': 'MTZ', 'title': 'MASTEC INC'},\n", " 'COHR': {'cik_str': 820318, 'ticker': 'COHR', 'title': 'COHERENT CORP.'},\n", " 'GGG': {'cik_str': 42888, 'ticker': 'GGG', 'title': 'GRACO INC'},\n", " 'EG': {'cik_str': 1095073, 'ticker': 'EG', 'title': 'EVEREST GROUP, LTD.'},\n", " 'GMAB': {'cik_str': 1434265, 'ticker': 'GMAB', 'title': 'GENMAB A/S'},\n", " 'QXO': {'cik_str': 1236275, 'ticker': 'QXO', 'title': 'QXO, Inc.'},\n", " 'UTHR': {'cik_str': 1082554,\n", "  'ticker': 'UTHR',\n", "  'title': 'UNITED THERAPEUTICS Corp'},\n", " 'SFM': {'cik_str': 1575515,\n", "  'ticker': 'SFM',\n", "  'title': 'Sprouts Farmers Market, Inc.'},\n", " 'DOCU': {'cik_str': 1261333, 'ticker': 'DOCU', 'title': 'DOCUSIGN, INC.'},\n", " 'JLL': {'cik_str': 1037976,\n", "  'ticker': 'J<PERSON>',\n", "  'title': 'JONES LANG LASALLE INC'},\n", " 'JBHT': {'cik_str': 728535,\n", "  'ticker': 'JBHT',\n", "  'title': 'HUNT J B TRANSPORT SERVICES INC'},\n", " 'SOAGY': {'cik_str': 1715801, 'ticker': 'SOAGY', 'title': 'Sartorius AG/ADR'},\n", " 'BIP': {'cik_str': 1406234,\n", "  'ticker': 'BIP',\n", "  'title': 'Brookfield Infrastructure Partners L.P.'},\n", " 'AVY': {'cik_str': 8818, 'ticker': 'AVY', 'title': 'Avery Dennison Corp'},\n", " 'CF': {'cik_str': 1324404,\n", "  'ticker': 'CF',\n", "  'title': 'CF Industries Holdings, Inc.'},\n", " 'H': {'cik_str': 1468174, 'ticker': 'H', 'title': 'Hyatt Hotels Corp'},\n", " 'HLI': {'cik_str': 1302215, 'ticker': 'HLI', 'title': 'HOULIHAN LOKEY, INC.'},\n", " 'ICLR': {'cik_str': 1060955, 'ticker': 'ICLR', 'title': 'ICON PLC'},\n", " 'ULS': {'cik_str': 1901440, 'ticker': 'ULS', 'title': 'UL Solutions Inc.'},\n", " 'BJ': {'cik_str': 1531152,\n", "  'ticker': 'B<PERSON>',\n", "  'title': \"BJ's Wholesale Club Holdings, Inc.\"},\n", " 'MP': {'cik_str': 1801368,\n", "  'ticker': 'MP',\n", "  'title': 'MP Materials Corp. / DE'},\n", " 'WF': {'cik_str': 1264136,\n", "  'ticker': 'WF',\n", "  'title': 'WOORI FINANCIAL GROUP INC.'},\n", " 'LECO': {'cik_str': 59527,\n", "  'ticker': 'LECO',\n", "  'title': 'LINCOLN ELECTRIC HOLDINGS INC'},\n", " 'NLY': {'cik_str': 1043219,\n", "  'ticker': 'NLY',\n", "  'title': 'ANNALY CAPITAL MANAGEMENT INC'},\n", " 'CLH': {'cik_str': 822818, 'ticker': 'CLH', 'title': 'CLEAN HARBORS INC'},\n", " 'BEN': {'cik_str': 38777, 'ticker': 'BEN', 'title': 'FRANKLIN RESOURCES INC'},\n", " 'BAH': {'cik_str': 1443646,\n", "  'ticker': 'BAH',\n", "  'title': '<PERSON>oz Allen Hamilton Holding Corp'},\n", " 'CNC': {'cik_str': 1071739, 'ticker': 'CNC', 'title': 'CENTENE CORP'},\n", " 'LTM': {'cik_str': 1047716,\n", "  'ticker': 'LTM',\n", "  'title': 'LATAM AIRLINES GROUP S.A.'},\n", " 'MANH': {'cik_str': 1056696,\n", "  'ticker': 'MANH',\n", "  'title': 'MANHATTAN ASSOCIATES INC'},\n", " 'ITT': {'cik_str': 216228, 'ticker': 'ITT', 'title': 'ITT INC.'},\n", " 'NBIX': {'cik_str': 914475,\n", "  'ticker': 'NBIX',\n", "  'title': 'NEUROCRINE BIOSCIENCES INC'},\n", " 'REG': {'cik_str': 910606, 'ticker': 'REG', 'title': 'REGENCY CENTERS CORP'},\n", " 'GLPI': {'cik_str': 1575965,\n", "  'ticker': 'GLPI',\n", "  'title': 'Gaming & Leisure Properties, Inc.'},\n", " 'CNA': {'cik_str': 21175, 'ticker': 'CNA', 'title': 'CNA FINANCIAL CORP'},\n", " 'YPF': {'cik_str': 904851, 'ticker': 'YPF', 'title': 'YPF SOCIEDAD ANONIMA'},\n", " 'MEDP': {'cik_str': 1668397,\n", "  'ticker': 'MEDP',\n", "  'title': 'Medpace Holdings, Inc.'},\n", " 'TOL': {'cik_str': 794170, 'ticker': 'TOL', 'title': 'Toll Brothers, Inc.'},\n", " 'ROKU': {'cik_str': 1428439, 'ticker': 'ROKU', 'title': 'ROKU, INC'},\n", " 'CIEN': {'cik_str': 936395, 'ticker': 'CIEN', 'title': 'CIENA CORP'},\n", " 'OC': {'cik_str': 1370946, 'ticker': 'OC', 'title': '<PERSON> Corning'},\n", " 'RBC': {'cik_str': 1324948, 'ticker': 'RBC', 'title': 'RBC Bearings INC'},\n", " 'CNM': {'cik_str': 1856525, 'ticker': 'CNM', 'title': 'Core & Main, Inc.'},\n", " 'ARE': {'cik_str': 1035443,\n", "  'ticker': 'ARE',\n", "  'title': 'ALEXANDRIA REAL ESTATE EQUITIES, INC.'},\n", " 'JEF': {'cik_str': 96223,\n", "  'ticker': 'J<PERSON>',\n", "  'title': 'Jefferies Financial Group Inc.'},\n", " 'SOLV': {'cik_str': 1964738, 'ticker': 'SOLV', 'title': 'Solventum Corp'},\n", " 'RTO': {'cik_str': 930157,\n", "  'ticker': 'RTO',\n", "  'title': 'RENTOKIL INITIAL PLC /FI'},\n", " 'PAC': {'cik_str': 1347557,\n", "  'ticker': 'PAC',\n", "  'title': 'Pacific Airport Group'},\n", " 'FNMA': {'cik_str': 310522,\n", "  'ticker': 'FNMA',\n", "  'title': 'FEDERAL NATIONAL MORTGAGE ASSOCIATION FANNIE MAE'},\n", " 'MGA': {'cik_str': 749098,\n", "  'ticker': 'MGA',\n", "  'title': 'MAGNA INTERNATIONAL INC'},\n", " 'IEX': {'cik_str': 832101, 'ticker': 'IEX', 'title': 'IDEX CORP /DE/'},\n", " 'CIB': {'cik_str': 2058897, 'ticker': 'CIB', 'title': 'Grupo Cibest S.A.'},\n", " 'RGA': {'cik_str': 898174,\n", "  'ticker': 'RGA',\n", "  'title': 'REINSURANCE GROUP OF AMERICA INC'},\n", " 'BAX': {'cik_str': 10456,\n", "  'ticker': 'BAX',\n", "  'title': 'BAXTER INTERNATIONAL INC'},\n", " 'VTRS': {'cik_str': 1792044, 'ticker': 'VTRS', 'title': 'Viatris Inc'},\n", " 'PNDRY': {'cik_str': 1505880,\n", "  'ticker': 'PNDRY',\n", "  'title': 'Pandora A/S / ADR'},\n", " 'PAA': {'cik_str': 1070423,\n", "  'ticker': 'PAA',\n", "  'title': 'PLAINS ALL AMERICAN PIPELINE LP'},\n", " 'SNX': {'cik_str': 1177394, 'ticker': 'SNX', 'title': 'TD SYNNEX CORP'},\n", " 'COOP': {'cik_str': 933136,\n", "  'ticker': 'COOP',\n", "  'title': 'Mr. Cooper Group Inc.'},\n", " 'OHI': {'cik_str': 888491,\n", "  'ticker': 'OHI',\n", "  'title': 'OMEGA HEALTHCARE INVESTORS INC'},\n", " 'AVAV': {'cik_str': 1368622, 'ticker': 'AVAV', 'title': 'AeroVironment Inc'},\n", " 'BLD': {'cik_str': 1633931, 'ticker': 'BLD', 'title': 'TopBuild Corp'},\n", " 'CRS': {'cik_str': 17843,\n", "  'ticker': 'CRS',\n", "  'title': 'CARPENTER TECHNOLOGY CORP'},\n", " 'CX': {'cik_str': 1076378, 'ticker': 'CX', 'title': 'CEMEX SAB DE CV'},\n", " 'SUZ': {'cik_str': 909327, 'ticker': 'SUZ', 'title': '<PERSON><PERSON> S.A.'},\n", " 'PAYC': {'cik_str': 1590955,\n", "  'ticker': 'PAYC',\n", "  'title': 'Paycom Software, Inc.'},\n", " 'LAMR': {'cik_str': 1090425,\n", "  'ticker': 'LAMR',\n", "  'title': 'LAMAR ADVERTISING CO/NEW'},\n", " 'NDSN': {'cik_str': 72331, 'ticker': 'NDSN', 'title': 'NORDSON CORP'},\n", " 'POOL': {'cik_str': 945841, 'ticker': 'POOL', 'title': 'POOL CORP'},\n", " 'ELS': {'cik_str': 895417,\n", "  'ticker': 'E<PERSON>',\n", "  'title': 'EQUITY LIFESTYLE PROPERTIES INC'},\n", " 'RKUNY': {'cik_str': 1294591,\n", "  'ticker': 'RKUN<PERSON>',\n", "  'title': 'Rakuten Group, Inc.'},\n", " 'SQM': {'cik_str': 909037,\n", "  'ticker': 'SQM',\n", "  'title': 'CHEMICAL & MINING CO OF CHILE INC'},\n", " 'TEM': {'cik_str': 1717115, 'ticker': 'TEM', 'title': 'Tempus AI, Inc.'},\n", " 'PAG': {'cik_str': 1019849,\n", "  'ticker': 'PAG',\n", "  'title': '<PERSON><PERSON><PERSON><PERSON> AUTOMOTIVE GROUP, INC.'},\n", " 'STN': {'cik_str': 1131383, 'ticker': 'STN', 'title': 'STANTEC INC'},\n", " 'EVR': {'cik_str': 1360901, 'ticker': 'EVR', 'title': 'Evercore Inc.'},\n", " 'UNM': {'cik_str': 5513, 'ticker': 'UNM', 'title': 'Unum Group'},\n", " 'EHC': {'cik_str': 785161, 'ticker': 'EHC', 'title': 'Encompass Health Corp'},\n", " 'IONQ': {'cik_str': 1824920, 'ticker': 'IONQ', 'title': 'IonQ, Inc.'},\n", " 'SNAP': {'cik_str': 1564408, 'ticker': 'SNAP', 'title': 'Snap Inc'},\n", " 'ENTG': {'cik_str': 1101302, 'ticker': 'ENTG', 'title': 'ENTEGRIS INC'},\n", " 'AUR': {'cik_str': 1828108,\n", "  'ticker': 'AUR',\n", "  'title': 'Aurora Innovation, Inc.'},\n", " 'ALLY': {'cik_str': 40729, 'ticker': 'ALLY', 'title': 'Ally Financial Inc.'},\n", " 'SJM': {'cik_str': 91419, 'ticker': 'SJM', 'title': 'J M SMUCKER Co'},\n", " 'DOC': {'cik_str': 765880,\n", "  'ticker': 'DOC',\n", "  'title': 'HEALTHPEAK PROPERTIES, INC.'},\n", " 'RDY': {'cik_str': 1135951,\n", "  'ticker': 'RD<PERSON>',\n", "  'title': 'DR REDDYS LABORATORIES LTD'},\n", " 'SF': {'cik_str': 720672, 'ticker': 'SF', 'title': 'STIFEL FINANCIAL CORP'},\n", " 'DOCS': {'cik_str': 1516513, 'ticker': 'DOCS', 'title': 'Doximity, Inc.'},\n", " 'SLV': {'cik_str': 1330568, 'ticker': 'SLV', 'title': 'iShares Silver Trust'},\n", " 'KTOS': {'cik_str': 1069258,\n", "  'ticker': 'KTOS',\n", "  'title': 'KRATOS DEFENSE & SECURITY SOLUTIONS, INC.'},\n", " 'AEG': {'cik_str': 769218, 'ticker': 'AEG', 'title': 'AEGON LTD.'},\n", " 'CCK': {'cik_str': 1219601, 'ticker': 'CCK', 'title': 'CROWN HOLDINGS, INC.'},\n", " 'JKHY': {'cik_str': 779152,\n", "  'ticker': '<PERSON><PERSON>H<PERSON>',\n", "  'title': 'JACK HENRY & ASSOCIATES INC'},\n", " 'GNRC': {'cik_str': 1474735,\n", "  'ticker': 'GNRC',\n", "  'title': 'GENERAC HOLDINGS INC.'},\n", " 'ASND': {'cik_str': 1612042,\n", "  'ticker': 'ASND',\n", "  'title': '<PERSON><PERSON><PERSON> A/S'},\n", " 'SWK': {'cik_str': 93556,\n", "  'ticker': 'SW<PERSON>',\n", "  'title': 'STANLEY BLACK & DECKER, INC.'},\n", " 'CART': {'cik_str': 1579091, 'ticker': 'CART', 'title': 'Maplebear Inc.'},\n", " 'WYNN': {'cik_str': 1174922, 'ticker': 'WYNN', 'title': 'WYNN RESORTS LTD'},\n", " 'TXRH': {'cik_str': 1289460,\n", "  'ticker': 'TXRH',\n", "  'title': 'Texas Roadhouse, Inc.'},\n", " 'BXP': {'cik_str': 1037540, 'ticker': 'BXP', 'title': 'BXP, Inc.'},\n", " 'DLAKY': {'cik_str': 1049724,\n", "  'ticker': 'DLAK<PERSON>',\n", "  'title': 'DEUTSCHE LUFTHANSA A G /FI'},\n", " 'YMM': {'cik_str': 1838413,\n", "  'ticker': 'YMM',\n", "  'title': 'Full Truck Alliance Co. Ltd.'},\n", " 'RNR': {'cik_str': 913144,\n", "  'ticker': 'RNR',\n", "  'title': 'RENAISSANCERE HOLDINGS LTD'},\n", " 'BSAC': {'cik_str': 1027552,\n", "  'ticker': 'BSAC',\n", "  'title': 'BANCO SANTANDER CHILE'},\n", " 'PAAS': {'cik_str': 771992,\n", "  'ticker': 'PAAS',\n", "  'title': 'PAN AMERICAN SILVER CORP'},\n", " 'UHS': {'cik_str': 352915,\n", "  'ticker': 'UHS',\n", "  'title': 'UNIVERSAL HEALTH SERVICES INC'},\n", " 'FN': {'cik_str': 1408710, 'ticker': 'FN', 'title': '<PERSON><PERSON>rinet'},\n", " 'CPT': {'cik_str': 906345, 'ticker': 'CPT', 'title': 'CAMDEN PROPERTY TRUST'},\n", " 'PPC': {'cik_str': 802481, 'ticker': 'PPC', 'title': 'PILGRIMS PRIDE CORP'},\n", " 'SCI': {'cik_str': 89089,\n", "  'ticker': 'SCI',\n", "  'title': 'SERVICE CORP INTERNATIONAL'},\n", " 'SOTGY': {'cik_str': 1601992,\n", "  'ticker': 'SOTGY',\n", "  'title': 'Sunny Optical Technology (Group) Co Limited/ADR'},\n", " 'GTM': {'cik_str': 1794515,\n", "  'ticker': 'GTM',\n", "  'title': 'ZoomInfo Technologies Inc.'},\n", " 'HAS': {'cik_str': 46080, 'ticker': 'HAS', 'title': 'HASBRO, INC.'},\n", " 'FHN': {'cik_str': 36966, 'ticker': 'FHN', 'title': 'FIRST HORIZON CORP'},\n", " 'MBLY': {'cik_str': 1910139,\n", "  'ticker': 'MBL<PERSON>',\n", "  'title': 'Mobileye Global Inc.'},\n", " 'GL': {'cik_str': 320335, 'ticker': 'GL', 'title': 'GLOBE LIFE INC.'},\n", " 'CHYM': {'cik_str': 1795586,\n", "  'ticker': 'CHYM',\n", "  'title': 'Chime Financial, Inc.'},\n", " 'RGLD': {'cik_str': 85535, 'ticker': 'RGLD', 'title': 'ROYAL GOLD INC'},\n", " 'HST': {'cik_str': 1070750,\n", "  'ticker': 'HST',\n", "  'title': 'HOST HOTELS & RESORTS, INC.'},\n", " 'DRS': {'cik_str': 1833756, 'ticker': 'DRS', 'title': 'Leonardo DRS, Inc.'},\n", " 'AFG': {'cik_str': 1042046,\n", "  'ticker': 'AFG',\n", "  'title': 'AMERICAN FINANCIAL GROUP INC'},\n", " 'SAIL': {'cik_str': 2030781, 'ticker': 'SAIL', 'title': 'SailPoint, Inc.'},\n", " 'SWKS': {'cik_str': 4127,\n", "  'ticker': '<PERSON>W<PERSON>',\n", "  'title': '<PERSON><PERSON><PERSON><PERSON><PERSON> SOLUTIONS, INC.'},\n", " 'BMRN': {'cik_str': 1048477,\n", "  'ticker': 'BMRN',\n", "  'title': 'BIOMARIN PHARMACEUTICAL INC'},\n", " 'CR': {'cik_str': 1944013, 'ticker': 'CR', 'title': 'Crane Co'},\n", " 'VNOM': {'cik_str': 1602065, 'ticker': 'VNOM', 'title': 'Viper Energy, Inc.'},\n", " 'NCLH': {'cik_str': 1513761,\n", "  'ticker': 'NCLH',\n", "  'title': 'Norwegian Cruise Line Holdings Ltd.'},\n", " 'MORN': {'cik_str': 1289419, 'ticker': 'MOR<PERSON>', 'title': 'Morningstar, Inc.'},\n", " 'SEIC': {'cik_str': 350894, 'ticker': 'SEIC', 'title': 'SEI INVESTMENTS CO'},\n", " 'WTRG': {'cik_str': 78128,\n", "  'ticker': 'WTRG',\n", "  'title': 'Essential Utilities, Inc.'},\n", " 'PNW': {'cik_str': 764622,\n", "  'ticker': 'PNW',\n", "  'title': 'PINNACLE WEST CAPITAL CORP'},\n", " 'ERJ': {'cik_str': 1355444, 'ticker': 'ERJ', 'title': 'EMBRAER S.A.'},\n", " 'WLK': {'cik_str': 1262823, 'ticker': 'WLK', 'title': 'WESTLAKE CORP'},\n", " 'OKLO': {'cik_str': 1849056, 'ticker': 'OKL<PERSON>', 'title': 'Oklo Inc.'},\n", " 'CACI': {'cik_str': 16058,\n", "  'ticker': 'CACI',\n", "  'title': 'CACI INTERNATIONAL INC /DE/'},\n", " 'PR': {'cik_str': 1658566, 'ticker': 'PR', 'title': 'Permian Resources Corp'},\n", " 'LKNCY': {'cik_str': 1767582,\n", "  'ticker': 'LK<PERSON><PERSON>',\n", "  'title': 'Luckin Coffee Inc.'},\n", " 'WMS': {'cik_str': 1604028,\n", "  'ticker': 'WMS',\n", "  'title': 'ADVANCED DRAINAGE SYSTEMS, INC.'},\n", " 'AIZ': {'cik_str': 1267238, 'ticker': 'AIZ', 'title': 'ASSURANT, INC.'},\n", " 'TTIPF': {'cik_str': 1877778,\n", "  'ticker': 'TTIPF',\n", "  'title': 'Thiogenesis Therapeutics, Corp.'},\n", " 'AGI': {'cik_str': 1178819, 'ticker': 'AGI', 'title': 'ALAMOS GOLD INC'},\n", " 'CRRFY': {'cik_str': 1078642, 'ticker': 'CRRFY', 'title': 'CARREFOUR SA'},\n", " 'ACI': {'cik_str': 1646972,\n", "  'ticker': 'ACI',\n", "  'title': 'Albertsons Companies, Inc.'},\n", " 'AKAM': {'cik_str': 1086222,\n", "  'ticker': 'AK<PERSON>',\n", "  'title': 'AKAMAI TECHNOLOGIES INC'},\n", " 'BLSH': {'cik_str': 1872195, 'ticker': 'BLSH', 'title': 'Bullish'},\n", " 'QGEN': {'cik_str': 1015820, 'ticker': 'QGEN', 'title': 'QIAGEN N.V.'},\n", " 'DSEEY': {'cik_str': 1481045,\n", "  'ticker': 'DSEEY',\n", "  'title': 'Daiwa Securities Group Inc.'},\n", " 'PUGBY': {'cik_str': 2029472,\n", "  'ticker': 'PUGBY',\n", "  'title': '<PERSON><PERSON><PERSON>s S.A./ADR'},\n", " 'GLXY': {'cik_str': 1859392,\n", "  'ticker': 'GLXY',\n", "  'title': 'Galaxy Digital Inc.'},\n", " 'FRHC': {'cik_str': 924805,\n", "  'ticker': 'FRHC',\n", "  'title': 'Freedom Holding Corp.'},\n", " 'KNSL': {'cik_str': 1669162,\n", "  'ticker': 'KNSL',\n", "  'title': 'Kinsale Capital Group, Inc.'},\n", " 'DTM': {'cik_str': 1842022, 'ticker': 'DTM', 'title': 'DT Midstream, Inc.'},\n", " 'HIMS': {'cik_str': 1773751,\n", "  'ticker': 'HIMS',\n", "  'title': 'Hims & Hers Health, Inc.'},\n", " 'HII': {'cik_str': 1501585,\n", "  'ticker': 'HII',\n", "  'title': 'HUN<PERSON>NG<PERSON><PERSON> INGALLS INDUSTRIES, INC.'},\n", " 'BMNR': {'cik_str': 1829311,\n", "  'ticker': 'BMNR',\n", "  'title': 'BITMINE IMMERSION TECHNOLOGIES, INC.'},\n", " 'UUGRY': {'cik_str': 1440130,\n", "  'ticker': 'UUGRY',\n", "  'title': 'United Utilities Group plc'},\n", " 'CSXXY': {'cik_str': 1541309,\n", "  'ticker': 'CSXXY',\n", "  'title': 'carsales.com Limited/ADR'},\n", " 'CAI': {'cik_str': 2019410,\n", "  'ticker': 'CAI',\n", "  'title': 'Caris Life Sciences, Inc.'},\n", " 'ARMK': {'cik_str': 1584509, 'ticker': 'ARMK', 'title': 'Aramark'},\n", " 'AIT': {'cik_str': 109563,\n", "  'ticker': 'AIT',\n", "  'title': 'APPLIED INDUSTRIAL TECHNOLOGIES INC'},\n", " 'ALGN': {'cik_str': 1097149,\n", "  'ticker': 'ALGN',\n", "  'title': 'ALIGN TECHNOLOGY INC'},\n", " 'WBA': {'cik_str': 1618921,\n", "  'ticker': 'WBA',\n", "  'title': 'Walgreens Boots Alliance, Inc.'},\n", " 'RVTY': {'cik_str': 31791, 'ticker': 'RVTY', 'title': 'REVVITY, INC.'},\n", " 'EXEL': {'cik_str': 939767, 'ticker': 'EXEL', 'title': 'EXELIXIS, INC.'},\n", " 'MRNA': {'cik_str': 1682852, 'ticker': 'MRNA', 'title': 'Moderna, Inc.'},\n", " 'BE': {'cik_str': 1664703, 'ticker': 'BE', 'title': 'Bloom Energy Corp'},\n", " 'BROS': {'cik_str': 1866581, 'ticker': 'BROS', 'title': 'Dutch Bros Inc.'},\n", " 'NCLTY': {'cik_str': 1801729,\n", "  'ticker': 'NCLTY',\n", "  'title': 'Nitori Holdings Co., Ltd.'},\n", " 'RDEIY': {'cik_str': 1438654,\n", "  'ticker': 'RDEIY',\n", "  'title': 'Red Electrica Corporacion SA/ADR'},\n", " 'WCC': {'cik_str': 929008,\n", "  'ticker': 'WCC',\n", "  'title': 'WESCO INTERNATIONAL INC'},\n", " 'ATI': {'cik_str': 1018963, 'ticker': 'ATI', 'title': 'ATI INC'},\n", " 'HTHT': {'cik_str': 1483994, 'ticker': 'HTHT', 'title': 'H World Group Ltd'},\n", " 'MLI': {'cik_str': 89439, 'ticker': 'MLI', 'title': 'MUELLER INDUSTRIES INC'},\n", " 'GME': {'cik_str': 1326380, 'ticker': 'GME', 'title': 'GameStop Corp.'},\n", " 'OVV': {'cik_str': 1792580, 'ticker': 'OVV', 'title': 'Ovintiv Inc.'},\n", " 'MOS': {'cik_str': 1285785, 'ticker': 'MOS', 'title': 'MOSAIC CO'},\n", " 'AOS': {'cik_str': 91142, 'ticker': 'AOS', 'title': 'SMITH A O CORP'},\n", " 'BZLFY': {'cik_str': 1072397, 'ticker': 'BZLFY', 'title': 'BUNZL PLC'},\n", " 'TAP': {'cik_str': 24545,\n", "  'ticker': 'TAP',\n", "  'title': 'MOLSO<PERSON> COORS BEVERAGE CO'},\n", " 'LINE': {'cik_str': 1868159, 'ticker': 'LINE', 'title': 'Lineage, Inc.'},\n", " 'KT': {'cik_str': 892450, 'ticker': 'KT', 'title': 'KT CORP'},\n", " 'UHAL': {'cik_str': 4457,\n", "  'ticker': 'UHAL',\n", "  'title': 'U-Haul Holding Co /NV/'},\n", " 'SFD': {'cik_str': 91388, 'ticker': 'SFD', 'title': 'SMITHFIELD FOODS INC'},\n", " 'W': {'cik_str': 1616707, 'ticker': 'W', 'title': 'Wayfair Inc.'},\n", " 'NIO': {'cik_str': 1736541, 'ticker': 'NIO', 'title': 'NIO Inc.'},\n", " 'AGNC': {'cik_str': 1423689,\n", "  'ticker': 'AGNC',\n", "  'title': 'AGNC Investment Corp.'},\n", " 'COKE': {'cik_str': 317540,\n", "  'ticker': 'COKE',\n", "  'title': 'Coca-Cola Consolidated, Inc.'},\n", " 'TIMB': {'cik_str': 1826168, 'ticker': 'TIMB', 'title': 'TIM S.A.'},\n", " 'AR': {'cik_str': 1433270, 'ticker': 'AR', 'title': 'ANTERO RESOURCES Corp'},\n", " 'NYT': {'cik_str': 71691, 'ticker': 'NYT', 'title': 'NEW YORK TIMES CO'},\n", " 'BPYPP': {'cik_str': 1545772,\n", "  'ticker': 'BPYPP',\n", "  'title': 'Brookfield Property Partners L.P.'},\n", " 'RRX': {'cik_str': 82811, 'ticker': 'RRX', 'title': 'REGAL REXNORD CORP'},\n", " 'MGM': {'cik_str': 789570,\n", "  'ticker': 'MGM',\n", "  'title': 'MGM Resorts International'},\n", " 'SSB': {'cik_str': 764038, 'ticker': 'SSB', 'title': 'SouthState Corp'},\n", " 'PEN': {'cik_str': 1321732, 'ticker': 'PEN', 'title': 'Penumbra Inc'},\n", " 'PCOR': {'cik_str': 1611052,\n", "  'ticker': 'PCOR',\n", "  'title': 'PROCORE TECHNOLOGIES, INC.'},\n", " 'AYI': {'cik_str': 1144215, 'ticker': 'AYI', 'title': 'ACUITY INC. (DE)'},\n", " 'BILI': {'cik_str': 1723690, 'ticker': 'BILI', 'title': 'Bilibili Inc.'},\n", " 'BBIO': {'cik_str': 1743881,\n", "  'ticker': 'BBIO',\n", "  'title': 'BridgeBio Pharma, Inc.'},\n", " 'DOX': {'cik_str': 1062579, 'ticker': 'DOX', 'title': 'AMDOCS LTD'},\n", " 'WBS': {'cik_str': 801337,\n", "  'ticker': 'WBS',\n", "  'title': 'WEBSTER FINANCIAL CORP'},\n", " 'CPB': {'cik_str': 16732, 'ticker': 'CPB', 'title': \"CAMPBELL'S Co\"},\n", " 'BZ': {'cik_str': 1842827, 'ticker': 'BZ', 'title': 'Kanzhun Ltd'},\n", " 'ORI': {'cik_str': 74260,\n", "  'ticker': 'OR<PERSON>',\n", "  'title': 'OLD REPUBLIC INTERNATIONAL CORP'},\n", " 'ENSG': {'cik_str': 1125376, 'ticker': 'ENSG', 'title': 'ENSIGN GROUP, INC'},\n", " 'MYTHY': {'cik_str': 1437470,\n", "  'ticker': 'MYTHY',\n", "  'title': 'Mytilineos Holdings S.A.'},\n", " 'ASR': {'cik_str': 1123452,\n", "  'ticker': 'ASR',\n", "  'title': 'SOUTHEAST AIRPORT GROUP'},\n", " 'DVA': {'cik_str': 927066, 'ticker': 'DVA', 'title': 'DAVITA INC.'},\n", " 'APPF': {'cik_str': 1433195, 'ticker': 'APPF', 'title': 'APPFOLIO INC'},\n", " 'ALB': {'cik_str': 915913, 'ticker': 'ALB', 'title': 'ALBEMARLE CORP'},\n", " 'PCTY': {'cik_str': 1591698,\n", "  'ticker': 'PCTY',\n", "  'title': 'Paylocity Holding Corp'},\n", " 'KAIKY': {'cik_str': 1447126,\n", "  'ticker': 'KAIKY',\n", "  'title': 'Kawasaki Kisen Kaisha Ltd'},\n", " 'HMY': {'cik_str': 1023514,\n", "  'ticker': '<PERSON>Y',\n", "  'title': 'HAR<PERSON>NY GOLD MINING CO LTD'},\n", " 'UWMC': {'cik_str': 1783398, 'ticker': 'UWMC', 'title': 'UWM Holdings Corp'},\n", " 'IVZ': {'cik_str': 914208, 'ticker': 'IVZ', 'title': 'Invesco Ltd.'},\n", " 'IPG': {'cik_str': 51644,\n", "  'ticker': 'IPG',\n", "  'title': 'INTERPUBLIC GROUP OF COMPANIES, INC.'},\n", " 'SKX': {'cik_str': 1065837, 'ticker': 'SKX', 'title': 'SKECHERS USA INC'},\n", " 'TTEK': {'cik_str': 831641, 'ticker': 'TTEK', 'title': 'TETRA TECH INC'},\n", " 'PSO': {'cik_str': 938323, 'ticker': 'PSO', 'title': 'PEARSON PLC'},\n", " 'MTCH': {'cik_str': 891103, 'ticker': 'MTCH', 'title': 'Match Group, Inc.'},\n", " 'KVYO': {'cik_str': 1835830, 'ticker': 'KVYO', 'title': 'Klaviyo, Inc.'},\n", " 'MTSI': {'cik_str': 1493594,\n", "  'ticker': 'MTSI',\n", "  'title': 'MACOM Technology Solutions Holdings, Inc.'},\n", " 'CAG': {'cik_str': 23217, 'ticker': 'CAG', 'title': 'CONAGRA BRANDS INC.'},\n", " 'SRAD': {'cik_str': 1836470,\n", "  'ticker': 'SRAD',\n", "  'title': 'Sportradar Group AG'},\n", " 'FYBR': {'cik_str': 20520,\n", "  'ticker': 'FYBR',\n", "  'title': 'Frontier Communications Parent, Inc.'},\n", " 'REXR': {'cik_str': 1571283,\n", "  'ticker': 'REXR',\n", "  'title': 'Rexford Industrial Realty, Inc.'},\n", " 'OGE': {'cik_str': 1021635, 'ticker': 'OGE', 'title': 'OGE ENERGY CORP.'},\n", " 'WAL': {'cik_str': 1212545,\n", "  'ticker': 'WAL',\n", "  'title': 'WESTERN ALLIANCE BANCORPORATION'},\n", " 'TTAN': {'cik_str': 1638826, 'ticker': 'TTAN', 'title': 'ServiceTitan, Inc.'},\n", " 'PRMB': {'cik_str': 2042694, 'ticker': 'PRMB', 'title': 'Primo Brands Corp'},\n", " 'AES': {'cik_str': 874761, 'ticker': 'AES', 'title': 'AES CORP'},\n", " 'XP': {'cik_str': 1787425, 'ticker': 'XP', 'title': 'XP Inc.'},\n", " 'ALV': {'cik_str': 1034670, 'ticker': 'ALV', 'title': 'AUTOLIV INC'},\n", " 'MNDY': {'cik_str': 1845338, 'ticker': 'MNDY', 'title': 'monday.com Ltd.'},\n", " 'WTS': {'cik_str': 795403,\n", "  'ticker': 'WTS',\n", "  'title': 'WATTS WATER TECHNOLOGIES INC'},\n", " 'ATR': {'cik_str': 896622, 'ticker': 'ATR', 'title': 'APTARGROUP, INC.'},\n", " 'SARO': {'cik_str': 2025410, 'ticker': 'SARO', 'title': 'StandardAero, Inc.'},\n", " 'FSV': {'cik_str': 1637810, 'ticker': 'FSV', 'title': 'FirstService Corp'},\n", " 'WING': {'cik_str': 1636222, 'ticker': 'WING', 'title': 'Wingstop Inc.'},\n", " 'PSKY': {'cik_str': 2041610,\n", "  'ticker': 'PSK<PERSON>',\n", "  'title': 'Paramount Skydance Corp'},\n", " 'CAE': {'cik_str': 1173382, 'ticker': 'CAE', 'title': 'CAE INC'},\n", " 'CUBE': {'cik_str': 1298675, 'ticker': 'CUBE', 'title': 'CubeSmart'},\n", " 'GRFS': {'cik_str': 1438569, 'ticker': 'GRFS', 'title': 'Grifols SA'},\n", " 'OSK': {'cik_str': 775158, 'ticker': 'OSK', 'title': 'OSHKOSH CORP'},\n", " ...}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["sec_ticker_lookup"]}, {"cell_type": "code", "execution_count": 10, "id": "46b62d39", "metadata": {}, "outputs": [], "source": ["company_map = {}\n", "alias_to_ticker_map = {}\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a5b91766", "metadata": {}, "outputs": [], "source": ["company_map_path = \"../data/company_canonical_map.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "1d64960f", "metadata": {}, "outputs": [], "source": ["import json\n", "with open(company_map_path, 'r', encoding='utf-8') as f:\n", "    company_map = json.load(f)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5284d487", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'company_name': 'ADVANCED MICRO DEVICES INC', 'ticker_symbol': 'AMD', 'aliases': ['ADVANCED MICRO DEVICES', 'ADVANCED MICRO DEVICES INC', 'AMD']}, {'company_name': 'INTEL CORP', 'ticker_symbol': 'INTC', 'aliases': ['INTC', 'INTEL CORP', 'INTEL CORPORATION']}, {'company_name': 'NVIDIA CORP', 'ticker_symbol': 'NVDA', 'aliases': ['NVDA', 'NVIDIA CORP', 'NVIDIA CORPORATION']}, {'company_name': 'TEXAS INSTRUMENTS INC', 'ticker_symbol': 'TXN', 'aliases': ['TEXAS INSTRUMENTS', 'TEXAS INSTRUMENTS INC', 'TXN']}]\n"]}], "source": ["print(company_map)"]}, {"cell_type": "code", "execution_count": 8, "id": "7c9bde53", "metadata": {}, "outputs": [], "source": ["entity_dict = {}\n", "name = \"TEXAS INSTRUMENTS INC\"\n", "for obj in company_map:\n", "    if name in obj.get(\"aliases\", []):\n", "        entity_dict[name] = obj.get(\"company_name\", name)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4fd09faa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'TEXAS INSTRUMENTS INC': 'TEXAS INSTRUMENTS INC'}\n"]}], "source": ["print(entity_dict)"]}, {"cell_type": "code", "execution_count": 18, "id": "0f2f285c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'company_name': 'ADVANCED MICRO DEVICES INC', 'ticker_symbol': 'AMD', 'aliases': ['ADVANCED MICRO DEVICES', 'ADVANCED MICRO DEVICES INC', 'AMD']}, {'company_name': 'INTEL CORP', 'ticker_symbol': 'INTC', 'aliases': ['INTC', 'INTEL CORP', 'INTEL CORPORATION']}, {'company_name': 'NVIDIA CORP', 'ticker_symbol': 'NVDA', 'aliases': ['NVDA', 'NVIDIA CORP', 'NVIDIA CORPORATION']}, {'company_name': 'TEXAS INSTRUMENTS INC', 'ticker_symbol': 'TXN', 'aliases': ['TEXAS INSTRUMENTS', 'TEXAS INSTRUMENTS INC', 'TXN']}]\n"]}], "source": ["import json\n", "with open(company_map_path, 'r', encoding='utf-8') as f:\n", "    content = f.read()\n", "    if content.strip():\n", "        list_of_companies = json.loads(content)\n", "        print(list_of_companies)\n", "        for company_obj in list_of_companies:\n", "            ticker = company_obj.get(\"ticker_symbol\")\n", "            if ticker:\n", "                company_map[ticker] = company_obj\n", "                for alias in company_obj.get(\"aliases\", []):\n", "                    alias_to_ticker_map[alias.upper()] = ticker"]}, {"cell_type": "code", "execution_count": 3, "id": "7150d03b", "metadata": {}, "outputs": [], "source": ["input_path = \"../data/output/sample_relationship.jsonl\""]}, {"cell_type": "code", "execution_count": 8, "id": "7cb1bc30", "metadata": {}, "outputs": [], "source": ["import json\n", "def collect_data_from_file(input_path):\n", "        \"\"\"\n", "        Reads the entire .jsonl file once to do two jobs:\n", "        1. Collect a list of all unique entity objects.\n", "        2. Collect a flat list of all relationship objects.\n", "\n", "        Returns:\n", "            A tuple containing: (unique_entity_list, all_relationships_list)\n", "        \"\"\"\n", "        unique_entities: Dict[str, Dict] = {}\n", "        all_relationships: List[Dict] = []\n", "        try:\n", "            with open(input_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    data = json.loads(line)\n", "                    all_relationships.extend(data.get(\"relationships\", []))\n", "                    # Collect unique entities from this line\n", "                    for entity in data.get(\"entities\", []):\n", "                        entity_name = entity.get(\"name\")\n", "                        if entity_name and entity_name not in unique_entities:\n", "                            unique_entities[entity_name] = entity\n", "            entity_list = list(unique_entities.values())\n", "            print(f\"Collected {len(entity_list)} unique entities and loaded {len(all_relationships)} relationships.\")\n", "            return entity_list, all_relationships\n", "        except FileNotFoundError:\n", "            print(f\"Input file not found: {input_path}. Cannot proceed.\")\n", "            return [], []"]}, {"cell_type": "code", "execution_count": 9, "id": "893d1a7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collected 5 unique entities and loaded 4 relationships.\n"]}], "source": ["ent, rel = collect_data_from_file(input_path)"]}, {"cell_type": "code", "execution_count": 10, "id": "9325ba4d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'Intel Corporation', 'type': 'Company'},\n", " {'name': 'NVIDIA Corporation', 'type': 'Company'},\n", " {'name': 'Advanced Micro Devices', 'type': 'Company'},\n", " {'name': 'Texas Instruments', 'type': 'Company'},\n", " {'name': 'AXFJK', 'type': 'Company'}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["ent"]}, {"cell_type": "code", "execution_count": 11, "id": "8f05eb3d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'source_entity': 'NVIDIA Corporation',\n", "  'target_entity': 'Intel Corporation',\n", "  'relationship_type': 'COMPETES_WITH',\n", "  'strength_score': 0.92,\n", "  'evidence': 'Sample output',\n", "  'rationale': 'Sample rationale',\n", "  'source': {'report_type': '10-Q',\n", "   'period_of_report': '2025-04-27',\n", "   'section': 'part2item2',\n", "   'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-20250427.htm'}},\n", " {'source_entity': 'Advanced Micro Devices',\n", "  'target_entity': 'Intel Corporation',\n", "  'relationship_type': 'COMPETES_WITH',\n", "  'strength_score': 0.92,\n", "  'evidence': 'Sample output',\n", "  'rationale': 'Sample rationale',\n", "  'source': {'report_type': '10-Q',\n", "   'period_of_report': '2025-04-27',\n", "   'section': 'part2item2',\n", "   'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-20250427.htm'}},\n", " {'source_entity': 'Texas Instruments',\n", "  'target_entity': 'Intel Corporation',\n", "  'relationship_type': 'COMPETES_WITH',\n", "  'strength_score': 0.92,\n", "  'evidence': 'Sample output',\n", "  'rationale': 'Sample rationale',\n", "  'source': {'report_type': '10-Q',\n", "   'period_of_report': '2025-04-27',\n", "   'section': 'part2item2',\n", "   'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-20250427.htm'}},\n", " {'source_entity': 'Texas Instruments',\n", "  'target_entity': 'AXFJK',\n", "  'relationship_type': 'COMPETES_WITH',\n", "  'strength_score': 0.92,\n", "  'evidence': 'Sample output',\n", "  'rationale': 'Sample rationale',\n", "  'source': {'report_type': '10-Q',\n", "   'period_of_report': '2025-04-27',\n", "   'section': 'part2item2',\n", "   'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000116/nvda-20250427.htm'}}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rel"]}, {"cell_type": "code", "execution_count": 16, "id": "9522b395", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'Intel Corporation', 'type': 'Company'}, {'name': 'NVIDIA Corporation', 'type': 'Company'}, {'name': 'Advanced Micro Devices', 'type': 'Company'}, {'name': 'Texas Instruments', 'type': 'Company'}, {'name': 'AXFJK', 'type': 'Company'}]\n"]}], "source": ["print(op)"]}, {"cell_type": "code", "execution_count": 17, "id": "38e0d180", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'Intel Corporation', 'type': 'Company'}, {'name': 'Texas Instruments', 'type': 'Company'}, {'name': 'Advanced Micro Devices', 'type': 'Company'}, {'name': 'AXFJK', 'type': 'Company'}, {'name': 'NVIDIA Corporation', 'type': 'Company'}]\n"]}], "source": ["entities_to_process = []\n", "with open(input_path, 'r', encoding='utf-8') as f:\n", "    for line in f:\n", "        raw_data = json.loads(line)\n", "        entities_to_process.extend(raw_data.get(\"entities\", []))\n", "\n", "entities_to_process = [dict(t) for t in {tuple(d.items()) for d in entities_to_process}]\n", "print(entities_to_process)"]}, {"cell_type": "code", "execution_count": 19, "id": "ba29a4b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'AMD': {'company_name': 'ADVANCED MICRO DEVICES INC',\n", "  'ticker_symbol': 'AMD',\n", "  'aliases': ['ADVANCED MICRO DEVICES', 'ADVANCED MICRO DEVICES INC', 'AMD']},\n", " 'INTC': {'company_name': 'INTEL CORP',\n", "  'ticker_symbol': 'INTC',\n", "  'aliases': ['INTC', 'INTEL CORP', 'INTEL CORPORATION']},\n", " 'NVDA': {'company_name': 'NVIDIA CORP',\n", "  'ticker_symbol': 'NVDA',\n", "  'aliases': ['NVDA', 'NVIDIA CORP', 'NVIDIA CORPORATION']},\n", " 'TXN': {'company_name': 'TEXAS INSTRUMENTS INC',\n", "  'ticker_symbol': 'TXN',\n", "  'aliases': ['TEXAS INSTRUMENTS', 'TEXAS INSTRUMENTS INC', 'TXN']}}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["company_map"]}, {"cell_type": "code", "execution_count": 20, "id": "8770812c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ADVANCED MICRO DEVICES': 'AMD',\n", " 'ADVANCED <PERSON>CRO DEVICES INC': 'AMD',\n", " 'AMD': 'AMD',\n", " 'INTC': 'INTC',\n", " 'INTEL CORP': 'INTC',\n", " 'INTEL CORPORATION': 'INTC',\n", " 'NVDA': 'NVDA',\n", " 'NVIDIA CORP': 'NVDA',\n", " 'NVIDIA CORPORATION': 'NVDA',\n", " 'TEXAS INSTRUMENTS': 'TXN',\n", " 'TEXAS INSTRUMENTS INC': 'TXN',\n", " 'TXN': 'TXN'}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["alias_to_ticker_map"]}, {"cell_type": "code", "execution_count": 21, "id": "249303e2", "metadata": {}, "outputs": [], "source": ["list_to_save = sorted(company_map.values(), key=lambda x: x['ticker_symbol'])"]}, {"cell_type": "code", "execution_count": 23, "id": "5b735eaa", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'company_name': 'ADVANCED MICRO DEVICES INC',\n", "  'ticker_symbol': 'AMD',\n", "  'aliases': ['ADVANCED MICRO DEVICES', 'ADVANCED MICRO DEVICES INC', 'AMD']},\n", " {'company_name': 'INTEL CORP',\n", "  'ticker_symbol': 'INTC',\n", "  'aliases': ['INTC', 'INTEL CORP', 'INTEL CORPORATION']},\n", " {'company_name': 'NVIDIA CORP',\n", "  'ticker_symbol': 'NVDA',\n", "  'aliases': ['NVDA', 'NVIDIA CORP', 'NVIDIA CORPORATION']},\n", " {'company_name': 'TEXAS INSTRUMENTS INC',\n", "  'ticker_symbol': 'TXN',\n", "  'aliases': ['TEXAS INSTRUMENTS', 'TEXAS INSTRUMENTS INC', 'TXN']}]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["list_to_save"]}, {"cell_type": "code", "execution_count": null, "id": "2bc41b22", "metadata": {}, "outputs": [], "source": ["def get_company_details(self, ticker: str) -> Dict[str, Any] | None:\n", "        \"\"\"\n", "        Retrieves the full canonical details for a company using its ticker.\n", "\n", "        Args:\n", "            ticker (str): The stock ticker symbol (our canonical ID).\n", "\n", "        Returns:\n", "            Dict[str, Any] | None: The company object from our map, or None if not found.\n", "        \"\"\"\n", "        return self.company_map.get(ticker)"]}, {"cell_type": "markdown", "id": "c04af656", "metadata": {}, "source": ["# fuzzy match"]}, {"cell_type": "code", "execution_count": 8, "id": "dbb86003", "metadata": {}, "outputs": [], "source": ["from fuzzywuzzy import fuzz\n", "\n", "def compare_strings(str1, str2):\n", "    print(f\"Comparing: '{str1}'  vs  '{str2}'\\n\")\n", "    \n", "    print(\"fuzz.ratio:\".ljust(25), fuzz.ratio(str1, str2))\n", "    print(\"fuzz.partial_ratio:\".l<PERSON><PERSON>(25), fuzz.partial_ratio(str1, str2))\n", "    print(\"fuzz.token_sort_ratio:\".lju<PERSON>(25), fuzz.token_sort_ratio(str1, str2))\n", "    print(\"fuzz.token_set_ratio:\".l<PERSON><PERSON>(25), fuzz.token_set_ratio(str1, str2))\n", "    print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": 12, "id": "e1c3ff13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Comparing: 'INTEL'  vs  'INTERNAL CORPORATION'\n", "\n", "fuzz.ratio:               40\n", "fuzz.partial_ratio:       80\n", "fuzz.token_sort_ratio:    40\n", "fuzz.token_set_ratio:     40\n", "--------------------------------------------------\n"]}], "source": ["compare_strings(\"INTEL\", \"INTERNAL CORPORATION\")"]}, {"cell_type": "code", "execution_count": 6, "id": "9aa14b95", "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, Iterable, List, Tuple\n", "import json\n", "def flatten_relationship_properties(props: Dict[str, Any]) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Neo4j relationship properties must be primitive (string/number/bool/list/temporal/point).\n", "    This converts the nested \"source\" object into flat properties and a JSON string for reference.\n", "    \"\"\"\n", "    props = dict(props or {})\n", "    source_obj = props.pop(\"source\", None)\n", "    print(source_obj)\n", "\n", "    # keep originals\n", "    flattened = dict(props)\n", "    print(flattened)\n", "\n", "    if isinstance(source_obj, dict):\n", "        # convenient flattened fields for filtering\n", "        flattened[\"source_report_type\"] = source_obj.get(\"report_type\")\n", "        flattened[\"source_period_of_report\"] = source_obj.get(\"period_of_report\")\n", "        flattened[\"source_section\"] = source_obj.get(\"section\")\n", "        flattened[\"source_url\"] = source_obj.get(\"source_url\")\n", "        # also preserve full JSON for audit/debug\n", "        flattened[\"source_json\"] = json.dumps(source_obj, separators=(\",\", \":\"), ensure_ascii=False)\n", "\n", "    return flattened"]}, {"cell_type": "code", "execution_count": 3, "id": "40ae16dc", "metadata": {}, "outputs": [], "source": ["dictio = {\n", "        \"source\": {\n", "          \"report_type\": \"10-K\",\n", "          \"period_of_report\": \"2025-01-26\",\n", "          \"section\": \"1\",\n", "          \"source_url\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm\"\n", "        },\n", "        \"evidence\": \"Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.\",\n", "        \"rationale\": \"NVIDIA states it is subject to U.S. laws and regulatory oversight by the U.S. government.\",\n", "        \"strength_score\": 0.8\n", "      }"]}, {"cell_type": "code", "execution_count": 7, "id": "93e40b4d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'report_type': '10-K', 'period_of_report': '2025-01-26', 'section': '1', 'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm'}\n", "{'evidence': 'Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.', 'rationale': 'NVIDIA states it is subject to U.S. laws and regulatory oversight by the U.S. government.', 'strength_score': 0.8}\n"]}], "source": ["res = flatten_relationship_properties(dictio)"]}, {"cell_type": "code", "execution_count": 9, "id": "cd6adf1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'evidence': 'Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.',\n", " 'rationale': 'NVIDIA states it is subject to U.S. laws and regulatory oversight by the U.S. government.',\n", " 'strength_score': 0.8,\n", " 'source_report_type': '10-K',\n", " 'source_period_of_report': '2025-01-26',\n", " 'source_section': '1',\n", " 'source_url': 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm',\n", " 'source_json': '{\"report_type\":\"10-K\",\"period_of_report\":\"2025-01-26\",\"section\":\"1\",\"source_url\":\"https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm\"}'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 10, "id": "820f2062", "metadata": {}, "outputs": [], "source": ["def labels_list(node: Dict[str, Any]) -> List[str]:\n", "    labels = node.get(\"labels\") or []\n", "    # De-duplicate and keep order stable\n", "    seen = set()\n", "    cleaned = []\n", "    for l in labels:\n", "        if not isinstance(l, str):\n", "            continue\n", "        if l not in seen:\n", "            seen.add(l)\n", "            cleaned.append(l)\n", "    if not cleaned:\n", "        cleaned = [\"Thing\"]  # fallback label\n", "    return cleaned"]}, {"cell_type": "code", "execution_count": 11, "id": "a0a5d102", "metadata": {}, "outputs": [], "source": ["nd = {\n", "      \"id\": \"NVDA\",\n", "      \"labels\": [\n", "        \"Company\"\n", "      ],\n", "      \"properties\": {\n", "        \"name\": \"NVIDIA CORP\",\n", "        \"id\": \"NVDA\"\n", "      }\n", "    }\n", "\n", "res_nd = labels_list(nd)"]}, {"cell_type": "code", "execution_count": 12, "id": "e46a0f50", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Company']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["res_nd"]}, {"cell_type": "code", "execution_count": 13, "id": "4adfa70f", "metadata": {}, "outputs": [], "source": ["import hashlib\n", "def _sha1(text: str) -> str:\n", "    return hashlib.sha1(text.encode(\"utf-8\")).hexdigest()"]}, {"cell_type": "code", "execution_count": 16, "id": "ef0c924b", "metadata": {}, "outputs": [], "source": ["def build_rel_id(r: Dict[str, Any]) -> str:\n", "    \"\"\"\n", "    Build a deterministic id for MERGE'ing relationships so that\n", "    distinct sources can create parallel edges while identical ones don't duplicate.\n", "    \"\"\"\n", "    t = r.get(\"type\", \"\")\n", "    s = r.get(\"source_id\", \"\")\n", "    tgt = r.get(\"target_id\", \"\")\n", "\n", "    p = dict(r.get(\"properties\") or {})\n", "    src = p.get(\"source\") or {}\n", "    key_parts = [\n", "        t, s, tgt,\n", "        str(src.get(\"report_type\", \"\")),\n", "        str(src.get(\"period_of_report\", \"\")),\n", "        str(src.get(\"section\", \"\")),\n", "        str(src.get(\"source_url\", \"\")),\n", "        str(p.get(\"evidence\", \"\")),\n", "        str(p.get(\"rationale\", \"\")),\n", "        str(p.get(\"strength_score\", \"\")),\n", "    ]\n", "    print(key_parts)\n", "    return _sha1(\"|\".join(key_parts))"]}, {"cell_type": "code", "execution_count": 19, "id": "c24f9ba4", "metadata": {}, "outputs": [], "source": ["rel_dict = {\n", "      \"source_id\": \"NVDA\",\n", "      \"target_id\": \"United States Government\",\n", "      \"type\": \"IS_REGULATED_BY\",\n", "      \"properties\": {\n", "        \"source\": {\n", "          \"report_type\": \"10-K\",\n", "          \"period_of_report\": \"2025-01-26\",\n", "          \"section\": \"1\",\n", "          \"source_url\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm\"\n", "        },\n", "        \"evidence\": \"Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.\",\n", "        \"rationale\": \"NVIDIA states it is subject to U.S. laws and regulatory oversight by the U.S. government.\",\n", "        \"strength_score\": 0.8\n", "      }\n", "    }"]}, {"cell_type": "code", "execution_count": 20, "id": "e452c39f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IS_REGULATED_BY', 'NVDA', 'United States Government', '10-K', '2025-01-26', '1', 'https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm', 'Our worldwide business activities are subject to various laws, rules, and regulations of the United States as well as of foreign governments.', 'NVIDIA states it is subject to U.S. laws and regulatory oversight by the U.S. government.', '0.8']\n"]}, {"data": {"text/plain": ["'485dc151a64e2bbe60290fe8e6f2c9d8f36ab13e'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["has_res = build_rel_id(rel_dict)\n", "\n", "has_res"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}