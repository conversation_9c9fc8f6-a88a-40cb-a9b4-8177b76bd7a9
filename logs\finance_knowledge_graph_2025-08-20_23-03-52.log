2025-08-20 23:03:52,427 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-20_23-03-52.log
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:176] - ========================================================
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:177] - = Starting Entity Resolution Pipeline                  =
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:178] - ========================================================
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:188] - Selected latest raw file for processing: sample_relationships.jsonl
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:40] - Initializing Entity Resolution Pipeline...
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:19] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-20 23:03:52,585 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:43] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-20 23:03:52,637 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:58] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 23:03:52,637 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:39] - CompanyNormalizer initialized with 3 canonical companies.
2025-08-20 23:03:52,637 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:49] - Entity Resolution Pipeline initialized.
2025-08-20 23:03:52,637 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:74] - Starting processing for input file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\sample_relationships.jsonl
2025-08-20 23:03:52,637 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:161] - A critical error occurred during file processing: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Autowiz\\Fin_knowledge_graph\\new_approach_11_08\\data\\output\\sample_relationships.jsonl'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 80, in process_file
    with open(input_path, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Autowiz\\Fin_knowledge_graph\\new_approach_11_08\\data\\output\\sample_relationships.jsonl'
2025-08-20 23:03:52,654 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:85] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 23:03:52,657 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:97] - Successfully saved 3 companies to the map.
2025-08-20 23:03:52,657 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:168] - All canonical maps have been saved.
2025-08-20 23:03:52,658 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:213] - Entity resolution pipeline completed successfully.
