2025-08-13 18:47:31,598 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-13_18-47-31.log
2025-08-13 18:47:43,042 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-13 18:47:43,043 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-13 18:47:43,043 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-13 18:47:43,044 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-13 18:47:43,045 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-13 18:47:44,888 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-13 18:47:44,898 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-13 18:47:44,899 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-13 18:48:08,604 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-13 18:48:08,605 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-13 18:48:08,605 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-mini
2025-08-13 18:48:09,338 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-13 18:48:09,338 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:48:09,340 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-13 18:48:09,340 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-13 18:48:09,341 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-13 18:48:09,341 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-13 18:48:09,342 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-13 18:48:57,658 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-13 18:48:57,668 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-13 18:51:32,436 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:91] - Successfully extracted 18 relationships from the provided context.
2025-08-13 18:51:32,436 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 18 raw relationships for INTEL CORP.
2025-08-13 18:51:32,437 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,438 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'CPU design and development and related solutions for third-party customers' of type 'Product' with ID 'product_cpu_design_and_development_and'.
2025-08-13 18:51:32,440 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,441 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Client Computing Group (CCG)' of type 'Industry Sector' with ID 'industry sector_client_computing_group_(ccg)'.
2025-08-13 18:51:32,443 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,452 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Data Center and Artificial Intelligence (DCAI)' of type 'Industry Sector' with ID 'industry sector_data_center_and_artificial_int'.
2025-08-13 18:51:32,454 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,454 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Networking and Edge (NEX)' of type 'Industry Sector' with ID 'industry sector_networking_and_edge_(nex)'.
2025-08-13 18:51:32,457 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Total Intel Products revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,472 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'CCG' (Score: 100.0). Matched to 'industry sector_client_computing_group_(ccg)'. Adding as new alias.
2025-08-13 18:51:32,474 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Operating income' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,475 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher CCG revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,476 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Operating income' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,476 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher unit costs in DCAI and CCG' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,477 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,480 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,484 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'DCAI' (Score: 100.0). Matched to 'industry sector_data_center_and_artificial_int'. Adding as new alias.
2025-08-13 18:51:32,486 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Intel Products' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,487 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'NEX' (Score: 100.0). Matched to 'industry sector_networking_and_edge_(nex)'. Adding as new alias.
2025-08-13 18:51:32,488 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,489 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'our CCG operating segment' (Score: 100.0). Matched to 'industry sector_client_computing_group_(ccg)'. Adding as new alias.
2025-08-13 18:51:32,493 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This Q2 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,494 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher Q2 2024 CCG revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,501 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This Q2 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,501 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'lower Q2 2024 DCAI and NEX period charges due to the sell-through of previously reserved inventory and lower reserves taken' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,504 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This Q2 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,505 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher Q2 2024 unit costs and higher Q2 2024 operating expenses in DCAI' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,508 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'Revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,523 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This YTD 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,524 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher YTD 2024 CCG revenue' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,526 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This YTD 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,529 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'lower YTD 2024 period charges across each of the Intel Products' operating segments due to the sell-through of previously reserved inventory and lower reserves taken' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,537 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'This YTD 2024 operating margin increase' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,538 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'higher YTD 2024 unit costs in CCG and DCAI' has type 'Other'. Skipping normalization and logging for review.
2025-08-13 18:51:32,540 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-13 18:51:32,541 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 18
2025-08-13 18:51:32,543 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-13 18:51:32,544 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-13 18:51:32,553 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-13 18:51:32,554 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-13 18:51:32,555 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-13 18:51:32,555 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
