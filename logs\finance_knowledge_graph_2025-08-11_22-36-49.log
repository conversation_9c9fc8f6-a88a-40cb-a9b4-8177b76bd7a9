2025-08-11 22:36:49,417 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-11_22-36-49.log
2025-08-11 22:36:49,417 - Finance_Knowledge_Graph - INFO - [data_retriever.py:126] - --- Running DataRetriever Test ---
2025-08-11 22:36:49,417 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-11 22:36:51,180 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-11 22:36:51,186 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-11 22:36:51,186 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-11 22:37:06,118 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-11 22:37:06,118 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-11 22:37:06,118 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-11 22:37:06,510 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Describe the competitive landscape and market position for NVIDIA CORP.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:06,630 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Who are the main competitors, rivals, and peers of NVIDIA CORP?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:06,744 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Analysis of market share, pricing pressure, and competitive advantages for NVIDIA CORP.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:06,860 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Who are the key suppliers, vendors, and third-party manufacturers for NVIDIA CORP?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:06,968 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Description of NVIDIA CORP's manufacturing processes, key partners, and production facilities.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,079 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'What are the supply chain risks and dependencies for NVIDIA CORP, including reliance on sole-source suppliers?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,199 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Who are NVIDIA CORP's major customers, clients, and key partners?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,299 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Details on strategic alliances, collaborations, and joint ventures involving NVIDIA CORP.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,410 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Information on customer concentration and revenue generated from significant clients of NVIDIA CORP.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,520 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'In which countries and geographic regions does NVIDIA CORP have significant operations, sales, or manufacturing facilities?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,629 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'What are the geopolitical risks, tariffs, and trade regulations affecting NVIDIA CORP's international business?'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,737 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Description of NVIDIA CORP's core technologies, product portfolio, and intellectual property.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,829 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Information on technology licensing agreements between NVIDIA CORP and other entities.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,944 - Finance_Knowledge_Graph - ERROR - [data_retriever.py:115] - Qdrant search failed for query 'Details about recent or potential mergers, acquisitions, or strategic investments made by NVIDIA CORP.'. Error: module 'qdrant_client.models' has no attribute 'NearVector'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\data_retriever.py", line 96, in fetch_relevant_chunks
    query=models.NearVector(vector=query_vector),
          ^^^^^^^^^^^^^^^^^
AttributeError: module 'qdrant_client.models' has no attribute 'NearVector'
2025-08-11 22:37:07,944 - Finance_Knowledge_Graph - INFO - [data_retriever.py:118] - Fetched a total of 0 unique chunks for 'NVIDIA CORP'.
2025-08-11 22:37:07,944 - Finance_Knowledge_Graph - WARNING - [data_retriever.py:157] - Test run completed, but no chunks were retrieved.
