2025-08-20 18:45:04,969 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-20_18-45-04.log
2025-08-20 18:45:05,076 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:176] - ========================================================
2025-08-20 18:45:05,076 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:177] - = Starting Entity Resolution Pipeline                  =
2025-08-20 18:45:05,084 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:178] - ========================================================
2025-08-20 18:45:05,084 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:188] - Selected latest raw file for processing: sample_relationship.jsonl
2025-08-20 18:45:05,084 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:40] - Initializing Entity Resolution Pipeline...
2025-08-20 18:45:05,084 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:22] - Initializing CompanyNormalizer...
2025-08-20 18:45:05,084 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-20 18:45:05,124 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:54] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 18:45:05,124 - Finance_Knowledge_Graph - ERROR - [company_normalizer.py:64] - FATAL: Could not decode JSON from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json. File may be corrupt.
2025-08-20 18:45:05,124 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:207] - The entity resolution pipeline failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 200, in main
    resolution_pipeline = EntityResolutionPipeline(sec_tickers_path, company_map_path)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 41, in __init__
    self.company_normalizer = CompanyNormalizer(sec_ticker_path, company_map_path)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\company_normalizer.py", line 26, in __init__
    self.company_map = self._load_company_map()
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\company_normalizer.py", line 59, in _load_company_map
    return json.load(f)
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
