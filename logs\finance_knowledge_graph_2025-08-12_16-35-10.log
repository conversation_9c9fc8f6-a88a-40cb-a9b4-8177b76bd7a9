2025-08-12 16:35:10,885 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-35-10.log
2025-08-12 16:35:20,678 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-12 16:35:20,678 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-12 16:35:20,678 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-12 16:35:20,685 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-12 16:35:20,688 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 16:35:22,364 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-12 16:35:22,367 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 16:35:22,367 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 16:35:38,249 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 16:35:38,249 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 16:35:38,249 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 16:35:38,958 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 16:35:38,960 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:35:38,960 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 16:35:38,960 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-12 16:35:38,962 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-12 16:35:38,962 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: NVIDIA CORP ---
2025-08-12 16:35:38,963 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 16:36:20,993 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-12 16:36:20,993 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 5 chunks to OpenAI for extraction. Total context length: 22005
2025-08-12 16:36:42,862 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:92] - Successfully extracted 12 relationships from the provided context.
2025-08-12 16:36:42,863 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 12 raw relationships for NVIDIA CORP.
2025-08-12 16:36:42,863 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Advanced Micro Devices, Inc.' of type 'Company' with ID 'company_advanced_micro_devic'.
2025-08-12 16:36:42,863 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Huawei Technologies Co. Ltd.' of type 'Company' with ID 'company_huawei_technologies_'.
2025-08-12 16:36:42,872 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Alibaba Group' of type 'Company' with ID 'company_alibaba_group'.
2025-08-12 16:36:42,875 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Alphabet Inc.' of type 'Company' with ID 'company_alphabet_inc'.
2025-08-12 16:36:42,877 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Amazon, Inc.' of type 'Company' with ID 'company_amazon,_inc'.
2025-08-12 16:36:42,881 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Microsoft Corporation' of type 'Company' with ID 'company_microsoft_corporatio'.
2025-08-12 16:36:42,881 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'automotive OEMs' of type 'Company' with ID 'company_automotive_oems'.
2025-08-12 16:36:42,887 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'tier-1 suppliers' of type 'Company' with ID 'company_tier-1_suppliers'.
2025-08-12 16:36:42,887 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'start-ups' of type 'Company' with ID 'company_start-ups'.
2025-08-12 16:36:42,887 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'universities' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:36:42,892 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'startups' of type 'Company' with ID 'company_startups'.
2025-08-12 16:36:42,896 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-12 16:36:42,896 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 12
2025-08-12 16:36:42,896 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-12 16:36:42,896 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:36:42,900 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-12 16:36:42,904 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-12 16:36:42,904 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-12 16:36:42,904 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
