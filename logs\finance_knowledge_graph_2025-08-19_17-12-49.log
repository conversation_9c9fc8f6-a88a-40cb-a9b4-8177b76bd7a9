2025-08-19 17:12:49,994 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-19_17-12-49.log
2025-08-19 17:13:00,735 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-19 17:13:00,736 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-19 17:13:00,736 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-19 17:13:00,738 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - Initializing Knowledge Graph Pipeline...
2025-08-19 17:13:00,738 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-19 17:13:02,503 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-19 17:13:02,510 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-19 17:13:02,510 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-19 17:13:20,094 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-19 17:13:20,095 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-19 17:13:20,095 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-19 17:13:20,835 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - Pipeline initialized successfully.
2025-08-19 17:13:20,835 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-19 17:13:20,836 - Finance_Knowledge_Graph - INFO - [pipeline.py:60] - --- Processing company: NVIDIA CORP ---
2025-08-19 17:13:20,836 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-19 17:14:39,939 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-19 17:14:39,939 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 1 of 3 for NVIDIA CORP (ID: b7b78a23-5093-4bd1-b065-51c0ba568f54)
2025-08-19 17:14:39,939 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 791
2025-08-19 17:15:38,120 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 8 entities and 6 relationships.
2025-08-19 17:15:38,122 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 2 of 3 for NVIDIA CORP (ID: 51e0fce9-1d7f-4f68-b17b-6bb144f2c436)
2025-08-19 17:15:38,123 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1488
2025-08-19 17:16:27,044 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 3 entities and 2 relationships.
2025-08-19 17:16:27,045 - Finance_Knowledge_Graph - INFO - [pipeline.py:75] - Processing chunk 3 of 3 for NVIDIA CORP (ID: 42dd8a05-8bbe-4c53-ac0a-8e231a46364f)
2025-08-19 17:16:27,046 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1182
2025-08-19 17:17:19,386 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 5 entities and 4 relationships.
2025-08-19 17:17:19,389 - Finance_Knowledge_Graph - INFO - [pipeline.py:89] - --- Pipeline Run Finished ---
2025-08-19 17:17:19,390 - Finance_Knowledge_Graph - INFO - [pipeline.py:90] - Total entities extracted: 16
2025-08-19 17:17:19,391 - Finance_Knowledge_Graph - INFO - [pipeline.py:91] - Total relationships extracted: 12
2025-08-19 17:17:19,393 - Finance_Knowledge_Graph - INFO - [main.py:30] - =====================================================
2025-08-19 17:17:19,395 - Finance_Knowledge_Graph - INFO - [main.py:31] -       PIPELINE EXECUTION FINISHED                 
2025-08-19 17:17:19,396 - Finance_Knowledge_Graph - INFO - [main.py:32] - =====================================================
