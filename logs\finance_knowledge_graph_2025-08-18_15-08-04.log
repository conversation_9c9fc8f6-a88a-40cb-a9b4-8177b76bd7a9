2025-08-18 15:08:04,185 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-18_15-08-04.log
2025-08-18 15:08:12,692 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:177] - --- Starting UMAP -> HDBSCAN Clustering Analysis ---
2025-08-18 15:08:12,696 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:38] - Loading relationship phrases from single JSON file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\nvda.json
2025-08-18 15:08:12,701 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:49] - Found 30 unique relationship phrases in JSON.
2025-08-18 15:08:12,703 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-18 15:08:14,553 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-18 15:08:14,553 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-18 15:08:14,553 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-18 15:08:28,135 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-18 15:08:28,135 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-18 15:08:28,135 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:87] - Vectorizing 30 phrases using the BGE model...
2025-08-18 15:08:33,573 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:89] - Vectorization complete. Embedding matrix shape: (30, 768)
2025-08-18 15:08:33,577 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:108] - Starting dimensionality reduction with UMAP from 768 to 5 dimensions...
2025-08-18 15:08:56,077 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:120] - UMAP reduction complete. New matrix shape: (30, 5)
2025-08-18 15:08:56,081 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:131] - Starting clustering with HDBSCAN...
2025-08-18 15:08:56,111 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:145] - HDBSCAN clustering complete.
2025-08-18 15:08:56,111 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:146] - Found 3 clusters and 10 noise points.
2025-08-18 15:08:56,118 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:152] - 
================================================================================
2025-08-18 15:08:56,120 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:153] - --- CLUSTER ANALYSIS RESULTS ---
2025-08-18 15:08:56,120 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:154] - ================================================================================
2025-08-18 15:08:56,171 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:168] - 
Full cluster analysis saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\cluster_analysis_results.csv
2025-08-18 15:08:56,175 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:225] - --- UMAP -> HDBSCAN Clustering Analysis Finished ---
