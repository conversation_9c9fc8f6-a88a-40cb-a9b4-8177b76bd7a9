
from __future__ import annotations

import argparse
import json
import hashlib
from logger import log
import config
from pathlib import Path
from typing import Any, Dict, Iterable, List, Tuple

from neo4j import GraphDatabase, Driver



# -------- utilities --------

def _sha1(text: str) -> str:
    return hashlib.sha1(text.encode("utf-8")).hexdigest()


def _flatten_relationship_properties(props: Dict[str, Any]) -> Dict[str, Any]:
    """
    Neo4j relationship properties must be primitive (string/number/bool/list/temporal/point).
    This converts the nested "source" object into flat properties and a JSON string for reference.
    """
    props = dict(props or {})
    source_obj = props.pop("source", None)

    # keep originals
    flattened = dict(props)

    if isinstance(source_obj, dict):
        # convenient flattened fields for filtering
        flattened["source_report_type"] = source_obj.get("report_type")
        flattened["source_period_of_report"] = source_obj.get("period_of_report")
        flattened["source_section"] = source_obj.get("section")
        flattened["source_url"] = source_obj.get("source_url")
        # also preserve full JSON for audit/debug
        # flattened["source_json"] = json.dumps(source_obj, separators=(",", ":"), ensure_ascii=False) # this extra object is added but there is no need.

    return flattened


def _ensure_id_in_node_properties(node: Dict[str, Any]) -> None:
    """
    Make sure node.properties['id'] exists and equals node['id'].
    """
    node_id = node.get("id")
    props = node.get("properties") or {}
    if props.get("id") != node_id:
        props["id"] = node_id
    node["properties"] = props


def _labels_list(node: Dict[str, Any]) -> List[str]:
    labels = node.get("labels") or []
    # De-duplicate and keep order stable
    seen = set()
    cleaned = []
    for l in labels:
        if not isinstance(l, str):
            continue
        if l not in seen:
            seen.add(l)
            cleaned.append(l)
    if not cleaned:
        cleaned = ["Thing"]  # fallback label
    return cleaned


def _build_rel_id(r: Dict[str, Any]) -> str:
    """
    Build a deterministic id for MERGE'ing relationships so that
    distinct sources can create parallel edges while identical ones don't duplicate.
    """
    t = r.get("type", "")
    s = r.get("source_id", "")
    tgt = r.get("target_id", "")

    p = dict(r.get("properties") or {})
    src = p.get("source") or {}
    key_parts = [
        t, s, tgt,
        str(src.get("report_type", "")),
        str(src.get("period_of_report", "")),
        str(src.get("section", "")),
        str(src.get("source_url", "")),
        str(p.get("evidence", "")),
        str(p.get("rationale", "")),
        str(p.get("strength_score", "")),
    ]
    return _sha1("|".join(key_parts))


# -------- Cypher queries (Driver 5.x, dynamic labels/types using $()) --------

NODE_MERGE_QUERY = """
MERGE (n:$($labels) {id: $id})
SET n += $props
"""

# We match by global id (assuming it's unique across nodes). If you prefer label-qualified matches,
# add labels to the MATCH patterns too.
REL_MERGE_QUERY = """
MATCH (a {id: $source_id})
MATCH (b {id: $target_id})
MERGE (a)-[r:$($rel_type) {rel_id: $rel_id}]->(b)
SET r += $props
RETURN type(r) AS rel_type, r.rel_id AS rel_id
"""

# Optional: ensure uniqueness on id for each label we ingest
CONSTRAINT_QUERY_TPL = "CREATE CONSTRAINT IF NOT EXISTS FOR (n:`{label}`) REQUIRE n.id IS UNIQUE"


# -------- ingestion functions --------

def ingest_nodes(driver: Driver, nodes: Iterable[Dict[str, Any]]) -> None:
    count = 0
    with driver.session(database=_database_or_default(driver)) as session:
        for node in nodes:
            _ensure_id_in_node_properties(node)
            labels = _labels_list(node)
            node_id = node["id"]
            props = dict(node["properties"] or {})
            # do not redundantly double-SET id via +=
            props_wo_id = {k: v for k, v in props.items() if k != "id"}

            session.execute_write(
                lambda tx, labels, node_id, props_wo_id: tx.run(
                    NODE_MERGE_QUERY,
                    labels=labels,
                    id=node_id,
                    props=props_wo_id,
                ).consume(),
                labels, node_id, props_wo_id
            )
            count += 1
    log.info("Ingested/merged %d nodes.", count)


def ingest_relationships(driver: Driver, rels: Iterable[Dict[str, Any]]) -> None:
    count = 0
    with driver.session(database=_database_or_default(driver)) as session:
        for r in rels:
            rel_type = r.get("type")
            rel_id = _build_rel_id(r)
            props = _flatten_relationship_properties(r.get("properties") or {})

            session.execute_write(
                lambda tx, src, tgt, rtype, rid, rprops: tx.run(
                    REL_MERGE_QUERY,
                    source_id=src,
                    target_id=tgt,
                    rel_type=rtype,
                    rel_id=rid,
                    props=rprops,
                ).consume(),
                r.get("source_id"), r.get("target_id"),
                rel_type, rel_id, props
            )
            count += 1
    log.info("Ingested/merged %d relationships.", count)


def ensure_constraints(driver: Driver, labels: Iterable[str]) -> None:
    """
    Creates UNIQUE(id) constraint for each label, if it doesn't already exist.
    """
    unique_labels = {l for l in labels if isinstance(l, str) and l}
    if not unique_labels:
        return
    with driver.session(database=_database_or_default(driver)) as session:
        for label in sorted(unique_labels):
            cypher = CONSTRAINT_QUERY_TPL.format(label=label.replace("`", "``"))
            session.execute_write(lambda tx, q: tx.run(q).consume(), cypher)
            log.info("Ensured constraint on :%s(id).", label)


def _database_or_default(driver: Driver) -> str | None:
    # If you defined NEO4J_DATABASE in config.py, use it; otherwise let driver default (usually "neo4j")
    try:
        from config import NEO4J_DATABASE  # type: ignore
        return NEO4J_DATABASE or None
    except Exception:
        return None


# -------- main --------

def main() -> None:
    parser = argparse.ArgumentParser(description="Ingest JSON graph into Neo4j AuraDB.")
    parser.add_argument("--input", type=str, default="data/output/cleaned_llm_output_2025-08-24_16-57-42.json",
                        help="Path to JSON file containing 'nodes' and 'relationships'.")
    parser.add_argument("--create-constraints", action="store_true",
                        help="Create UNIQUE(id) constraints for each label encountered.")
    args = parser.parse_args()

    # resolve paths relative to this script (root folder)
    root = Path(__file__).resolve().parent
    input_path = (root / args.input).resolve()

    if not input_path.exists():
        raise FileNotFoundError(f"Input JSON not found: {input_path}")

    data = json.loads(input_path.read_text(encoding="utf-8"))
    nodes = data.get("nodes", [])
    rels = data.get("relationships", [])

    from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD  # provided by you

    log.info("Connecting to Neo4j AuraDB at %s ...", NEO4J_URI)
    with GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD)) as driver:
        # proactively validate connection (driver 5.x)
        driver.verify_connectivity()

        if args.create_constraints:
            # gather all labels that appear in nodes
            all_labels = []
            for n in nodes:
                all_labels.extend(_labels_list(n))
            ensure_constraints(driver, all_labels)

        ingest_nodes(driver, nodes)
        ingest_relationships(driver, rels)

    log.info("Done.")


if __name__ == "__main__":
    main()
