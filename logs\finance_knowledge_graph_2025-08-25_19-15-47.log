2025-08-25 19:15:47,643 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-25_19-15-47.log
2025-08-25 19:15:49,907 - Finance_Knowledge_Graph - INFO - [ingestion.py:198] - ========================================================
2025-08-25 19:15:49,907 - Finance_Knowledge_Graph - INFO - [ingestion.py:199] - = Starting Neo4j Ingestion & Verification Pipeline     =
2025-08-25 19:15:49,912 - Finance_Knowledge_Graph - INFO - [ingestion.py:200] - ========================================================
2025-08-25 19:15:49,913 - Finance_Knowledge_Graph - INFO - [ingestion.py:203] - Selected latest graph file for ingestion: cleaned_llm_output_2025-08-24_16-57-42.json
2025-08-25 19:15:49,913 - Finance_Knowledge_Graph - INFO - [ingestion.py:23] - Connecting to Neo4j database at neo4j+s://aab151a2.databases.neo4j.io...
2025-08-25 19:15:51,489 - Finance_Knowledge_Graph - INFO - [ingestion.py:27] - Neo4j connection successful.
2025-08-25 19:15:51,489 - Finance_Knowledge_Graph - INFO - [ingestion.py:56] - Setting up database constraints...
2025-08-25 19:15:51,493 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'Technology'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,494 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'GeographicRegion'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,494 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'StockExchange'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'GovernmentAgency'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'LawOrRegulation'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'BusinessSegment'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'Person'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'Product'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'IndustrySector'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - ERROR - [ingestion.py:66] - Failed to create constraint for label 'Company'. Error: 'Neo4jIngestor' object has no attribute '_execute_query'
2025-08-25 19:15:51,495 - Finance_Knowledge_Graph - INFO - [ingestion.py:154] - --- Starting Neo4j Ingestion Process ---
2025-08-25 19:15:51,503 - Finance_Knowledge_Graph - INFO - [ingestion.py:79] - Ingesting 9 nodes in batches...
2025-08-25 19:15:51,626 - Finance_Knowledge_Graph - INFO - [ingestion.py:101] - Ingested batch of 9 nodes.
2025-08-25 19:15:51,626 - Finance_Knowledge_Graph - INFO - [ingestion.py:103] - Node ingestion complete.
2025-08-25 19:15:51,626 - Finance_Knowledge_Graph - INFO - [ingestion.py:117] - Ingesting 10 relationships in batches...
2025-08-25 19:15:51,995 - Finance_Knowledge_Graph - INFO - [ingestion.py:144] - Ingested batch of 10 relationships.
2025-08-25 19:15:51,995 - Finance_Knowledge_Graph - INFO - [ingestion.py:146] - Relationship ingestion complete.
2025-08-25 19:15:51,995 - Finance_Knowledge_Graph - INFO - [ingestion.py:170] - --- Neo4j Ingestion Process Finished ---
2025-08-25 19:15:51,995 - Finance_Knowledge_Graph - INFO - [ingestion.py:179] - --- Verifying Relationship Ingestion ---
2025-08-25 19:15:51,995 - Finance_Knowledge_Graph - INFO - [ingestion.py:182] - Querying for a general sample of relationships...
2025-08-25 19:15:52,076 - Finance_Knowledge_Graph - WARNING - [ingestion.py:191] - No relationships found! The ingestion might have failed.
2025-08-25 19:15:52,076 - Finance_Knowledge_Graph - INFO - [ingestion.py:38] - Closing Neo4j connection.
2025-08-25 19:15:52,076 - Finance_Knowledge_Graph - INFO - [ingestion.py:224] - Pipeline completed.
