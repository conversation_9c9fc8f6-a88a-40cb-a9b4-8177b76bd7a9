2025-08-11 22:39:59,319 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-11_22-39-59.log
2025-08-11 22:39:59,319 - Finance_Knowledge_Graph - INFO - [data_retriever.py:125] - --- Running DataRetriever Test ---
2025-08-11 22:39:59,319 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-11 22:40:01,452 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-11 22:40:01,456 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-11 22:40:01,464 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-11 22:40:15,862 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-11 22:40:15,862 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-11 22:40:15,867 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-11 22:40:59,067 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-11 22:40:59,068 - Finance_Knowledge_Graph - INFO - [data_retriever.py:144] - Successfully retrieved 93 chunks for testing.
2025-08-11 22:40:59,068 - Finance_Knowledge_Graph - INFO - [data_retriever.py:145] - --- Sample Chunk ---
