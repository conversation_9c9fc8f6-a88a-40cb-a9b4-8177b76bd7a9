import json
from openai import OpenAI
from typing import List, Dict, Any
from qdrant_client.http import models as qdrant_models

from logger import log
# Import our new prompt and schema definitions
import src.prompts as prompts

class LLMExtractor:
    """
    Manages all interactions with the OpenAI API to extract structured data.
    Loads prompts and schema from an external prompts module for maintainability.
    """

    def __init__(self, api_key: str, model_name: str = "gpt-5-nano"):
        """
        Initializes the LLM Extractor with the OpenAI client.

        Args:
            api_key (str): Your OpenAI API key.
            model_name (str): The OpenAI model to use.
        """
        log.info(f"Initializing LLMExtractor with OpenAI model: {model_name}")
        if not api_key:
            raise ValueError("OpenAI API key cannot be empty.")
        self.client = OpenAI(api_key=api_key)
        self.model_name = model_name

        # Load prompts and schema from our dedicated module
        self.system_prompt = prompts.SYSTEM_PROMPT
        self.user_prompt_template = prompts.USER_PROMPT_TEMPLATE
        self.json_schema = prompts.response_schema

    def _format_chunks_for_prompt(self, chunks: List[qdrant_models.ScoredPoint]) -> str:
        """
        Combines the page_content of retrieved documents into a single string,
        prefixing each chunk with its detailed metadata (company, form, date, section, URL).
        """
        if not chunks:
            return "No context documents found."
        formatted_output = ""
        for i, chunk in enumerate(chunks, 1):
            metadata = chunk.payload.get("metadata", {})
            source_url = metadata.get('source', 'N/A URL') 
            chunk_string = (
                f"--- Source Document {i+1} ---\n"
                f"Source URL: {source_url}\n\n"
                f"Content:\n{chunk.payload.get('page_content', 'Content not available.')}\n"
                f"--- End Document {i+1} ---"
            )
            formatted_output += chunk_string
        return formatted_output.strip()

    def extract_relationships(self, chunks: List[qdrant_models.ScoredPoint]) ->  Dict[str, List[Dict[str, Any]]]:
        """
        Formats chunks, sends them to the OpenAI API, and extracts structured data.

        Args:
            chunks (List[qdrant_models.ScoredPoint]): A list of document chunks to process.

        Returns:
            Dict[str, List[Dict[str, Any]]]: A dictionary with two keys: 'entities' and 'relationships'.
        """
        empty_response = {"entities": [], "relationships":[]}
        if not chunks:
            log.warning("Received an empty list of chunks. Skipping LLM call.")
            return empty_response

        # Format all chunks into a single context string
        formatted_context = self._format_chunks_for_prompt(chunks)
        user_prompt = self.user_prompt_template.format(formatted_chunks=formatted_context)
        
        log.info(f"Sending {len(chunks)} chunks to OpenAI for extraction. Total context length: {len(formatted_context)}")
        try:
            response = self.client.responses.create(
                model=self.model_name,
                input=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                text=self.json_schema
            )
            
            json_string = response.output_text
            if not json_string:
                log.warning("LLM returned an empty response content.")
                return empty_response

            parsed_output = json.loads(json_string) # this is dict
            entities = parsed_output.get("entities", [])
            relationships = parsed_output.get("relationships", [])
            for ent in entities:
                if "name" in ent:
                    ent["name"] = ent["name"].upper()
            for rel in relationships:
                if "source_entity" in rel:
                    rel["source_entity"] = rel["source_entity"].upper()
                if "target_entity" in rel:
                    rel["target_entity"] = rel["target_entity"].upper()
            log.info(f"Successfully extracted {len(entities)} entities and {len(relationships)} relationships.")
            return {"entities": entities,"relationships": relationships}

        except json.JSONDecodeError as e:
            log.error(f"Failed to decode JSON from OpenAI response. Error: {e}. Response text: '{json_string}'")
            return empty_response
        except Exception as e:
            log.error(f"An error occurred during OpenAI API call: {e}", exc_info=True)
            return empty_response

if __name__ == "__main__":
    import os
    from src.data_retriever import DataRetriever
    import config

    # --- Setup ---
    # Ensure your OpenAI API key is in config.py or set as an environment variable
    openai_api_key = getattr(config, "OPENAI_API_KEY", None)
    if not openai_api_key:
        raise RuntimeError("No OpenAI API key found. Set config.OPENAI_API_KEY or the OPENAI_API_KEY environment variable.")

    # Instantiate retriever and extractor
    retriever = DataRetriever(
        qdrant_url=config.QDRANT_URL,
        collection_name=config.QDRANT_COLLECTION_NAME,
        query_file_path=os.path.join(os.path.dirname(__file__), "../data/search_queries.json"),
        model_name=getattr(config, "EMBED_MODEL_NAME", "BAAI/bge-base-en-v1.5"),
    )
    extractor = LLMExtractor(api_key=openai_api_key, model_name="gpt-4.1")

    # --- Fetch chunks for a sample company ---
    company = "NVIDIA CORP"  # You can change this to test other companies
    print(f"Fetching chunks for: {company}")
    chunks = retriever.fetch_relevant_chunks(company_name=company, search_limit=5)
    print(f"Fetched {len(chunks)} chunks.")

    # --- Extract relationships ---
    print(f"Extracting relationships from {len(chunks)} chunks...")
    relationships = extractor.extract_relationships(chunks[:5])
    entities = relationships["entities"]
    relationships = relationships["relationships"]
    print(f"entities: {entities}")
    print(f"relationships: {relationships}")
    print("\n--- End of Extraction ---")
