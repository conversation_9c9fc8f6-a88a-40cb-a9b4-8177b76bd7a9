2025-08-11 17:32:08,481 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-11_17-32-08.log
2025-08-11 17:32:08,494 - Finance_Knowledge_Graph - INFO - [data_retriever.py:131] - --- Running DataRetriever Test ---
2025-08-11 17:32:08,494 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-11 17:32:10,429 - Finance_Knowledge_Graph - INFO - [data_retriever.py:68] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-11 17:32:10,429 - Finance_Knowledge_Graph - INFO - [data_retriever.py:43] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-11 17:32:10,429 - Finance_Knowledge_Graph - INFO - [data_retriever.py:47] - Using device: cpu
2025-08-11 17:32:26,676 - Finance_Knowledge_Graph - INFO - [data_retriever.py:56] - Embedding model initialized successfully.
2025-08-11 17:32:26,676 - Finance_Knowledge_Graph - INFO - [data_retriever.py:37] - DataRetriever initialized successfully.
2025-08-11 17:32:26,676 - Finance_Knowledge_Graph - INFO - [data_retriever.py:85] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-11 17:33:10,718 - Finance_Knowledge_Graph - INFO - [data_retriever.py:123] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-11 17:33:10,718 - Finance_Knowledge_Graph - INFO - [data_retriever.py:150] - Successfully retrieved 93 chunks for testing.
2025-08-11 17:33:10,718 - Finance_Knowledge_Graph - INFO - [data_retriever.py:151] - --- Sample Chunk ---
