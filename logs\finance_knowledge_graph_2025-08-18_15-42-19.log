2025-08-18 15:42:19,013 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-18_15-42-19.log
2025-08-18 15:42:30,120 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:177] - --- Starting UMAP -> HDBSCAN Clustering Analysis ---
2025-08-18 15:42:30,120 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:38] - Loading relationship phrases from single JSON file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\broadcom.json
2025-08-18 15:42:30,120 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:49] - Found 39 unique relationship phrases in JSON.
2025-08-18 15:42:30,120 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-18 15:42:31,941 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-18 15:42:31,946 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-18 15:42:31,946 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-18 15:42:47,470 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-18 15:42:47,470 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-18 15:42:47,470 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:87] - Vectorizing 39 phrases using the BGE model...
2025-08-18 15:42:53,708 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:89] - Vectorization complete. Embedding matrix shape: (39, 768)
2025-08-18 15:42:53,720 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:108] - Starting dimensionality reduction with UMAP from 768 to 5 dimensions...
2025-08-18 15:43:17,108 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:120] - UMAP reduction complete. New matrix shape: (39, 5)
2025-08-18 15:43:17,114 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:131] - Starting clustering with HDBSCAN...
2025-08-18 15:43:17,149 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:145] - HDBSCAN clustering complete.
2025-08-18 15:43:17,149 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:146] - Found 2 clusters and 3 noise points.
2025-08-18 15:43:17,160 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:152] - 
================================================================================
2025-08-18 15:43:17,162 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:153] - --- CLUSTER ANALYSIS RESULTS ---
2025-08-18 15:43:17,162 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:154] - ================================================================================
2025-08-18 15:43:17,249 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:168] - 
Full cluster analysis saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\cluster_analysis_results.csv
2025-08-18 15:43:17,251 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:225] - --- UMAP -> HDBSCAN Clustering Analysis Finished ---
