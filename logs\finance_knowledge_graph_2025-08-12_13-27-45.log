2025-08-12 13:27:45,199 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_13-27-45.log
2025-08-12 13:27:51,731 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 13:27:53,436 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\../data/search_queries.json
2025-08-12 13:27:53,452 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 13:27:53,455 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 13:28:09,832 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 13:28:09,832 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 13:28:09,832 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 13:28:10,549 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 13:28:52,619 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-12 13:28:52,626 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 5 chunks to OpenAI for extraction. Total context length: 22005
2025-08-12 13:29:09,692 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:92] - Successfully extracted 13 relationships from the provided context.
