2025-08-17 21:42:52,031 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_21-42-52.log
2025-08-17 21:43:07,224 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:177] - --- Starting UMAP -> HDBSCAN Clustering Analysis ---
2025-08-17 21:43:07,225 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:23] - Loading relationship phrases from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships.jsonl
2025-08-17 21:43:07,227 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:35] - Found 8 unique relationship phrases.
2025-08-17 21:43:07,228 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 21:43:09,767 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 21:43:09,768 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 21:43:09,769 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 21:43:26,017 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 21:43:26,018 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 21:43:26,018 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:50] - Vectorizing 8 phrases using the BGE model...
2025-08-17 21:43:30,668 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:52] - Vectorization complete. Embedding matrix shape: (8, 768)
2025-08-17 21:43:30,670 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:71] - Starting dimensionality reduction with UMAP from 768 to 5 dimensions...
2025-08-17 21:43:59,082 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:83] - UMAP reduction complete. New matrix shape: (8, 5)
2025-08-17 21:43:59,082 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:94] - Starting clustering with HDBSCAN...
2025-08-17 21:43:59,122 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:108] - HDBSCAN clustering complete.
2025-08-17 21:43:59,122 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:109] - Found 2 clusters and 0 noise points.
2025-08-17 21:43:59,122 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:71] - Starting dimensionality reduction with UMAP from 768 to 2 dimensions...
2025-08-17 21:43:59,147 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:83] - UMAP reduction complete. New matrix shape: (8, 2)
2025-08-17 21:43:59,157 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:115] - 
================================================================================
2025-08-17 21:43:59,158 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:116] - --- CLUSTER ANALYSIS RESULTS ---
2025-08-17 21:43:59,158 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:117] - ================================================================================
2025-08-17 21:43:59,200 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:131] - 
Full cluster analysis saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\cluster_analysis_results.csv
2025-08-17 21:43:59,205 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:141] - Generating 2D cluster visualization...
2025-08-17 21:44:00,525 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:169] - Cluster visualization saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\cluster_visualization.png
2025-08-17 21:44:00,551 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:226] - --- UMAP -> HDBSCAN Clustering Analysis Finished ---
