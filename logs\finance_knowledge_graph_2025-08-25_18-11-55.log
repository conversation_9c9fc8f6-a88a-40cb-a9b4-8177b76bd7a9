2025-08-25 18:11:55,867 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-25_18-11-55.log
2025-08-25 18:11:58,872 - Finance_Knowledge_Graph - INFO - [ingestion.py:160] - ========================================================
2025-08-25 18:11:58,872 - Finance_Knowledge_Graph - INFO - [ingestion.py:161] - = Starting Neo4j Ingestion Pipeline                    =
2025-08-25 18:11:58,873 - Finance_Knowledge_Graph - INFO - [ingestion.py:162] - ========================================================
2025-08-25 18:11:58,873 - Finance_Knowledge_Graph - INFO - [ingestion.py:170] - Selected latest graph file for ingestion: cleaned_llm_output_2025-08-24_16-57-42.json
2025-08-25 18:11:58,874 - Finance_Knowledge_Graph - INFO - [ingestion.py:23] - Connecting to Neo4j database at neo4j+s://aab151a2.databases.neo4j.io...
2025-08-25 18:12:00,404 - Finance_Knowledge_Graph - INFO - [ingestion.py:27] - Neo4j connection successful.
2025-08-25 18:12:00,404 - Finance_Knowledge_Graph - INFO - [ingestion.py:53] - Setting up database constraints...
2025-08-25 18:12:00,563 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GovernmentAgency' is set.
2025-08-25 18:12:00,644 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Person' is set.
2025-08-25 18:12:00,720 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'GeographicRegion' is set.
2025-08-25 18:12:00,805 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'BusinessSegment' is set.
2025-08-25 18:12:00,890 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Company' is set.
2025-08-25 18:12:00,970 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Product' is set.
2025-08-25 18:12:01,056 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'StockExchange' is set.
2025-08-25 18:12:01,139 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'LawOrRegulation' is set.
2025-08-25 18:12:01,233 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'IndustrySector' is set.
2025-08-25 18:12:01,335 - Finance_Knowledge_Graph - INFO - [ingestion.py:61] - Constraint for label 'Technology' is set.
2025-08-25 18:12:01,336 - Finance_Knowledge_Graph - INFO - [ingestion.py:139] - --- Starting Neo4j Ingestion Process ---
2025-08-25 18:12:01,337 - Finance_Knowledge_Graph - INFO - [ingestion.py:76] - Ingesting 9 nodes in batches...
2025-08-25 18:12:01,643 - Finance_Knowledge_Graph - INFO - [ingestion.py:98] - Ingested batch of 9 nodes.
2025-08-25 18:12:01,644 - Finance_Knowledge_Graph - INFO - [ingestion.py:100] - Node ingestion complete.
2025-08-25 18:12:01,646 - Finance_Knowledge_Graph - INFO - [ingestion.py:113] - Ingesting 10 relationships in batches...
2025-08-25 18:12:01,838 - Finance_Knowledge_Graph - INFO - [ingestion.py:131] - Ingested batch of 10 relationships.
2025-08-25 18:12:01,838 - Finance_Knowledge_Graph - INFO - [ingestion.py:133] - Relationship ingestion complete.
2025-08-25 18:12:01,839 - Finance_Knowledge_Graph - INFO - [ingestion.py:155] - --- Neo4j Ingestion Process Finished ---
2025-08-25 18:12:01,839 - Finance_Knowledge_Graph - INFO - [ingestion.py:38] - Closing Neo4j connection.
2025-08-25 18:12:01,841 - Finance_Knowledge_Graph - INFO - [ingestion.py:188] - Ingestion pipeline completed.
