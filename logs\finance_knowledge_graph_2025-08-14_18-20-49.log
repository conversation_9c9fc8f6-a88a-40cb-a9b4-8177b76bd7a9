2025-08-14 18:20:49,078 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_18-20-49.log
2025-08-14 18:21:05,187 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-14 18:21:05,187 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-14 18:21:05,187 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-14 18:21:05,195 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-14 18:21:05,195 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-14 18:21:07,933 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-14 18:21:07,934 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-14 18:21:07,934 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-14 18:21:58,756 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-14 18:21:58,756 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-14 18:21:58,756 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-14 18:21:59,740 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-14 18:21:59,740 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-14 18:21:59,740 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-14 18:21:59,744 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-14 18:21:59,744 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-14 18:21:59,746 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: INTEL CORP ---
2025-08-14 18:21:59,746 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-14 18:22:51,545 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-14 18:22:51,555 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 2 chunks to OpenAI for extraction. Total context length: 3937
2025-08-14 18:24:16,228 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:91] - Successfully extracted 6 relationships from the provided context.
2025-08-14 18:24:16,228 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 6 raw relationships for INTEL CORP.
2025-08-14 18:24:16,228 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - --- Pipeline Run Finished ---
2025-08-14 18:24:16,228 - Finance_Knowledge_Graph - INFO - [pipeline.py:127] - Total clean relationships found and saved: 0
2025-08-14 18:24:16,233 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Saving updated canonical maps to disk...
2025-08-14 18:24:16,234 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-14 18:24:16,234 - Finance_Knowledge_Graph - INFO - [pipeline.py:130] - Canonical maps saved successfully.
2025-08-14 18:24:16,237 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-14 18:24:16,237 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-14 18:24:16,237 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
