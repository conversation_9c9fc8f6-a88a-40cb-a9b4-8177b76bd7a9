2025-08-20 18:15:09,135 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-20_18-15-09.log
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:190] - ========================================================
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:191] - = Starting Entity Resolution Pipeline                  =
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:192] - ========================================================
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:202] - Selected latest raw file for processing: clean_relationships_2025-08-19_14-26-57.jsonl
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:46] - Initializing Entity Resolution Pipeline...
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:22] - Initializing CompanyNormalizer...
2025-08-20 18:15:09,257 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-20 18:15:09,298 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:54] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 18:15:09,305 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:57] - Company map file is empty, Starting fresh
2025-08-20 18:15:09,305 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:34] - CompanyNormalizer initialized successfully.
2025-08-20 18:15:09,305 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:58] - Entity Resolution Pipeline initialized.
2025-08-20 18:15:09,305 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:100] - Starting processing for input file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships_2025-08-19_14-26-57.jsonl
2025-08-20 18:15:09,305 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:171] - Processing complete. Found 0 unique nodes and 0 relationships.
2025-08-20 18:15:09,313 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:172] - Clean graph data saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\graph_ready_output_2025-08-20_18-15-09.json
2025-08-20 18:15:09,313 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:69] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-20 18:15:09,313 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:182] - All canonical maps have been saved.
2025-08-20 18:15:09,321 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:224] - Entity resolution pipeline completed successfully.
