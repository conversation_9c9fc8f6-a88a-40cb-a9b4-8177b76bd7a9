2025-08-17 19:15:15,567 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_19-15-15.log
2025-08-17 19:15:27,398 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:146] - --- Starting Full Clustering Analysis ---
2025-08-17 19:15:27,398 - Finance_Knowledge_Graph - INFO - [cluster_analysis.py:21] - Loading relationship phrases from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships.jsonl
2025-08-17 19:15:27,401 - Finance_Knowledge_Graph - ERROR - [cluster_analysis.py:39] - Error decoding JSON from file C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\clean_relationships.jsonl. Error: Expecting value: line 2 column 1 (char 1)
2025-08-17 19:15:27,403 - Finance_Knowledge_Graph - ERROR - [cluster_analysis.py:159] - No relationship phrases found. Aborting analysis.
