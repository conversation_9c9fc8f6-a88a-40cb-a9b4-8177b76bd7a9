2025-08-28 12:52:24,973 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-28_12-52-24.log
2025-08-28 12:52:33,338 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:203] - ========================================================
2025-08-28 12:52:33,339 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:204] - = Starting Unified Entity Resolution & Cleaning Pipeline =
2025-08-28 12:52:33,340 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:205] - ========================================================
2025-08-28 12:52:33,340 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:214] - Selected latest raw file for processing: clean_relationships_2025-08-27_22-07-42.jsonl
2025-08-28 12:52:33,341 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:27] - Initializing Entity Resolution Pipeline...
2025-08-28 12:52:33,341 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:45] - Collecting all unique entities and all relationships from clean_relationships_2025-08-27_22-07-42.jsonl...
2025-08-28 12:52:33,375 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:59] - Collected 180 unique entities and loaded 318 relationships.
2025-08-28 12:52:33,376 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:34] - Entity Resolution Pipeline initialized.
2025-08-28 12:52:33,376 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:70] - --- Starting Pass 1: The Learning Pass ---
2025-08-28 12:52:33,378 - Finance_Knowledge_Graph - INFO - [other_norm.py:433] - 
--- Starting Normalization Pass for 180 total entities ---
2025-08-28 12:52:33,380 - Finance_Knowledge_Graph - INFO - [other_norm.py:438] - Separated entities: 42 Companies, 138 Others.
2025-08-28 12:52:33,380 - Finance_Knowledge_Graph - INFO - [other_norm.py:442] - --- Processing Company Entities ---
2025-08-28 12:52:33,381 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:18] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-28 12:52:33,382 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:42] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-28 12:52:33,444 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:57] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-28 12:52:33,446 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - CompanyNormalizer initialized with 25 canonical companies.
2025-08-28 12:52:33,446 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'NVIDIA Corporation' -> 'NVDA'
2025-08-28 12:52:33,447 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Advanced Micro Devices, Inc.' -> 'AMD'
2025-08-28 12:52:33,447 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Huawei Technologies Co. Ltd.' -> 'MHUA'
2025-08-28 12:52:33,447 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Intel Corporation' -> 'INTC'
2025-08-28 12:52:33,448 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Alibaba Group' -> 'BABA'
2025-08-28 12:52:33,448 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Alphabet Inc.' -> 'GOOGL'
2025-08-28 12:52:33,449 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Amazon, Inc.' -> 'AAON'
2025-08-28 12:52:33,449 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Baidu, Inc.' -> 'BIDU'
2025-08-28 12:52:33,450 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Microsoft Corporation' -> 'INTC'
2025-08-28 12:52:33,451 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Ambarella, Inc.' -> 'AMBA'
2025-08-28 12:52:33,451 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Broadcom Inc.' -> 'AVGO'
2025-08-28 12:52:33,625 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Qualcomm Incorporated'. No match in local map or SEC list.
2025-08-28 12:52:33,625 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Qualcomm Incorporated' -> 'UNRESOLVED_COMPANY_QUALCOMM_INCORPORATED'
2025-08-28 12:52:33,824 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Renesas Electronics Corporation'. No match in local map or SEC list.
2025-08-28 12:52:33,824 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Renesas Electronics Corporation' -> 'UNRESOLVED_COMPANY_RENESAS_ELECTRONICS_CORPORATION'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Samsung Electronics Co., Ltd.'. No match in local map or SEC list.
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Samsung Electronics Co., Ltd.' -> 'UNRESOLVED_COMPANY_SAMSUNG_ELECTRONICS_CO,_LTD'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Tesla, Inc.' -> 'TSLA'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Arista Networks, Inc.' -> 'ANET'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Cisco Systems, Inc.' -> 'CSCO'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Hewlett Packard Enterprise Company' -> 'HPE'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Lumentum Holdings, Inc.' -> 'LITE'
2025-08-28 12:52:34,022 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Marvell Technology Group' -> 'MRVL'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Samsung Electronics Co. Ltd.'. No match in local map or SEC list.
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Samsung Electronics Co. Ltd.' -> 'UNRESOLVED_COMPANY_SAMSUNG_ELECTRONICS_CO_LTD'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Lumentum Holdings Inc.' -> 'LITE'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Marvell Technology, Inc.' -> 'MRVL'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Mellanox Technologies, Ltd.' -> 'TATT'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Mellanox Technologies' -> 'TATT'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'NVIDIA CORP' -> 'NVDA'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Taiwan Semiconductor Manufacturing Company Limited' -> 'TSM'
2025-08-28 12:52:34,222 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Micron Technology, Inc.' -> 'MRVL'
2025-08-28 12:52:34,410 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'SK hynix Inc.'. No match in local map or SEC list.
2025-08-28 12:52:34,410 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'SK hynix Inc.' -> 'UNRESOLVED_COMPANY_SK_HYNIX_INC'
2025-08-28 12:52:34,623 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Hon Hai Precision Industry Co., Ltd.'. No match in local map or SEC list.
2025-08-28 12:52:34,639 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Hon Hai Precision Industry Co., Ltd.' -> 'UNRESOLVED_COMPANY_HON_HAI_PRECISION_INDUSTRY_CO,_LTD'
2025-08-28 12:52:34,639 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Wistron Corporation' -> 'INTC'
2025-08-28 12:52:34,639 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Fabrinet' -> 'FN'
2025-08-28 12:52:34,792 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Third party'. No match in local map or SEC list.
2025-08-28 12:52:34,792 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Third party' -> 'UNRESOLVED_COMPANY_THIRD_PARTY'
2025-08-28 12:52:34,976 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer A'. No match in local map or SEC list.
2025-08-28 12:52:34,976 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer A' -> 'UNRESOLVED_COMPANY_CUSTOMER_A'
2025-08-28 12:52:35,158 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer B'. No match in local map or SEC list.
2025-08-28 12:52:35,158 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer B' -> 'UNRESOLVED_COMPANY_CUSTOMER_B'
2025-08-28 12:52:35,324 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer C'. No match in local map or SEC list.
2025-08-28 12:52:35,324 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer C' -> 'UNRESOLVED_COMPANY_CUSTOMER_C'
2025-08-28 12:52:35,509 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer D'. No match in local map or SEC list.
2025-08-28 12:52:35,509 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer D' -> 'UNRESOLVED_COMPANY_CUSTOMER_D'
2025-08-28 12:52:35,675 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Customer E'. No match in local map or SEC list.
2025-08-28 12:52:35,675 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Customer E' -> 'UNRESOLVED_COMPANY_CUSTOMER_E'
2025-08-28 12:52:35,873 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer A'. No match in local map or SEC list.
2025-08-28 12:52:35,873 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer A' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_A'
2025-08-28 12:52:36,057 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer B'. No match in local map or SEC list.
2025-08-28 12:52:36,057 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer B' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_B'
2025-08-28 12:52:36,240 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Direct Customer C'. No match in local map or SEC list.
2025-08-28 12:52:36,240 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Direct Customer C' -> 'UNRESOLVED_COMPANY_DIRECT_CUSTOMER_C'
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'Acquired Company'. No match in local map or SEC list.
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - INFO - [other_norm.py:454] - Resolved Company 'Acquired Company' -> 'UNRESOLVED_COMPANY_ACQUIRED_COMPANY'
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - INFO - [other_norm.py:460] - Company resolution review map saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\review_company_nodes.json
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:84] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:96] - Successfully saved 25 companies to the map.
2025-08-28 12:52:36,423 - Finance_Knowledge_Graph - INFO - [other_norm.py:466] - --- Finished Processing Company Entities ---
2025-08-28 12:52:36,438 - Finance_Knowledge_Graph - INFO - [other_norm.py:470] - --- Processing Other Entity Types ---
2025-08-28 12:55:17,492 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:81] - The Learning Pass failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 473, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 358, in _Update_alias
    llm_result = call_gemini_api(system_prompt=system_prompt,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 152, in call_gemini_api
    response = client.models.generate_content(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\models.py", line 5630, in generate_content
    response = self._generate_content(
               ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\models.py", line 4593, in _generate_content
    response_dict = self._api_client.request(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\_api_client.py", line 755, in request
    response = self._request(http_request, stream=False)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\_api_client.py", line 684, in _request
    errors.APIError.raise_for_response(response)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\errors.py", line 101, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 429 RESOURCE_EXHAUSTED. {'error': {'code': 429, 'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.', 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.QuotaFailure', 'violations': [{'quotaMetric': 'generativelanguage.googleapis.com/generate_content_free_tier_requests', 'quotaId': 'GenerateRequestsPerMinutePerProjectPerModel-FreeTier', 'quotaDimensions': {'location': 'global', 'model': 'gemini-2.5-flash-lite'}, 'quotaValue': '15'}]}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Learn more about Gemini API quotas', 'url': 'https://ai.google.dev/gemini-api/docs/rate-limits'}]}, {'@type': 'type.googleapis.com/google.rpc.RetryInfo', 'retryDelay': '42s'}]}}
2025-08-28 12:55:17,555 - Finance_Knowledge_Graph - ERROR - [entity_resolution.py:248] - The entity resolution pipeline failed with a critical error.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 232, in main
    pipeline.run_learning_pass()
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\entity_resolution.py", line 72, in run_learning_pass
    run_normalization_pass(
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 473, in run_normalization_pass
    _Update_alias(matched_entities, alias_path_others)
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 358, in _Update_alias
    llm_result = call_gemini_api(system_prompt=system_prompt,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\other_norm.py", line 152, in call_gemini_api
    response = client.models.generate_content(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\models.py", line 5630, in generate_content
    response = self._generate_content(
               ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\models.py", line 4593, in _generate_content
    response_dict = self._api_client.request(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\_api_client.py", line 755, in request
    response = self._request(http_request, stream=False)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\_api_client.py", line 684, in _request
    errors.APIError.raise_for_response(response)
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\google\genai\errors.py", line 101, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 429 RESOURCE_EXHAUSTED. {'error': {'code': 429, 'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.', 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.QuotaFailure', 'violations': [{'quotaMetric': 'generativelanguage.googleapis.com/generate_content_free_tier_requests', 'quotaId': 'GenerateRequestsPerMinutePerProjectPerModel-FreeTier', 'quotaDimensions': {'location': 'global', 'model': 'gemini-2.5-flash-lite'}, 'quotaValue': '15'}]}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Learn more about Gemini API quotas', 'url': 'https://ai.google.dev/gemini-api/docs/rate-limits'}]}, {'@type': 'type.googleapis.com/google.rpc.RetryInfo', 'retryDelay': '42s'}]}}
