import json

input_path = "clean_relationships_2025-08-27_22-07-42.jsonl"
output_path = "clean_relationships_2025-08-27_22-07-42.json"

objects = []
with open(input_path, "r", encoding="utf-8") as infile:
    for line in infile:
        if line.strip():
            obj = json.loads(line)
            objects.append(obj)

with open(output_path, "w", encoding="utf-8") as outfile:
    json.dump(objects, outfile, indent=2, ensure_ascii=False)