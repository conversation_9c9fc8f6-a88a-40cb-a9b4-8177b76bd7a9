import json
from typing import List, Dict, Any

import torch
from qdrant_client import QdrantClient, models
from langchain_huggingface import HuggingFaceEmbeddings

from logger import log

class DataRetriever:
    """
    Handles all communication with Qdrant to fetch relevant document chunks.
    It uses the specified HuggingFace model to generate query embeddings.
    """

    def __init__(self, qdrant_url: str, collection_name: str, query_file_path: str, model_name: str = 'BAAI/bge-base-en-v1.5'):
        """
        Initializes the DataRetriever with database and model configurations.

        Args:
            qdrant_url (str): The URL of the Qdrant instance.
            collection_name (str): The name of the collection in Qdrant.
            query_file_path (str): The file path to the JSON file of search queries.
            model_name (str): The name of the HuggingFace model to use for embeddings.
        """
        log.info("Initializing DataRetriever...")
        self.qdrant_client = QdrantClient(qdrant_url, timeout=80)
        self.collection_name = collection_name
        self.queries = DataRetriever._load_queries(query_file_path)
        
        self.embedding_model = DataRetriever._initialize_embedding_model(model_name)
        
        if not self.embedding_model:
            raise RuntimeError("Failed to initialize the embedding model. Cannot proceed.")
        log.info("DataRetriever initialized successfully.")

    @staticmethod
    def _initialize_embedding_model(model_name: str) -> HuggingFaceEmbeddings | None:
        """
        Initializes the HuggingFace embedding model, mirroring the ingestion setup.
        """
        log.info(f"--- Initializing Embedding Model: {model_name} ---")
        try:
            # Auto-detect device (CUDA for GPU, MPS for Apple Silicon, CPU as fallback)
            device = 'cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu'
            log.info(f"Using device: {device}")
            embedding_model = HuggingFaceEmbeddings(
                model_name=model_name,
                model_kwargs={'device': device},
                encode_kwargs={'normalize_embeddings': True} 
            )
            log.info("Embedding model initialized successfully.")
            return embedding_model
        except Exception as e:
            log.error(f"FATAL: Error initializing embedding model: {e}", exc_info=True)
            return None

    @staticmethod
    def _load_queries(file_path: str) -> List[Dict[str, Any]]:
        """
        Loads the search queries from the specified JSON file.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                log.info(f"Loading search queries from: {file_path}")
                return json.load(f)
        except FileNotFoundError:
            log.error(f"FATAL: Query file not found at {file_path}")
            raise

    def fetch_relevant_chunks(self, company_name: str, search_limit: int) -> List[models.ScoredPoint]:
        """
        Fetches and deduplicates relevant chunks for a specific company from Qdrant.

        Args:
            company_name (str): The name of the company to search for.
            search_limit (int): The number of results to return for each query.

        Returns:
            List[models.ScoredPoint]: A deduplicated list of Qdrant point objects.
        """
        log.info(f"Starting to fetch chunks for company: '{company_name}'")
        
        retrieved_chunks: Dict[str, models.ScoredPoint] = {}

        all_query_templates = [q for category in self.queries for q in category.get("queries", [])]
        
        for query_template in all_query_templates:
            formatted_query = query_template.format(company=company_name)
            log.debug(f"Running query: '{formatted_query}'")

            query_vector = self.embedding_model.embed_query(formatted_query)
            try:
                hits = self.qdrant_client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="metadata.company_name",
                                match=models.MatchValue(value=company_name),
                            )
                        ]
                    ),
                    limit=search_limit,
                    with_payload=True
                )
                
                # 3. Add results to our dictionary to handle duplicates
                for hit in hits:
                    if hit.id not in retrieved_chunks:
                        retrieved_chunks[hit.id] = hit
            except Exception as e:
                log.error(f"Qdrant search failed for query '{formatted_query}'. Error: {e}", exc_info=True)
                continue # Move to the next query

        log.info(f"Fetched a total of {len(retrieved_chunks)} unique chunks for '{company_name}'.")
        return list(retrieved_chunks.values())



if __name__ == '__main__':
    from config import QDRANT_URL, QDRANT_COLLECTION_NAME, SEARCH_QUERIES_PATH, QDRANT_SEARCH_LIMIT

    log.info("--- Running DataRetriever Test ---")
    
    try:
        # 1. Create an instance of our retriever
        retriever = DataRetriever(
            qdrant_url=QDRANT_URL,
            collection_name=QDRANT_COLLECTION_NAME,
            query_file_path=SEARCH_QUERIES_PATH
        )

        # 2. Test fetching data for one company
        test_company = "NVIDIA CORP"
        chunks = retriever.fetch_relevant_chunks(
            company_name=test_company,
            search_limit=QDRANT_SEARCH_LIMIT
        )

        # 3. Print the results to verify
        if chunks:
            log.info(f"Successfully retrieved {len(chunks)} chunks for testing.")
            log.info("--- Sample Chunk ---")
            sample_chunk = chunks[0]
            print(f"ID: {sample_chunk.id}")
            print(f"Score: {sample_chunk.score}")
            # Ensure the payload has 'page_content' before accessing it
            if sample_chunk.payload and 'page_content' in sample_chunk.payload:
                print(f"Content Snippet: {sample_chunk.payload['page_content'][:200]}...")
            else:
                print("Payload content not found.")
            print("-" * 20)
        else:
            log.warning("Test run completed, but no chunks were retrieved.")

    except Exception as e:
        log.error(f"An error occurred during the test run: {e}", exc_info=True)