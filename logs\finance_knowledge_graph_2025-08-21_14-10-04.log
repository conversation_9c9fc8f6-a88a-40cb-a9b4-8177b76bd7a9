2025-08-21 14:10:04,068 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-21_14-10-04.log
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:185] - ========================================================
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:186] - = Starting Entity Resolution Pipeline                  =
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:187] - ========================================================
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:197] - Selected latest raw file for processing: sample_relationship.jsonl
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:40] - Initializing Entity Resolution Pipeline...
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:18] - Initializing CompanyNormalizer with new list-based map strategy...
2025-08-21 14:10:04,225 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:42] - Loading SEC master ticker list from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\tickers.json
2025-08-21 14:10:04,296 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:57] - Loading company canonical map from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-21 14:10:04,296 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:38] - CompanyNormalizer initialized with 4 canonical companies.
2025-08-21 14:10:04,296 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:49] - Entity Resolution Pipeline initialized.
2025-08-21 14:10:04,296 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:74] - Starting processing for input file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\sample_relationship.jsonl
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - WARNING - [company_normalizer.py:233] - Could not resolve company 'AXFJK'. No match in local map or SEC list.
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:166] - Processing complete. Found 5 unique nodes and 4 relationships.
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:167] - Clean graph data saved to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\output\graph_ready_output_2025-08-21_14-10-04.json
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:84] - Saving updated company canonical map to: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\company_canonical_map.json
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [company_normalizer.py:96] - Successfully saved 4 companies to the map.
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:177] - All canonical maps have been saved.
2025-08-21 14:10:04,473 - Finance_Knowledge_Graph - INFO - [entity_resolution.py:222] - Entity resolution pipeline completed successfully.
