2025-08-14 18:03:09,329 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-14_18-03-09.log
2025-08-14 18:03:39,706 - Finance_Knowledge_Graph - INFO - [srl_experiment.py:114] - --- Starting SRL Experiment on Live Data (Verified Model) ---
2025-08-14 18:03:39,706 - Finance_Knowledge_Graph - INFO - [srl_experiment.py:22] - Initializing Hugging Face SRL pipeline with model 'liaad/srl-en_xlmr-base'...
2025-08-14 18:23:05,719 - Finance_Knowledge_Graph - ERROR - [srl_experiment.py:33] - Failed to initialize SRL model. Error: Converting from Tiktoken failed, if a converter for SentencePiece is available, provide a model path with a SentencePiece tokenizer.model file.Currently available slow->fast convertors: ['AlbertTokenizer', 'BartTokenizer', 'BarthezTokenizer', 'BertTokenizer', 'BigBirdTokenizer', '<PERSON>len<PERSON>botTokenizer', 'CamembertTokenizer', 'CLIPTokenizer', 'CodeGenTokenizer', 'ConvBertTokenizer', 'DebertaTokenizer', 'DebertaV2Tokenizer', 'DistilBertTokenizer', 'DPRReaderTokenizer', 'DPRQuestionEncoderTokenizer', 'DPRContextEncoderTokenizer', 'ElectraTokenizer', 'FNetTokenizer', 'FunnelTokenizer', 'GPT2Tokenizer', 'HerbertTokenizer', 'LayoutLMTokenizer', 'LayoutLMv2Tokenizer', 'LayoutLMv3Tokenizer', 'LayoutXLMTokenizer', 'LongformerTokenizer', 'LEDTokenizer', 'LxmertTokenizer', 'MarkupLMTokenizer', 'MBartTokenizer', 'MBart50Tokenizer', 'MPNetTokenizer', 'MobileBertTokenizer', 'MvpTokenizer', 'NllbTokenizer', 'OpenAIGPTTokenizer', 'PegasusTokenizer', 'Qwen2Tokenizer', 'RealmTokenizer', 'ReformerTokenizer', 'RemBertTokenizer', 'RetriBertTokenizer', 'RobertaTokenizer', 'RoFormerTokenizer', 'SeamlessM4TTokenizer', 'SqueezeBertTokenizer', 'T5Tokenizer', 'UdopTokenizer', 'WhisperTokenizer', 'XLMRobertaTokenizer', 'XLNetTokenizer', 'SplinterTokenizer', 'XGLMTokenizer', 'LlamaTokenizer', 'CodeLlamaTokenizer', 'GemmaTokenizer', 'Phi3Tokenizer']
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\convert_slow_tokenizer.py", line 1636, in convert_slow_tokenizer
    ).converted()
      ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\convert_slow_tokenizer.py", line 1533, in converted
    tokenizer = self.tokenizer()
                ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\convert_slow_tokenizer.py", line 1526, in tokenizer
    vocab_scores, merges = self.extract_vocab_merges_from_model(self.vocab_file)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\convert_slow_tokenizer.py", line 1502, in extract_vocab_merges_from_model
    bpe_ranks = load_tiktoken_bpe(tiktoken_url)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\tiktoken\load.py", line 144, in load_tiktoken_bpe
    contents = read_file_cached(tiktoken_bpe_file, expected_hash)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\tiktoken\load.py", line 48, in read_file_cached
    cache_key = hashlib.sha1(blobpath.encode()).hexdigest()
                             ^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'encode'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\srl_experiment.py", line 25, in initialize_srl_model
    srl_pipeline = pipeline(
                   ^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\pipelines\__init__.py", line 1047, in pipeline
    tokenizer = AutoTokenizer.from_pretrained(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\models\auto\tokenization_auto.py", line 953, in from_pretrained
    return tokenizer_class_fast.from_pretrained(pretrained_model_name_or_path, *inputs, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\tokenization_utils_base.py", line 2036, in from_pretrained
    return cls._from_pretrained(
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\tokenization_utils_base.py", line 2276, in _from_pretrained
    tokenizer = cls(*init_inputs, **init_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\models\xlm_roberta\tokenization_xlm_roberta_fast.py", line 108, in __init__
    super().__init__(
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\tokenization_utils_fast.py", line 139, in __init__
    fast_tokenizer = convert_slow_tokenizer(self, from_tiktoken=True)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\transformers\convert_slow_tokenizer.py", line 1638, in convert_slow_tokenizer
    raise ValueError(
ValueError: Converting from Tiktoken failed, if a converter for SentencePiece is available, provide a model path with a SentencePiece tokenizer.model file.Currently available slow->fast convertors: ['AlbertTokenizer', 'BartTokenizer', 'BarthezTokenizer', 'BertTokenizer', 'BigBirdTokenizer', 'BlenderbotTokenizer', 'CamembertTokenizer', 'CLIPTokenizer', 'CodeGenTokenizer', 'ConvBertTokenizer', 'DebertaTokenizer', 'DebertaV2Tokenizer', 'DistilBertTokenizer', 'DPRReaderTokenizer', 'DPRQuestionEncoderTokenizer', 'DPRContextEncoderTokenizer', 'ElectraTokenizer', 'FNetTokenizer', 'FunnelTokenizer', 'GPT2Tokenizer', 'HerbertTokenizer', 'LayoutLMTokenizer', 'LayoutLMv2Tokenizer', 'LayoutLMv3Tokenizer', 'LayoutXLMTokenizer', 'LongformerTokenizer', 'LEDTokenizer', 'LxmertTokenizer', 'MarkupLMTokenizer', 'MBartTokenizer', 'MBart50Tokenizer', 'MPNetTokenizer', 'MobileBertTokenizer', 'MvpTokenizer', 'NllbTokenizer', 'OpenAIGPTTokenizer', 'PegasusTokenizer', 'Qwen2Tokenizer', 'RealmTokenizer', 'ReformerTokenizer', 'RemBertTokenizer', 'RetriBertTokenizer', 'RobertaTokenizer', 'RoFormerTokenizer', 'SeamlessM4TTokenizer', 'SqueezeBertTokenizer', 'T5Tokenizer', 'UdopTokenizer', 'WhisperTokenizer', 'XLMRobertaTokenizer', 'XLNetTokenizer', 'SplinterTokenizer', 'XGLMTokenizer', 'LlamaTokenizer', 'CodeLlamaTokenizer', 'GemmaTokenizer', 'Phi3Tokenizer']
