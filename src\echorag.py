import json
import asyncio
import time, os
from typing import List
from Eco_RAG.utils import call_gemini_api,Normalizer, json_schema_from_basemodel, _process_single_article
from Eco_RAG.prompt import prompts
from Eco_RAG.schema import FilteredArticlesOutput, Ent_Re<PERSON>

def filter_articles_llm(
    articles: List,  # Your list of Article objects
    company_name: str,
    model_name: str = "gemini-2.5-flash",
    stream: bool = False,
) -> List:
    """
    Filters a list of articles for economic relevance and removes duplicates
    using a single, powerful, two-stage LLM prompt.

    Args:
        articles: The list of original Article objects to filter.
        company_name: The name of the company to check for relevance.
        model_name: The Gemini model to use.

    Returns:
        A new list containing only the unique, economically relevant Article objects.
    """
    if not articles:
        return []

    output_schema = json_schema_from_basemodel(FilteredArticlesOutput, mode="gemini")
    schema_as_text = json.dumps(output_schema, indent=2)
    system_prompt = prompts["article_filtering"].format(schema_as_text=schema_as_text,company_name=company_name)
    
    # 1. Prepare the input data for the LLM
    input_data_for_llm = [
        {"id": i, "summary": article.summary}
        for i, article in enumerate(articles)
    ]
    user_prompt = json.dumps(input_data_for_llm, indent=2)
    
    # 2. Get the required JSON schema for both the prompt and the API call

    print(f"Sending {len(articles)} articles to Gemini for two-stage filtering...")

    
    llm_response = call_gemini_api(
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        schema=output_schema,  # Still pass the schema object to the API
        model_name=model_name,
        thinking_token=-1,
        process_name=f"FilterAndDedupe for {company_name}",
        stream=stream,
    )

    if not llm_response or "content" not in llm_response:
        print("Error: Received no content from the LLM.")
        return []
        
    llm_output_data = llm_response["content"]

    # 5. Validate the output and build the final list
    final_filtered_articles = []
    original_summary_map = {i: article.summary for i, article in enumerate(articles)}

    for returned_article in llm_output_data["relevant_articles"]:
        article_id = returned_article.get("id")
        returned_summary = returned_article.get("summary")

        if article_id is not None and article_id in original_summary_map:
            if original_summary_map[article_id].lower() == returned_summary.lower():
                final_filtered_articles.append(articles[article_id])
            else:
                print(f"Validation Error: LLM returned a modified summary for id {article_id}.")
        else:
            print(f"Validation Error: LLM returned an invalid id {article_id}.")


    print(f"Filtering complete. Kept {len(final_filtered_articles)} unique, relevant articles.")
    return final_filtered_articles


async def extract_entities_and_relations(
    json_file_path: str
    ) -> List[dict]:
    """
    Reads articles from a JSON file, extracts entities and relations in batches,
    and adds the article link to the extracted data.

    Args:
        json_file_path: Path to the input JSON file.
        model_name: The Gemini model to use.

    Returns:
        A list of dictionaries, where each dictionary contains the extracted
        entities, relations, and the source article link.
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            articles = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error reading or parsing {json_file_path}: {e}")
        return []

    all_extractions = []
    batch_size = 10

    for i in range(0, len(articles), batch_size):
        batch = articles[i:i+batch_size]
        print(f"Processing batch {i//batch_size + 1}...")
        start=time.time()
        tasks = [
            _process_single_article(article)
            for article in batch
            if isinstance(article.get("content"), str)
        ]
        
        results = await asyncio.gather(*tasks)
        print(time.time()-start)
        # time.sleep(45)

        
        # Filter out any None results from failed API calls
        successful_results = [res for res in results if res]
        all_extractions.extend(successful_results)

    print(f"Extraction complete. Processed {len(all_extractions)} articles successfully.")
    return all_extractions



def entity_resolution(jsonl_path: str, alias_path: str):
    """
    Reads a JSONL file, extracts entities from each line, and normalizes them synchronously.

    Args:
        jsonl_path: Path to the input JSONL data file.
        alias_path: Path to the aliases.json knowledge base file.
    """
    if not os.path.exists(jsonl_path):
        print(f"Error: Input data file not found at '{jsonl_path}'.")
        return

    # Ensure the directory for the alias file exists
    alias_dir = os.path.dirname(alias_path)
    if alias_dir:
        os.makedirs(alias_dir, exist_ok=True)

    print(f"Starting entity resolution for file: {jsonl_path}")
    
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            
            data = json.loads(line)
            entities_to_process = []
            for entity in data.get("entities", []):
                if entity.get("name") and entity.get("type"):
                    entities_to_process.append({"name": entity["name"], "type": entity["type"]})
            
            if not entities_to_process:
                continue

            print(f"\n>>> Processing line {i+1} with {len(entities_to_process)} entities...")
            Normalizer(entities_to_process, alias_path) # Removed base_url

    #create graph ready json

    
    print("\n✅ Entity resolution process completed for all lines.")