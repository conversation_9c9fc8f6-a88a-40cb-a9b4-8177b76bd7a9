2025-08-12 16:54:53,035 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_16-54-53.log
2025-08-12 16:55:03,881 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-12 16:55:03,881 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-12 16:55:03,881 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-12 16:55:03,885 - Finance_Knowledge_Graph - INFO - [pipeline.py:20] - --- Initializing Entity Extraction Pipeline ---
2025-08-12 16:55:03,885 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 16:55:05,549 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-12 16:55:05,549 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 16:55:05,550 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 16:55:20,278 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 16:55:20,281 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 16:55:20,281 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 16:55:20,985 - Finance_Knowledge_Graph - INFO - [normalization_service.py:20] - Initializing NormalizationService...
2025-08-12 16:55:20,985 - Finance_Knowledge_Graph - INFO - [normalization_service.py:30] - Loading canonical maps from C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:55:20,985 - Finance_Knowledge_Graph - INFO - [normalization_service.py:24] - NormalizationService initialized successfully.
2025-08-12 16:55:20,985 - Finance_Knowledge_Graph - INFO - [pipeline.py:35] - --- Pipeline Initialized Successfully ---
2025-08-12 16:55:20,992 - Finance_Knowledge_Graph - INFO - [pipeline.py:51] - --- Starting Pipeline Run for 1 companies ---
2025-08-12 16:55:20,992 - Finance_Knowledge_Graph - INFO - [pipeline.py:55] - --- Processing Company: NVIDIA CORP ---
2025-08-12 16:55:20,992 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 16:56:02,668 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-12 16:56:02,668 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 3 chunks to OpenAI for extraction. Total context length: 13657
2025-08-12 16:56:40,027 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:92] - Successfully extracted 19 relationships from the provided context.
2025-08-12 16:56:40,027 - Finance_Knowledge_Graph - INFO - [pipeline.py:73] - Extracted 19 raw relationships for NVIDIA CORP.
2025-08-12 16:56:40,035 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'Broadcom Inc.' (Score: 100.0). Matched to 'company_avgo'. Adding as new alias.
2025-08-12 16:56:40,038 - Finance_Knowledge_Graph - INFO - [normalization_service.py:109] - Fuzzy match found for 'Qualcomm Incorporated' (Score: 100.0). Matched to 'company_qcom'. Adding as new alias.
2025-08-12 16:56:40,039 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Renesas Electronics Corporation' of type 'Company' with ID 'company_renesas_electronics_'.
2025-08-12 16:56:40,044 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Samsung' of type 'Company' with ID 'company_samsung'.
2025-08-12 16:56:40,046 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Arista Networks' of type 'Company' with ID 'company_arista_networks'.
2025-08-12 16:56:40,048 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Cisco Systems, Inc.' of type 'Company' with ID 'company_cisco_systems,_inc'.
2025-08-12 16:56:40,055 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Hewlett Packard Enterprise Company' of type 'Company' with ID 'company_hewlett_packard_ente'.
2025-08-12 16:56:40,057 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Lumentum Holdings' of type 'Company' with ID 'company_lumentum_holdings'.
2025-08-12 16:56:40,062 - Finance_Knowledge_Graph - INFO - [normalization_service.py:67] - Created new canonical entry for 'Marvell Technology Group' of type 'Company' with ID 'company_marvell_technology_g'.
2025-08-12 16:56:40,066 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'automotive OEMs' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:56:40,072 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'tier-1 suppliers' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:56:40,075 - Finance_Knowledge_Graph - WARNING - [normalization_service.py:82] - Entity 'start-ups' has type 'Other'. Skipping normalization and logging for review.
2025-08-12 16:56:40,079 - Finance_Knowledge_Graph - INFO - [pipeline.py:124] - --- Pipeline Run Finished ---
2025-08-12 16:56:40,080 - Finance_Knowledge_Graph - INFO - [pipeline.py:125] - Total clean relationships found and saved: 19
2025-08-12 16:56:40,082 - Finance_Knowledge_Graph - INFO - [pipeline.py:126] - Saving updated canonical maps to disk...
2025-08-12 16:56:40,083 - Finance_Knowledge_Graph - INFO - [normalization_service.py:42] - Saving updated canonical maps to C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\canonical_maps.json
2025-08-12 16:56:40,086 - Finance_Knowledge_Graph - INFO - [pipeline.py:128] - Canonical maps saved successfully.
2025-08-12 16:56:40,087 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-12 16:56:40,087 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-12 16:56:40,087 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
