2025-08-17 13:47:42,969 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-47-42.log
2025-08-17 13:47:58,207 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:47:58,208 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:47:58,209 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:47:58,210 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:47:58,211 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:48:00,378 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:48:00,379 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:48:00,380 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:48:27,974 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:48:27,975 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:48:27,975 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:48:28,813 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:48:28,813 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 13:48:28,814 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: NVIDIA CORP ---
2025-08-17 13:48:28,815 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-17 13:49:26,676 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-17 13:49:26,676 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 46 for NVIDIA CORP (ID: a3a20ae7-aa9d-4cfd-9a91-a36d6e5c7562)
2025-08-17 13:49:26,678 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3582
2025-08-17 13:49:28,316 - Finance_Knowledge_Graph - ERROR - [llm_extractor.py:100] - An error occurred during OpenAI API call: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\llm_extractor.py", line 76, in extract_relationships
    response = self.client.responses.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_utils\_utils.py", line 279, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\resources\responses\responses.py", line 603, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1242, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 919, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1023, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
2025-08-17 13:49:28,331 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 2 of 46 for NVIDIA CORP (ID: de0f523e-f6ac-4a0b-8469-5839492b46e1)
2025-08-17 13:49:28,331 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 3601
2025-08-17 13:49:29,433 - Finance_Knowledge_Graph - ERROR - [llm_extractor.py:100] - An error occurred during OpenAI API call: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\llm_extractor.py", line 76, in extract_relationships
    response = self.client.responses.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_utils\_utils.py", line 279, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\resources\responses\responses.py", line 603, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1242, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 919, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1023, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
2025-08-17 13:49:29,436 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 3 of 46 for NVIDIA CORP (ID: dbe4fae5-62f2-4182-83f8-65f73161e70f)
2025-08-17 13:49:29,437 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 6474
2025-08-17 13:49:31,093 - Finance_Knowledge_Graph - ERROR - [llm_extractor.py:100] - An error occurred during OpenAI API call: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\llm_extractor.py", line 76, in extract_relationships
    response = self.client.responses.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_utils\_utils.py", line 279, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\resources\responses\responses.py", line 603, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1242, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 919, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1023, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "Invalid schema for response_format 'entity_and_relationship_extraction': In context=(), 'required' is required to be supplied and to be an array including every key in properties. Missing 'entities'.", 'type': 'invalid_request_error', 'param': 'text.format.schema', 'code': 'invalid_json_schema'}}
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [pipeline.py:79] - --- Pipeline Run Finished ---
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [pipeline.py:80] - Total entities extracted: 0
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [pipeline.py:81] - Total relationships extracted: 0
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-17 13:49:31,097 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
