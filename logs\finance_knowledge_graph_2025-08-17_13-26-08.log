2025-08-17 13:26:08,923 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-26-08.log
2025-08-17 13:26:18,735 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:26:18,735 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:26:18,735 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:26:18,742 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:26:18,742 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:26:20,515 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:26:20,517 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:26:20,517 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:26:36,243 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:26:36,244 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:26:36,244 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:26:37,059 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:26:37,066 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 13:26:37,066 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: INTEL CORP ---
2025-08-17 13:26:37,071 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-17 13:27:27,121 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-17 13:27:27,123 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 35 for INTEL CORP (ID: 9f517c03-550b-4de6-980d-e6f13a2bfb18)
2025-08-17 13:27:27,125 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1009
2025-08-17 13:28:00,873 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 13:28:00,873 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 2 of 35 for INTEL CORP (ID: 34b2071a-0a16-4df5-a507-5ece77b46dd6)
2025-08-17 13:28:00,873 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 2928
