2025-08-17 13:29:43,433 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-17_13-29-43.log
2025-08-17 13:29:52,903 - Finance_Knowledge_Graph - INFO - [main.py:9] - =====================================================
2025-08-17 13:29:52,903 - Finance_Knowledge_Graph - INFO - [main.py:10] -       STARTING ENTITY EXTRACTION PIPELINE      
2025-08-17 13:29:52,903 - Finance_Knowledge_Graph - INFO - [main.py:11] - =====================================================
2025-08-17 13:29:52,915 - Finance_Knowledge_Graph - INFO - [pipeline.py:19] - Initializing Knowledge Graph Pipeline...
2025-08-17 13:29:52,915 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-17 13:29:54,758 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-17 13:29:54,758 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-17 13:29:54,764 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-17 13:30:11,465 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-17 13:30:11,465 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-17 13:30:11,467 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-5-nano
2025-08-17 13:30:12,191 - Finance_Knowledge_Graph - INFO - [pipeline.py:29] - Pipeline initialized successfully.
2025-08-17 13:30:12,192 - Finance_Knowledge_Graph - INFO - [pipeline.py:48] - --- Starting Pipeline Run for 1 companies (Single Chunk Mode) ---
2025-08-17 13:30:12,192 - Finance_Knowledge_Graph - INFO - [pipeline.py:54] - --- Processing company: INTEL CORP ---
2025-08-17 13:30:12,192 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'INTEL CORP'
2025-08-17 13:30:57,550 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 35 unique chunks for 'INTEL CORP'.
2025-08-17 13:30:57,550 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 1 of 35 for INTEL CORP (ID: 9f517c03-550b-4de6-980d-e6f13a2bfb18)
2025-08-17 13:30:57,551 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1009
2025-08-17 13:31:26,237 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 13:31:26,238 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 2 of 35 for INTEL CORP (ID: 34b2071a-0a16-4df5-a507-5ece77b46dd6)
2025-08-17 13:31:26,240 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 2928
2025-08-17 13:32:00,839 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 13:32:00,841 - Finance_Knowledge_Graph - INFO - [pipeline.py:65] - Processing chunk 3 of 35 for INTEL CORP (ID: 9fdc8166-92c6-4b6f-bb91-d19ef2c6b969)
2025-08-17 13:32:00,843 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:74] - Sending 1 chunks to OpenAI for extraction. Total context length: 1378
2025-08-17 13:33:42,472 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:93] - Successfully extracted 0 entities and 0 relationships.
2025-08-17 13:33:42,474 - Finance_Knowledge_Graph - INFO - [pipeline.py:79] - --- Pipeline Run Finished ---
2025-08-17 13:33:42,475 - Finance_Knowledge_Graph - INFO - [pipeline.py:80] - Total entities extracted: 0
2025-08-17 13:33:42,475 - Finance_Knowledge_Graph - INFO - [pipeline.py:81] - Total relationships extracted: 0
2025-08-17 13:33:42,476 - Finance_Knowledge_Graph - INFO - [main.py:26] - =====================================================
2025-08-17 13:33:42,477 - Finance_Knowledge_Graph - INFO - [main.py:27] -       PIPELINE EXECUTION FINISHED                 
2025-08-17 13:33:42,477 - Finance_Knowledge_Graph - INFO - [main.py:28] - =====================================================
