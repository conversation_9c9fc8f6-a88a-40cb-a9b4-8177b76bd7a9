2025-08-12 12:59:32,385 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-12_12-59-32.log
2025-08-12 12:59:38,299 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-12 12:59:40,104 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\../data/search_queries.json
2025-08-12 12:59:40,104 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-12 12:59:40,104 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-12 13:00:01,151 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-12 13:00:01,151 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-12 13:00:01,151 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:24] - Initializing LLMExtractor with OpenAI model: gpt-4.1
2025-08-12 13:00:01,906 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-12 13:00:48,413 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 46 unique chunks for 'NVIDIA CORP'.
2025-08-12 13:00:48,421 - Finance_Knowledge_Graph - INFO - [llm_extractor.py:73] - Sending 46 chunks to OpenAI for extraction. Total context length: 137213
2025-08-12 13:00:50,062 - Finance_Knowledge_Graph - ERROR - [llm_extractor.py:99] - An error occurred during OpenAI API call: Error code: 400 - {'error': {'message': "Unknown parameter: 'text.type'.", 'type': 'invalid_request_error', 'param': 'text.type', 'code': 'unknown_parameter'}}
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\src\llm_extractor.py", line 75, in extract_relationships
    response = self.client.responses.create(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_utils\_utils.py", line 279, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\resources\responses\responses.py", line 603, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1242, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 919, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\envs\autowiz\Lib\site-packages\openai\_base_client.py", line 1023, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': "Unknown parameter: 'text.type'.", 'type': 'invalid_request_error', 'param': 'text.type', 'code': 'unknown_parameter'}}
