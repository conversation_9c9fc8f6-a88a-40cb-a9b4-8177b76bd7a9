2025-08-11 22:22:10,149 - Finance_Knowledge_Graph - INFO - [logger.py:61] - Logger 'Finance_Knowledge_Graph' initialized. Logging to file: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\logs\finance_knowledge_graph_2025-08-11_22-22-10.log
2025-08-11 22:22:10,149 - Finance_Knowledge_Graph - INFO - [data_retriever.py:125] - --- Running DataRetriever Test ---
2025-08-11 22:22:10,152 - Finance_Knowledge_Graph - INFO - [data_retriever.py:26] - Initializing DataRetriever...
2025-08-11 22:22:12,118 - Finance_Knowledge_Graph - INFO - [data_retriever.py:65] - Loading search queries from: C:\Users\<USER>\Desktop\Autowiz\Fin_knowledge_graph\new_approach_11_08\data\search_queries.json
2025-08-11 22:22:12,122 - Finance_Knowledge_Graph - INFO - [data_retriever.py:42] - --- Initializing Embedding Model: BAAI/bge-base-en-v1.5 ---
2025-08-11 22:22:12,126 - Finance_Knowledge_Graph - INFO - [data_retriever.py:46] - Using device: cpu
2025-08-11 22:22:31,218 - Finance_Knowledge_Graph - INFO - [data_retriever.py:52] - Embedding model initialized successfully.
2025-08-11 22:22:31,224 - Finance_Knowledge_Graph - INFO - [data_retriever.py:35] - DataRetriever initialized successfully.
2025-08-11 22:22:31,224 - Finance_Knowledge_Graph - INFO - [data_retriever.py:82] - Starting to fetch chunks for company: 'NVIDIA CORP'
2025-08-11 22:23:14,756 - Finance_Knowledge_Graph - INFO - [data_retriever.py:117] - Fetched a total of 93 unique chunks for 'NVIDIA CORP'.
2025-08-11 22:23:14,756 - Finance_Knowledge_Graph - INFO - [data_retriever.py:144] - Successfully retrieved 93 chunks for testing.
2025-08-11 22:23:14,756 - Finance_Knowledge_Graph - INFO - [data_retriever.py:145] - --- Sample Chunk ---
